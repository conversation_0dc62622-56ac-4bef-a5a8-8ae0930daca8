<div class="row page" data-ng-controller="WarrantySerial">
    <div class="col-md-12">
        <article class="article">
            <form name="material_signup_form" class="form-validation" >
                <md-card class="no-margin-h">
                    
                    <md-toolbar class="md-table-toolbar md-default">
                        <div class="md-toolbar-tools">
                            <span>Warranty Serial</span>
                            <div flex></div>
                             <a href="#!/WarrantySerialList">
                            <md-button class="md-raised btn-w-md md-default"><span class="fa fa-chevron-left"></span> Back to List</md-button>
                        </a>
                        </div>
                    </md-toolbar>
                    <div class="row"> 
                        <div class="col-md-12">        
                            <div class="col-md-3">
                                
                                    <md-input-container class="md-block">
                                        <label>Serial Number</label>
                                        <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                
                                        <input type="text" name="SerialNumber"  ng-model="Fleetrisk['SerialNumber']"  required ng-maxlength="100" />
                                        <div ng-messages="material_signup_form.SerialNumber.$error" multiple ng-if='material_signup_form.SerialNumber.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>                           
                                        </div>
                                    </md-input-container>
                                    <!-- <md-input-container class="md-block">                                            
                                        <label>In Warranty</label>
                                        <md-select name="in_warranty" ng-model="Fleetrisk.in_warranty" required aria-label="select">
                                            <md-option value="Yes"> Yes </md-option>
                                            <md-option value="No"> No </md-option>
                                        </md-select>   
                                        <div ng-messages="material_signup_form.in_warranty.$error" multiple ng-if='material_signup_form.in_warranty.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div>                                             
                                    </md-input-container> -->
                            </div>
                            <div class="col-md-3">

                                    <md-input-container class="md-block">                                            
                                        <label>Rack Asset ID</label>                                                                
                                        <input type="text" name="rack_asset_id"  ng-model="Fleetrisk['rack_asset_id']"  required ng-maxlength="100" />
                                        <div ng-messages="material_signup_form.rack_asset_id.$error" multiple ng-if='material_signup_form.rack_asset_id.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>                           
                                        </div>
                                        
                                    </md-input-container>
                            </div>
                            <div class="col-md-3">
                                    <md-input-container class="md-block">                                            
                                        <label>Host ID</label>                                                            
                                        <input type="text" name="host_id"  ng-model="Fleetrisk['host_id']"  required ng-maxlength="100" />
                                        <div ng-messages="material_signup_form.host_id.$error" multiple ng-if='material_signup_form.host_id.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>                           
                                        </div>
                                    </md-input-container>
                            </div>
                            <div class="col-md-3">
                                    <md-input-container class="md-block">                                            
                                        <label>Host Asset ID</label>                                                                
                                        <input type="text" name="host_asset_id"  ng-model="Fleetrisk['host_asset_id']"  required ng-maxlength="100" />
                                        <div ng-messages="material_signup_form.host_asset_id.$error" multiple ng-if='material_signup_form.host_asset_id.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>                           
                                        </div>
                                        
                                    </md-input-container>
                            </div>
                            <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Warranty Expiration Date</label>
                                        <md-datepicker name="warranty_expiration_date" ng-model="Fleetrisk.warranty_expiration_date" aria-label="Enter date" required  ></md-datepicker>
                                        <div ng-messages="material_signup_form.ApprovedDate.$error" multiple ng-if='material_signup_form.warranty_expiration_date.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="maxdate">Future Date is not allowed</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div> 
                                        </div>
                                    </md-input-container>                            
                            </div>
                            <div class="col-md-12 btns-row"> 
                                    <a href="#!/WarrantySerialList" style="text-decoration: none;">
                                <md-button 
                                class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>          
                                <md-button 
                                class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || Fleetrisk.busy" ng-click="SaveWarrantySerial('Submit')">
                                    <span ng-show="! Fleetrisk.busy">Submit</span>
                                    <span ng-show="Fleetrisk.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>
                        </div>
                    </div>
                        
                    
                </md-card>                
            </form>            
        </article>                            
    </div>
</div>