<div class="row page" data-ng-controller="site">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Work Station</span>
                        <div flex></div>
                            <a href="#!/siteList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="site_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>   
                                    <md-select name="FacilityID" ng-model="site.FacilityID" required aria-label="select" ng-change="GetAllRecoveryTypes(site.FacilityID)">
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                    
                                    </md-select>
                                    <div class="error-space"> 
                                    <div ng-messages="site_form.FacilityID.$error" multiple ng-if='site_form.FacilityID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>     
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Work Station Name</label>
                                    <input type="text" name="Site"  ng-model="site['Site']"  required ng-maxlength="45" />
                                    <div class="error-space"> 
                                    <div ng-messages="site_form.Site.$error" multiple ng-if='site_form.Site.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Work Station Description</label>
                                    <input type="text" name="Description"  ng-model="site['Description']"  required ng-maxlength="250" />
                                    <div class="error-space"> 
                                    <div ng-messages="site_form.Description.$error" multiple ng-if='site_form.Description.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Workflow</label>   
                                    <md-select name="workflow_id" ng-model="site.workflow_id" required aria-label="select">
                                        <md-option ng-repeat="flow in workflows" value="{{flow.workflow_id}}"> {{flow.workflow}} </md-option>
                                    </md-select>
                                    <div class="error-space"> 
                                    <div ng-messages="site_form.FacilityID.$error" multiple ng-if='site_form.FacilityID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>    
                                    </div> 
                                </md-input-container>
                            </div>

                            <div class="col-md-3" >                                
                                <div class="autocomplete insideuse" >
                                    <md-input-container class="md-block">                                    
                                        <md-autocomplete flex  required style="margin-bottom:0px !important; margin-top:0px !important; padding-top: 0px !important;"
                                            md-input-name="group"                                        
                                            md-input-maxlength="100" 
                                            ng-disabled="site.FacilityID == 0 || !site.FacilityID"
                                            md-no-cache="noCache"    
                                            md-search-text-change="LocationChange(site.group)"      
                                            md-search-text="site.group"                                  
                                            md-items="item in queryLocationSearch(site.group)"
                                            md-item-text="item.GroupName"
                                            md-selected-item-change="selectedLocationChange(item)"
                                            ng-model-options='{ debounce: 1000 }'
                                            md-min-length="0"
                                            placeholder="Search Location Group">
                                            <md-item-template>
                                                <span md-highlight-text="site.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                            </md-item-template>
                                            <md-not-found>
                                                No Records matching "{{site.group}}" were found.                                    
                                            </md-not-found>
                                            <div ng-messages="site_form.group.$error" ng-if="site_form.group.$touched">
                                                <div ng-message="required">No Records matching.</div>                                            
                                            </div>
                                        </md-autocomplete>
                                    </md-input-container>
                                    
                                </div>                                            
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Recovery Type</label>

                                    <md-select name="Recoverytypeid" ng-model="site.Recoverytypeid" aria-label="select">
                                        <md-option value="" ng-selected=true> Choose </md-option>
                                        <md-option ng-repeat="Recovery in Recoverytype" value="{{Recovery.Recoverytypeid}}"> {{Recovery.Recoverytype}}
                                        </md-option>
                                    </md-select>                                                                 
                                </md-input-container>
                            </div>

                           
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="site.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In active </md-option>
                                    </md-select>  
                                    <div class="error-space">  
                                    <div ng-messages="site_form.Status.$error" multiple ng-if='site_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>   
                                    </div>                                          
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/siteList" style="text-decoration: none;">
                                    <md-button class="md-button md-raised btn-w-md  md-default">
                                        Cancel
                                    </md-button>
                                </a>
                                
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="site_form.$invalid || site.busy" ng-click="saveLocation()">
                                <span ng-show="! site.busy">Save</span>
                                <span ng-show="site.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>



            <md-card class="no-margin-h" ng-show="site.SiteID">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Mapped Dispositions</span>
                        <div flex></div>
                        <div class="upload-btn-wrapper text-center mt-5">
                            <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                            <input type="file" ng-file-select="onFileSelect($files)" id="SiteDispositionFile">  
                            <a href="../../sample_files/upload_WorkStationDisposition_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                        </div> 
                    </div>
                </md-toolbar>

                <div class="row">                               
                    <form>                            
                        <div class="col-md-12">                        
                             
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Disposition</label>
                                    <md-select name="disposition_id" ng-model="site.disposition_id" required aria-label="select">
                                        <md-option ng-repeat="dis_position in Dispositions" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                    </md-select>                                                                 
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px;" ng-disabled="!site.disposition_id" ng-click="AddDispositionToStation($event)">Add</button>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-12">
                                    <table md-table md-row-select  ng-show="MappedDispositions.length > 0">
                                        <thead md-head>
                                            <tr md-row>
                                                <th md-column>Disposition</th>
                                                <th md-column>Delete</th>
                                            </tr>
                                        </thead>
                                        <tbody md-body>
                                            <tr md-row ng-repeat="disp in MappedDispositions">
                                                <td md-cell>{{disp.disposition}} <span style="color:red;" ng-show="disp.sub_disposition > 0">(Sub Disposition)</span></td>                                            
                                                <td md-cell>
                                                    <span class="text-danger actionlinkicon" ng-click="DeleteMappedDisposition(disp,$index,$event)" role="button" tabindex="0"><i class="material-icons">delete_forever</i> Delete  </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>  
                            </div>
                        </div>
                    </form>
                </div>
            </md-card>



            <md-card class="no-margin-h" ng-show="site.SiteID">
                    
                <md-toolbar class="md-table-toolbar md-default"  ng-init="attributesPanel = true;" >
                    <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="attributesPanel = !attributesPanel">                            
                        <i class="material-icons" ng-click="attributesPanel = !attributesPanel" ng-show="attributesPanel">keyboard_arrow_up</i>
                        <i class="material-icons" ng-click="attributesPanel = !attributesPanel" ng-show="! attributesPanel">keyboard_arrow_down</i>
                        <span>Mapped BINS</span>
                    </div>
                </md-toolbar>
                <div class="row" ng-show="attributesPanel">                                            
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <table md-table md-row-select  ng-show="mapping.length > 0">
                                <thead md-head>
                                    <tr md-row>
                                        <th md-column>Disposition</th>
                                        <th md-column>BIN Name</th>
                                        <th md-column style="padding-left: 5px;">Action</th>
                                    </tr>
                                </thead>
                                <tbody md-body>
                                    <tr md-row ng-repeat="mr in mapping">
                                        <td md-cell>{{mr.disposition}}</td>
                                        <td md-cell>{{mr.BinName}}</td>
                                        <td md-cell>
                                            <span class="text-danger actionlinkicon" ng-click="DeleteMappedBIN(mr,$index,$event)"><i class="material-icons">delete_forever</i> Delete  </span>                                  
                                        </td>
                                    </tr>
                                </tbody>
                            </table>  

                            <div class="alert alert-warning" role="alert"  ng-show="mapping.length == 0 && !loading">
                                <i class="material-icons">warning</i> No BINS Associated.                                
                            </div>

                        </div>
                    </div>                    
                </div>

            </md-card>
        </article> 
    </div>
</div>