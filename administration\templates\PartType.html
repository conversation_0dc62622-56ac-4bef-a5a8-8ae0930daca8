<div class="row page" data-ng-controller="PartType">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>PartType</span>
                        <div flex></div>
                            <a href="#!/PartTypeList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="parttype.FacilityID" required ng-disabled="true">
                                        <md-option ng-repeat="facility in Facility | filter:{ FacilityStatus : '!InActive'}" value="{{facility.FacilityID}}"> {{facility.FacilityName}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>PartType</label>
                                    <input type="text" name="parttype"  ng-model="parttype['parttype']"  required ng-maxlength="45" />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.parttype.$error" multiple ng-if='material_signup_form.parttype.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 45.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Serialized</label>
                                    <md-select name="serialized" ng-model="parttype.serialized " required aria-label="select" ng-disabled="parttype.parttypeid">
                                        <md-option value="Yes"> Yes </md-option>
                                        <md-option value="No"> No </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.serialized .$error" multiple ng-if='material_signup_form.serialized.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-8">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="Description"  ng-model="parttype['Description']"  required ng-maxlength="250" />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.Description.$error" multiple ng-if='material_signup_form.Description.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 250.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="parttype.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In active </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="parttype.EligibleForExceptionRecovery" aria-label="EligibleForExceptionRecovery" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Eligible For Unknown Recovery </md-checkbox>
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/PartTypeList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="parttypeSave()">
                                <span ng-show="! parttype.busy">Save</span>
                                <span ng-show="parttype.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>
         <md-card class="no-margin-h" ng-show="parttype.parttypeid">
                <div class="row">
                    <form>
                        <div class="col-md-12">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>COO</label>
                                    <md-select name="COOID" ng-model="parttype.COOID" required aria-label="select">
                                        <md-option ng-repeat="coo in coos" value="{{coo.COOID}}"> {{coo.COO}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px;" ng-disabled="!parttype.COOID" ng-click="AddParttypecoomapping()">Add</button>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-12">
                                <div class="table-responsive" style="overflow: auto;">
                                    <table md-table md-row-select  ng-show="parttypecoo.length > 0">
                                        <thead md-head>
                                            <tr md-row>
                                                <th md-column>COO </th>
                                                <th md-column>Default Value</th>
                                                <th md-column style="padding-left: 5px;">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody md-body>
                                            <tr md-row ng-repeat="parttype in parttypecoo">
                                                <td md-cell>{{parttype.COO}}</td>
                                                <td md-cell><md-switch ng-model="parttype.default" ng-change="UpdateDefault(parttype.parttypecoo_id,parttype.default,parttype)">Access</md-switch></td>
                                                <td md-cell>
                                                    <md-switch ng-model="parttype.status" aria-label="default" class="md-default" ng-true-value="'Active'" ng-false-value="'Inactive'" ng-change="ChangeparttypecooStatus(parttype,$event)" ng-show="!parttype.loading" ng-disabled="parttype.default">{{parttype.status}}</md-switch>
                                                    <i class="fa fa-spinner" style="font-size: 16px;" aria-hidden="true" ng-show="parttype.loading"></i>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </md-card>
    </div>
</div>
