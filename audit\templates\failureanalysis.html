<div class="page" data-ng-controller="failure_analysis">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">

                <style>
                    .md-virtual-repeat-container.md-autocomplete-suggestions-container{z-index:10000; margin-top: 12px;}

                    /* Actions Dropdown Styling */
                    .actionicons .md-button {
                        min-width: auto;
                        margin: 0;
                        padding: 8px 12px;
                        line-height: 1;
                        border-radius: 4px;
                        background-color: #f5f5f5;
                        border: 1px solid #ddd;
                    }

                    .actionicons .dropdown-text {
                        font-size: 14px;
                        color: #333;
                        margin-right: 4px;
                    }

                    .actionicons .material-icons {
                        font-size: 18px;
                        color: #666;
                    }

                    .actionicons .md-button:hover {
                        background-color: #e9e9e9;
                    }

                    /* Action Menu Items */
                    .create-action {
                        color: #4CAF50 !important;
                    }

                    .move-action {
                        color: #2196F3 !important;
                    }

                    .close-action {
                        color: #FF9800 !important;
                    }

                    .consolidate-action {
                        color: #9C27B0 !important;
                    }

                    .nest-action {
                        color: #607D8B !important;
                    }

                    /* Disabled state */
                    md-menu-item .md-button[disabled] {
                        color: #ccc !important;
                        cursor: not-allowed;
                    }
                </style>

                <script type="text/ng-template" id="password.html">

                    <div style="max-width:900px">

                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>Move & Scan New BIN</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">

                                <div class="form-horizontal verification-form">                                
                                    <form name="tpvForm">      
                                        <!-- <div>{{CurrentPallet}}</div> -->
                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Current Bin Location</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.LocationName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Location Label</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.lable_name}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Location Type</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.LocationType}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-8">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Current Bin</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.BinName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        

                                        <!-- <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Move Current Bin to New Location</label>
                                                <input required name="NewLocationGroup" id="AuditController" ng-model="confirmDetails.NewLocationGroup" ng-maxlength="200" type="text" autocomplete="off" ng-enter="MoveBinToNewLocationGroup($event)">
                                                <div ng-messages="tpvForm.NewLocationGroup.$error" multiple ng-if='tpvForm.NewLocationGroup.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div> -->

                                        <div class="col-md-4" >                                        
                                            <div class="autocomplete" style="padding:0px;">
                                                <md-autocomplete flex id="AuditController" style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                    md-input-name="group"
                                                    md-input-maxlength="100"                                                    
                                                    md-no-cache="noCache"
                                                    md-search-text-change="LocationChange1(confirmDetails.group)"
                                                    md-search-text="confirmDetails.group"
                                                    md-items="item in queryLocationSearch1(confirmDetails.group)"
                                                    md-item-text="item.GroupName"
                                                    md-selected-item-change="selectedLocationChange1(item)"
                                                    md-min-length="0"
                                                    ng-model-options='{ debounce: 1000 }'
                                                    md-escape-options="clear"
                                                    md-floating-label="Outbound Location Group"
                                                    >
                                                    <md-item-template>
                                                        <span md-highlight-text="confirmDetails.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                    </md-item-template>
                                                    <md-not-found>
                                                        No Records matching "{{confirmDetails.group}}" were found.
                                                    </md-not-found>
                                                    <div ng-messages="tpvForm.group.$error" ng-if="tpvForm.group.$touched">
                                                        <div ng-message="required">No Records matching.</div>
                                                        <div ng-message="minlength">Min length 2.</div>
                                                        <div ng-message="maxlength">Max length 100.</div>
                                                    </div>
                                                </md-autocomplete>
                                            </div>
                                        </div>


                                                                                                                
                                    </form>
                                </div>                        
                            
                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                    <!-- <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                    <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="MoveBinToNewLocationGroup($event)" ng-disabled="tpvForm.$invalid">Continue</button>
                                </div>
                        <md-dialog-content>
                    </div>
                </script>



                <script type="text/ng-template" id="password4.html">

                    <div style="max-width:900px">

                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>Consolidate BIN</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">

                                <div class="form-horizontal verification-form">                                
                                    <form name="tpvForm">      
                                        <!-- <div>{{CurrentPallet}}</div> -->
                                        <div class="col-md-12">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>From Bin</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentCustomPalletPallet.BinName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>  
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>To BIN</label>
                                                <input required name="ToBinName" id="newbin" ng-model="confirmDetails4.ToBinName" ng-maxlength="200" type="text" autocomplete="off" ng-change="confirmDetails4.ToShipmentContainer = 'n/a'">
                                                <div ng-messages="tpvForm.ToBinName.$error" multiple ng-if='tpvForm.ToBinName.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                        
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>To Shipment Container</label>
                                                <input required name="ToShipmentContainer" id="ToShipmentContainer" ng-model="confirmDetails4.ToShipmentContainer" ng-maxlength="200" type="text" autocomplete="off" ng-init="confirmDetails4.ToShipmentContainer = 'n/a'" ng-change="confirmDetails4.ToBinName = 'n/a'">
                                                <div ng-messages="tpvForm.ToShipmentContainer.$error" multiple ng-if='tpvForm.ToShipmentContainer.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                    </form>
                                </div>                        
                            
                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                    <!-- <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                    <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="ConsolidateBin($event)" ng-disabled="tpvForm.$invalid">Consolidate</button>
                                </div>
                        <md-dialog-content>
                    </div>
                </script>

                <!--Alert Start-->
                <!-- <div class="alert alert-info" role="alert" ng-init="showAlert3=true;" ng-show="showAlert3">
                    <i class="material-icons">info</i> <strong class="mr-5">Note!</strong> Please read the comments carefully.
                    <i class="material-icons alert-close" ng-click="showAlert3 = ! showAlert3">close</i>
                </div> -->
                <!--Alert End-->

                <!--Station info Start-->
                <md-card class="no-margin-h pt-0">

                    <md-toolbar class="md-table-toolbar md-default" ng-init="Stationblock = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="Stationblock = !Stationblock">
                            <i class="material-icons md-primary" ng-show="Stationblock">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! Stationblock">keyboard_arrow_down</i>
                            <span>Station Information</span>
                        </div>
                    </md-toolbar>

                    <div class="row" ng-show="Stationblock">

                        <div class="col-md-12">
                            <div class="bg-grey-light">
                                <div class="col-md-4 col-md-offset-4">
                                    <md-input-container class="md-block">
                                        <label>Station</label>
                                        <md-select name="SiteID" ng-model="SiteID" required ng-change="GetStationCustomPallets();GetStationRigs();GetCurrentTime(asset,'workstation_scan_time');CallServerFunction(0)">
                                            <md-option value="{{station.SiteID}}" ng-repeat="station in Stations">{{station.SiteName}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>
                                <!-- <div class="col-md-4 errornone">
                                    <md-input-container class="md-block includedsearch">
                                        <label>Source Bin ID</label>
                                        <input required name="BinName" ng-model="BinName" required ng-enter="GetCustomPalletDetails()">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="GetCustomPalletDetails()" ng-disabled="!BinName" style="bottom: 6px;">
                                            Go
                                        </md-button>
                                    </md-input-container>
                                </div> -->
                                <div style="clear: both;"></div>
                            </div>
                        </div>

                        <div class="col-md-12" ng-show="StationCustomPallets.length > 0">
                            <md-card-content class="pt-0">
                                <md-table-container>
                                    <table class="md-table mb-0">
                                        <thead>
                                            <tr>
                                                <th style="width: 240px;">Disposition</th>
                                                <th>Bin ID</th>
                                                <th style="width: 160px;" class="text-center">Current Count</th>
                                                <th style="width: 160px;" class="text-center">Move Bin</th>
                                                <th class="text-center">Consolidate</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="disp in StationCustomPallets">
                                                <td>
                                                    {{disp.disposition}}
                                                </td>
                                                <td>
                                                    <md-input-container class="md-block md-no-float includedsearch tdinput">
                                                        <input required name="BinName" ng-model="disp.BinName" required ng-enter="MapCustomPalletToDisposition(disp)" ng-disabled="disp.CustomPalletID">
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="MapCustomPalletToDisposition(disp)" ng-disabled="!disp.BinName || disp.CustomPalletID">
                                                            Go
                                                        </md-button>
                                                    </md-input-container>
                                                </td>
                                                <td class="text-center">{{disp.AssetsCount}}</td>
                                                <td class="text-center" style="cursor: pointer;" ng-click="MoveBinToStationLocationGroup(disp,SiteID,$event)"><i class="material-icons text-success">swap_horiz</i></td>
                                                <td class="text-center" style="cursor: pointer;" ng-click="ConsolidateBin(disp,SiteID,$event)"><i class="material-icons text-success">check_circle</i></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </md-table-container>
                            </md-card-content>
                        </div>


                    </div>

                </md-card>
                <!--Station info Close-->

                <!-- <md-card class="no-margin-h" ng-show="CustomPalletID > 0"> -->
                <md-card class="no-margin-h" ng-show="SiteID > 0">
                    <md-toolbar class="md-table-toolbar md-default"ng-init="SanitizationPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="SanitizationPanel = !SanitizationPanel">
                            <i class="material-icons md-primary" ng-show="SanitizationPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! SanitizationPanel">keyboard_arrow_down</i>
                            <span>Failure Analysis</span>
                        </div>
                    </md-toolbar>
                    <div class="row" ng-show="SanitizationPanel">
                        <form name="audit_form" class="form-validation">
                            <div class="col-md-12">
                                <div class="col-md-12 bg-grey-light pt-0 pb-0" style="margin-bottom: 1px;">
                                    <div class="col-md-4 col-md-offset-4 errornone">
                                        <md-input-container class="md-block">
                                            <label>Test Rig ID</label>
                                            <md-select name="RigID" ng-model="asset.RigID" id="select_rigid" required ng-change="GetRigLimit();GetCurrentTime(asset,'rig_scan_time')" style="margin-bottom: 1px !important;" data-ng-disabled="canDisableRig()">
                                                <md-option value="{{rig.RigID}}" ng-repeat="rig in Rigs">{{rig.Rigname}}</md-option>
                                            </md-select>
                                            <!--
                                            <div class="error-sapce">
                                                <div ng-messages="audit_form.RigID.$error" multiple ng-if='audit_form.RigID.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                </div>
                                            </div>
                                        -->
                                        </md-input-container>
                                    </div>

                                    <!-- <div class="col-md-4">
                                        <md-input-container class="md-block">
                                            <label>Test Result</label>
                                            <md-select name="fa_input_id" ng-model="asset.fa_input_id" required ng-change="ApplyBusinessRule()">
                                                <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}} <span style="color:red;">({{ir.input_type}})</span> </md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                                <div ng-messages="audit_form.fa_input_id.$error" multiple ng-if='audit_form.fa_input_id.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div> -->
                                    <div style="clear: both;"></div>
                                </div>
                            </div>


                            <!-- <div class="col-md-12">
                                <div class="col-md-3">
                                    <md-input-container class="md-block includedsearch">
                                        <label>SN</label>
                                        <input required name="SerialNumber" ng-model="asset.SerialNumber" required ng-maxlength="100" ng-minlength="3" ng-change="SerialChanged()" ng-enter="GetMPNFromSerialFA()" id="SerialNumber">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!asset.SerialNumber" ng-click="GetMPNFromSerialFA()">
                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                        </md-button>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.SerialNumber.$error" multiple ng-if='audit_form.SerialNumber.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>MPN</label>
                                        <input required name="UniversalModelNumber" ng-model="asset.UniversalModelNumber" required ng-maxlength="100" ng-minlength="3" ng-change="MPNChanged()" ng-enter="ApplyBusinessRule()">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!asset.UniversalModelNumber" ng-click="ApplyBusinessRule()">
                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                        </md-button>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.UniversalModelNumber.$error" multiple ng-if='audit_form.UniversalModelNumber.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Custom ID</label>
                                        <input required name="fa_custom_id" ng-model="asset.fa_custom_id" ng-maxlength="100" />
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.fa_custom_id.$error" multiple ng-if='audit_form.fa_custom_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Failure Analysis Notes</label>
                                        <input required name="fa_notes" ng-model="asset.fa_notes" ng-maxlength="100" />
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.fa_notes.$error" multiple ng-if='audit_form.fa_notes.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-6">
                                    <md-input-container class="md-block">
                                        <label>Disposition</label>
                                        <input name="disposition" disabled ng-model="asset.disposition" required style="background-color: {{disposition_color}};">
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.disposition.$error" multiple ng-if='audit_form.disposition.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-6">
                                    <md-input-container class="md-block">
                                        <label>Bin ID</label>
                                        <input name="BinName"  disabled ng-model="asset.BinName" required>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.BinName.$error" multiple ng-if='audit_form.BinName.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <dl class="dl-horizontal" ng-show="asset.rule_description != ''">
                                    <dt>Rule Description</dt>
                                    <dd>{{asset.rule_description}}</dd>
                                </dl>

                                <dl class="dl-horizontal" ng-show="asset.rule_id_text != '' && asset.rule_id_text">
                                    <dt>Rule ID</dt>
                                    <dd>{{asset.rule_id_text}}</dd>
                                </dl>

                            </div> -->

                            <!-- {{Records}} -->
                            <div class="col-md-12" ng-show="asset.RigID">
                                <div class="table-responsive" style="overflow: auto;">
                                    <md-card-content class="bg-grey-light pt-0 pb-0">
                                        <md-table-container>                                            
                                            <table class="table mb-0">
                                                <thead>
                                                    <tr class="bg-grey">
                                                        <th style="min-width: 200px;">Test Result</th>
                                                        <th style="min-width: 160px;">SN</th>
                                                        <th style="min-width: 144px;">MPN</th>
                                                        <th>Part Type</th>
                                                        <th>COO</th>
                                                        <!--<th>Failure Analysis Notes</th>-->
                                                        <!-- <th style="width: 60px;">Notes</th> -->
                                                        <th style="min-width: 230px;">Disposition</th>
                                                        <th style="min-width:360px;">Bin ID</th>
                                                        <th style="min-width: 130px;">Rule</th>
                                                        <th><i id="add_button" class="material-icons text-success" style="cursor: pointer; font-size: 24px;" ng-click="AddFaAsset()"> add_box</i></th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    <tr ng-repeat="asset in Records">

                                                        <td style="margin-top: 7px;">
                                                            <md-input-container  md-no-float class="md-block tdinput" style="min-width: 200px;">
                                                                <md-select name="fa_input_id" ng-model="asset.fa_input_id" ng-required="$index > 0" ng-change="ApplyBusinessRule(asset);GetCurrentTime(asset,'result_scan_time')" style="margin-bottom: 0px;">
                                                                    <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}} <span style="color:red;">({{ir.input_type}})</span> </md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input required name="SerialNumber" ng-model="asset.SerialNumber" ng-required="$index > 0"  ng-maxlength="100" ng-minlength="3" ng-change="SerialChanged(asset)" ng-enter="GetCurrentTime(asset,'serial_scan_time');GetMPNFromSerialFA(asset,$index)" id="SerialNumber{{$index}}">
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!asset.SerialNumber" ng-click="GetCurrentTime(asset,'serial_scan_time');GetMPNFromSerialFA(asset,$index)">
                                                                    <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                                </md-button>
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input required name="UniversalModelNumber" ng-model="asset.UniversalModelNumber" ng-required="$index > 0"  ng-maxlength="100" ng-minlength="3" ng-change="MPNChanged(asset)" ng-enter="GetCurrentTime(asset,'mpn_scan_time');ApplyBusinessRule(asset)">
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!asset.UniversalModelNumber" ng-click="GetCurrentTime(asset,'mpn_scan_time');ApplyBusinessRule(asset)">
                                                                    <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                                </md-button>
                                                            </md-input-container>
                                                        </td>

                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput" >
                                                                <md-select name="parttypeid" ng-model="asset.parttypeid" ng-required="$index > 0" ng-change="ApplyBusinessRule(asset);GetCurrentTime(asset,'part_type_scan_time')" style="margin-bottom: 0px;" id="parttypeid{{$index}}">
                                                                    <md-option value="{{ir.parttypeid}}" ng-repeat="ir in PartTypes">{{ir.parttype}}</md-option>
                                                                </md-select>
                                                            </md-input-container>

                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput" >
                                                                <md-select name="COOID" ng-model="asset.COOID" ng-required="$index > 0" ng-change="ApplyBusinessRule(asset);GetCurrentTime(asset,'coo_scan_time')" style="margin-bottom: 0px;" id="COOID{{$index}}">
                                                                    <md-option value="{{ir.COOID}}" ng-repeat="ir in COOList">{{ir.COO}}</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <!-- <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input ng-required="$index > 0"  name="fa_notes" ng-model="asset.fa_notes" ng-maxlength="100" ng-enter="FocusAddMore()" />
                                                            </md-input-container>
                                                        </td> -->
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input name="disposition" disabled ng-model="asset.disposition" ng-required="$index > 0"  style="background-color: {{asset.disposition_color}};">
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input name="BinName"  disabled ng-model="asset.BinName" ng-required="$index > 0" >
                                                            </md-input-container>
                                                        </td>
                                                        <td style="display: flex; padding-top: 7px;">

                                                            <span ng-show="asset.rule_description">
                                                                <!-- <md-icon class="material-icons">insert_drive_file</md-icon> -->
                                                                <span style="cursor: pointer;">{{asset.rule_id_text}}</span>
                                                                <md-tooltip md-visible="show" md-direction="top">
                                                                    {{asset.rule_description}}
                                                                </md-tooltip>
                                                            </span>
                                                            <!-- <md-input-container  md-no-float class="md-block tdinput">
                                                                <input name="asset.rule_id_text"  disabled ng-model="asset.rule_id_text" required>
                                                            </md-input-container> -->
                                                        </td>
                                                        <!-- <td><i class="material-icons text-danger" style="cursor: pointer; margin-top: 8px;" ng-show="$index != {{Records.length}} - 1" ng-click="RemoveAsset($event,$index)"> close</i></td> -->
                                                        <td><i class="material-icons text-danger" style="cursor: pointer; margin-top: 8px;" ng-click="RemoveAsset($event,$index)"> close</i></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </md-table-container>
                                    </md-card-content>
                                </div>
                            </div>



                            <div class="col-md-12">

                                <!-- <div class="col-md-3">
                                    <md-input-container class="md-block mt-5" >
                                        <md-checkbox ng-model="CopyCustomID"> Reuse Custom ID </md-checkbox>
                                    </md-input-container>
                                    <div class="error-sapce"></div>
                                </div> -->

                                <!-- <div class="col-md-3 text-left">
                                    <md-input-container class="md-block mt-5" >
                                        <md-checkbox ng-model="asset.CloseBin"> <strong class="text-warning">Empty Bin</strong> </md-checkbox>
                                    </md-input-container>
                                </div> -->

                                <div class="col-md-6" ng-if="asset.CloseBin">
                                    <md-input-container class="md-block">
                                        <label>All DispositionBin ID</label>
                                        <input name="AllDispositionBINName" ng-model="asset.AllDispositionBINName" required>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.AllDispositionBINName.$error" multiple ng-if='audit_form.AllDispositionBINName.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3" style="opacity:0;">
                                    <md-input-container class="md-block includedsearch">
                                        <label>Scan for Save</label>
                                        <input name="scan_for_save" ng-model="scan_for_save" ng-enter="UpdateAssetFailureAnalysis($event)" id="scan_for_save" style="width:2px;">
                                    </md-input-container>
                                </div>

                            </div>

                            <div class="col-md-12 btns-row mt-0">
                                <div class="col-md-4 col-md-offset-4">
                                    <button class="md-button md-raised btn-w-md  md-default">
                                        Cancel
                                    </button>
                                    <md-button id="main_save" class="md-raised btn-w-md md-primary btn-w-md"
                                        data-ng-disabled="audit_form.$invalid || asset.busy" ng-click="UpdateAssetFailureAnalysis($event)">
                                        <span ng-show="! asset.busy">Save</span>
                                        <span ng-show="asset.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </div>
                            </div>

                        </form>

                    </div>


                </md-card>

                <!--List Start-->
                <md-card class="no-margin-h" ng-show="Assets.length > 0">

                     <md-toolbar class="md-table-toolbar md-default" ng-init="AssetsSanizedPanel = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                 <i ng-click="AssetsSanizedPanel = !AssetsSanizedPanel" class="material-icons md-primary" ng-show="AssetsSanizedPanel">keyboard_arrow_up</i>
                                <i ng-click="AssetsSanizedPanel = !AssetsSanizedPanel" class="material-icons md-primary" ng-show="! AssetsSanizedPanel">keyboard_arrow_down</i>
                                <span ng-click="AssetsSanizedPanel = !AssetsSanizedPanel">Failure Analysis Records</span>
                                <div flex></div>
                            </div>
                        </md-toolbar>
                    <div class="row"  ng-show="AssetsSanizedPanel">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div ng-show="Assets" class="pull-right">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>
                                    <div class="table-responsive" style="overflow: auto;">                                        

                                        <table class="table table-striped mb-0">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Print</th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FromBinName')" ng-class="{'orderby' : OrderBy == 'FromBinName'}">
                                                        <div style="min-width: 200px;">
                                                            Source Bin ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FromBinName'"></i>
                                                            <span ng-show="OrderBy == 'FromBinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('parttype')" ng-class="{'orderby' : OrderBy == 'parttype'}">
                                                        <div style="min-width: 200px;">
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'parttype'"></i>
                                                            <span ng-show="OrderBy == 'parttype'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SerialNumber')" ng-class="{'orderby' : OrderBy == 'SerialNumber'}">
                                                        <div style="min-width: 120px;">
                                                            SN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'SerialNumber'"></i>
                                                            <span ng-show="OrderBy == 'SerialNumber'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('UniversalModelNumber')" ng-class="{'orderby' : OrderBy == 'UniversalModelNumber'}">
                                                        <div style="min-width: 120px;">
                                                            MPN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'UniversalModelNumber'"></i>
                                                            <span ng-show="OrderBy == 'UniversalModelNumber'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('input')" ng-class="{'orderby' : OrderBy == 'input'}">
                                                        <div style="min-width: 120px;">
                                                            Test Result <i class="fa fa-sort pull-right" ng-show="OrderBy != 'input'"></i>
                                                            <span ng-show="OrderBy == 'input'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('COO')" ng-class="{'orderby' : OrderBy == 'COO'}">
                                                        <div style="min-width: 120px;">
                                                            COO <i class="fa fa-sort pull-right" ng-show="OrderBy != 'COO'"></i>
                                                            <span ng-show="OrderBy == 'COO'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Rigname')" ng-class="{'orderby' : OrderBy == 'Rigname'}">
                                                        <div style="min-width: 120px;">
                                                            Test Rig <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Rigname'"></i>
                                                            <span ng-show="OrderBy == 'Rigname'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                        <div>
                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>
                                                            <span ng-show="OrderBy == 'disposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ToBinName')" ng-class="{'orderby' : OrderBy == 'ToBinName'}">
                                                        <div style="min-width: 200px;">
                                                            Bin ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ToBinName'"></i>
                                                            <span ng-show="OrderBy == 'ToBinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th>
                                                        <div style="min-width: 120px;">
                                                            Actions
                                                        </div>
                                                    </th>

                                                </tr>

                                                <tr class="errornone">
                                                    <td></td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FromBinName" ng-model="filter_text[0].FromBinName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="parttype" ng-model="filter_text[0].parttype" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SerialNumber" ng-model="filter_text[0].SerialNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="UniversalModelNumber" ng-model="filter_text[0].UniversalModelNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="input" ng-model="filter_text[0].input" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="COO" ng-model="filter_text[0].COO" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Rigname" ng-model="filter_text[0].Rigname" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ToBinName" ng-model="filter_text[0].ToBinName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <!-- Actions filter - no filter needed -->
                                                    </td>
                                                </tr>
                                            </thead>

                                            <tbody ng-show="Assets.length > 0">
                                                <tr ng-repeat="asset in Assets">
                                                    <td class="actionicons" style="min-width: 40px;">
                                                        <a href="{{host}}label/master/examples/failureanalysislabel.php?id={{asset.failure_analysis_id}}" target="_blank">
                                                            <i class="material-icons print" role="img" aria-label="print">print</i>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        {{asset.FromBinName}}
                                                    </td>
                                                    <td>
                                                        {{asset.parttype}}
                                                    </td>
                                                    <td>
                                                        {{asset.SerialNumber}}
                                                    </td>
                                                     <td>
                                                        {{asset.UniversalModelNumber}}
                                                    </td>
                                                    <td>
                                                        {{asset.input}}
                                                    </td>

                                                    <td>
                                                        {{asset.COO}}
                                                    </td>
                                                    <td>
                                                        {{asset.Rigname}}
                                                    </td>
                                                    <td>
                                                        {{asset.disposition}}
                                                    </td>
                                                    <td>
                                                        {{asset.ToBinName}}
                                                    </td>
                                                    <td class="actionicons" style="min-width: 120px;">
                                                        <!-- Actions Dropdown Menu -->
                                                        <md-menu md-position-mode="target-right target" md-offset="0 0">
                                                            <!-- Dropdown Trigger Button -->
                                                            <md-button aria-label="Actions" class="md-icon-button" ng-click="$mdMenu.open($event)">
                                                                <span class="dropdown-text">Actions</span>
                                                                <md-icon class="material-icons">arrow_drop_down</md-icon>
                                                            </md-button>

                                                            <!-- Menu Content -->
                                                            <md-menu-content>
                                                                <!-- Create Bin Action -->
                                                                <md-menu-item>
                                                                    <md-button ng-click="CreateBin(asset,asset.SiteID,$event)" class="create-action">
                                                                        Create Bin
                                                                    </md-button>
                                                                </md-menu-item>

                                                                <!-- Move Bin Action -->
                                                                <md-menu-item>
                                                                    <md-button ng-click="MoveBinToStationLocationGroup(asset,asset.SiteID,$event)"
                                                                               class="move-action"
                                                                               ng-disabled="!asset.ToBinName">
                                                                        Move Bin
                                                                    </md-button>
                                                                </md-menu-item>

                                                                <!-- Close Bin Action -->
                                                                <md-menu-item>
                                                                    <md-button ng-click="CloseBin(asset,asset.SiteID,$event)"
                                                                               class="close-action"
                                                                               ng-disabled="!asset.ToBinName">
                                                                        Close Bin
                                                                    </md-button>
                                                                </md-menu-item>

                                                                <!-- Consolidate Bin Action -->
                                                                <md-menu-item>
                                                                    <md-button ng-click="ConsolidateBin(asset,asset.SiteID,$event)"
                                                                               class="consolidate-action"
                                                                               ng-disabled="!asset.ToBinName">
                                                                        Consolidate Bin
                                                                    </md-button>
                                                                </md-menu-item>

                                                                <!-- Nest to Bin Action -->
                                                                <md-menu-item>
                                                                    <md-button ng-click="NestToBin(asset,asset.SiteID,$event)"
                                                                               class="nest-action"
                                                                               ng-disabled="!asset.ToBinName">
                                                                        Nest to Bin
                                                                    </md-button>
                                                                </md-menu-item>
                                                            </md-menu-content>
                                                        </md-menu>
                                                    </td>
                                                </tr>
                                            </tbody>

                                            <tfoot>
                                                <tr>
                                                    <td colspan="11">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                </md-card>
                <!--List Close-->


            </article>
        </div>
    </div>
</div>
