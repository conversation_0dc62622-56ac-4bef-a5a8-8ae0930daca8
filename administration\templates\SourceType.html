
<div class="row page" data-ng-controller="SourceType">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Source Type</span>
                        <div flex></div>
                            <a href="#!/SourceTypeList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a> 
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="SourceType_Form" class="form-validation" data-ng-submit="submitForm()">                            
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Source Type Name</label>
                                    <input type="text" name="Cumstomertype"  ng-model="SourceType['Cumstomertype']"  required ng-maxlength="100" />
                                    <div class="error-space"> 
                                        <div ng-messages="SourceType_Form.Cumstomertype.$error" multiple ng-if='SourceType_Form.Cumstomertype.$dirty'>
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="Description"  ng-model="SourceType['Description']"  required ng-maxlength="250" />
                                    <div class="error-space"> 
                                        <div ng-messages="SourceType_Form.Description.$error" multiple ng-if='SourceType_Form.Description.$dirty'>
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 250.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">       
                                    <label>Status</label>
                                    <md-select name="Active" ng-model="SourceType.Active" required aria-label="select">
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="Inactive"> Inactive </md-option>
                                    </md-select>   
                                    <div class="error-space"> 
                                        <div ng-messages="SourceType_Form.Status.$error" multiple ng-if='SourceType_Form.Status.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>    
                                    </div>                                         
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/SourceTypeList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="SourceType_Form.$invalid || SourceType.busy" ng-click="SaveSourceType()">
                                <span ng-show="! SourceType.busy">Save</span>
                                <span ng-show="SourceType.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>