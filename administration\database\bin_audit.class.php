<?php
session_start();
include_once("admin.class.php");
class BinAuditClass extends AdminClass {

    public function GetAllDispositions ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Controls Page';
				return json_encode($json);
			}
            
            $sql = "Select disposition_id, disposition,sub_disposition from disposition where status='Active' order by disposition";            
            $query = mysqli_query($this->connectionlink,$sql);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $i = 0;
                    while($row = mysqli_fetch_assoc($query)) {
                        $result[$i] = $row;
                        $i++;
                    }
                    $json['Success'] = true;
                    $json['Result'] = $result;
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'No Results';
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function GetFacilities ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Controls Page';
				return json_encode($json);
			}
			$query = "select * from facility where FacilityStatus = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' order by FacilityName";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Facilities Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function SaveBinAuditSchedule ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);			
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Controls Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Audit Controls Page';
				return json_encode($json);
			}


			$part_type = '';			
			for($j=0;$j<count($data['part_type']);$j++) {				
				if($data['part_type'][$j] != '') {
					$part_type = $part_type . $data['part_type'][$j];
					if($j < count($data['part_type']) - 1) {
						$part_type = $part_type . ',';					
					}
				}				
			}
			$data['part_type'] = $part_type;					
            if($data['ControlID'] > 0) { //Update existing

                //Start check for duplicate control
                $query = "select count(*) from bin_audit_controls where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' and part_type = '".mysqli_real_escape_string($this->connectionlink,$data['part_type'])."' and ControlID != '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."'";
                $q = mysqli_query($this->connectionlink,$query);			
                if(mysqli_error($this->connectionlink)) {			
                    $json['Success'] = false;			
                    $json['Result'] = mysqli_error($this->connectionlink);			
                    return json_encode($json);			
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['count(*)'] > 0) {
                        $json['Success'] = false;			
                        $json['Result'] = 'Schedule already exists with Facility and Disposition and Part Type Combination';
                        return json_encode($json);
                    }
                }
                //End check for duplicate control

                $query = "update bin_audit_controls set FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',BinsCount = '".mysqli_real_escape_string($this->connectionlink,$data['BinsCount'])."',AuditFrequency = '".mysqli_real_escape_string($this->connectionlink,$data['AuditFrequency'])."',Accuracy = '".mysqli_real_escape_string($this->connectionlink,$data['Accuracy'])."',Status = '".mysqli_real_escape_string($this->connectionlink,$data['Status'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',part_type = '".mysqli_real_escape_string($this->connectionlink,$data['part_type'])."' where ControlID = '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."' ";
                $q = mysqli_query($this->connectionlink,$query);			
                if(mysqli_error($this->connectionlink)) {			
                    $json['Success'] = false;			
                    $json['Result'] = mysqli_error($this->connectionlink);			
                    return json_encode($json);			
                }

                $json['Success'] = true;			
                $json['Result'] = 'Control Updated';
                return json_encode($json);

            } else { //Create new
                //Start check for duplicate control
                $query = "select count(*) from bin_audit_controls where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' and part_type = '".mysqli_real_escape_string($this->connectionlink,$data['part_type'])."'";				
                $q = mysqli_query($this->connectionlink,$query);			
                if(mysqli_error($this->connectionlink)) {			
                    $json['Success'] = false;			
                    $json['Result'] = mysqli_error($this->connectionlink);			
                    return json_encode($json);			
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['count(*)'] > 0) {
                        $json['Success'] = false;			
                        $json['Result'] = 'Schedule already exists with Facility and Disposition and Part Type Combination';
                        return json_encode($json);
                    }
                }
                //End check for duplicate control

                $query1 = "insert into bin_audit_controls (Type, FacilityID, disposition_id, BinsCount, AuditFrequency, Accuracy, Status,  CreatedDate, CreatedBy,part_type) values ('Schedule','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['BinsCount'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditFrequency'])."','".mysqli_real_escape_string($this->connectionlink,$data['Accuracy'])."','".mysqli_real_escape_string($this->connectionlink,$data['Status'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['part_type'])."')";
                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                $insert_id = mysqli_insert_id($this->connectionlink);
                $json['ControlID'] = $insert_id;

                $json['Success'] = true;
                $json['Result'] = 'New Control Created';
                return json_encode($json);
            }			

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function SaveBinAuditTarget ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Controls Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Audit Controls Page';
				return json_encode($json);
			}

            if($data['ControlID'] > 0) { //Update existing

                //Start check If BIN Name is valid or not
                $query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['AssignedBinName'])."' ";
                $q = mysqli_query($this->connectionlink,$query);			
                if(mysqli_error($this->connectionlink)) {			
                    $json['Success'] = false;			
                    $json['Result'] = mysqli_error($this->connectionlink);			
                    return json_encode($json);			
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['FacilityID'] != $data['FacilityID']) {
                        $json['Success'] = false;			
                        $json['Result'] = 'Assigned Bin and facility are not matching';
                        return json_encode($json);    
                    }
                    $CustomPalletID = $row['CustomPalletID'];                    
                } else {
                    $json['Success'] = false;			
                    $json['Result'] = 'Invalid Assigned BinName';
                    return json_encode($json);
                }
                //End check If BIN Name is valid of not

                $query1 = "update bin_audit_controls set FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."',AssignedBinName = '".mysqli_real_escape_string($this->connectionlink,$data['AssignedBinName'])."',CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."',Accuracy = '".mysqli_real_escape_string($this->connectionlink,$data['Accuracy'])."' where ControlID = '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."' ";
                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {	
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

				$query2 = "update bin_audit_bins set AccuracyTarget = '".mysqli_real_escape_string($this->connectionlink,$data['Accuracy'])."' where ControlID = '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."' ";
				$q2 = mysqli_query($this->connectionlink,$query2);
                if(mysqli_error($this->connectionlink)) {	
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                $json['Success'] = true;			
                $json['Result'] = 'Control Updated';
                return json_encode($json);


            } else { //Create new

                //Start check If BIN Name is valid or not
                $query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['AssignedBinName'])."' ";
                $q = mysqli_query($this->connectionlink,$query);			
                if(mysqli_error($this->connectionlink)) {			
                    $json['Success'] = false;			
                    $json['Result'] = mysqli_error($this->connectionlink);			
                    return json_encode($json);			
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['FacilityID'] != $data['FacilityID']) {
                        $json['Success'] = false;			
                        $json['Result'] = 'Assigned Bin and facility are not matching';
                        return json_encode($json);    
                    }

					if($row['StatusID'] != '1') {
						$json['Success'] = false;			
                        $json['Result'] = 'Only Active Bins are allowed for Audit,Selected Bin is not Active.';
                        return json_encode($json);
					}

					if($row['AssetsCount'] == 0) {
						$json['Success'] = false;			
                        $json['Result'] = 'No Serials available in the Bin';
                        return json_encode($json);
					}

                    $CustomPalletID = $row['CustomPalletID'];
					$TotalAssets = $row['AssetsCount'];
                } else {
                    $json['Success'] = false;			
                    $json['Result'] = 'Invalid Assigned BinName';
                    return json_encode($json);
                }
                //End check If BIN Name is valid of not

                //Start check for duplicate control
                $query = "select count(*) from bin_audit_controls where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."' and Status != 'Completed' ";
                $q = mysqli_query($this->connectionlink,$query);			
                if(mysqli_error($this->connectionlink)) {			
                    $json['Success'] = false;			
                    $json['Result'] = mysqli_error($this->connectionlink);			
                    return json_encode($json);			
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['count(*)'] > 0) {
                        $json['Success'] = false;			
                        $json['Result'] = 'Assigned BIN Name is already added';
                        return json_encode($json);
                    }
                }
                //End check for duplicate control

                //$query1 = "insert into bin_audit_controls (Type, FacilityID,AssignedBinName, Status,  CreatedDate, CreatedBy,CustomPalletID,Accuracy) values ('Target','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssignedBinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['Status'])."',NOW(),'".$_SESSION['user']['UserId']."','".$CustomPalletID."','".mysqli_real_escape_string($this->connectionlink,$data['Accuracy'])."')";
				$query1 = "insert into bin_audit_controls (Type, FacilityID,AssignedBinName, Status,  CreatedDate, CreatedBy,CustomPalletID,Accuracy) values ('Target','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssignedBinName'])."','Active',NOW(),'".$_SESSION['user']['UserId']."','".$CustomPalletID."','".mysqli_real_escape_string($this->connectionlink,$data['Accuracy'])."')";
                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                $insert_id = mysqli_insert_id($this->connectionlink);
                $json['ControlID'] = $insert_id;

				//Start insert in audit control bins table
				$query2 = "insert into bin_audit_bins (ControlID,CustomPalletID,FacilityID,AuditLocked,CreatedDate,CreatedBy,Status,Type,TotalAssets,AccuracyTarget) values ('".mysqli_real_escape_string($this->connectionlink,$insert_id)."','".$CustomPalletID."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','1',NOW(),'".$_SESSION['user']['UserId']."','Locked','Target','".mysqli_real_escape_string($this->connectionlink,$TotalAssets)."','".mysqli_real_escape_string($this->connectionlink,$data['Accuracy'])."')";
				$q2 = mysqli_query($this->connectionlink,$query2);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

				$query3 = "update custompallet set ControlID = '".mysqli_real_escape_string($this->connectionlink,$insert_id)."',DateAddedToBinAudit = NOW(),AuditLocked = '1' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
				//End insert in audit control bins table

                $json['Success'] = true;
                $json['Result'] = 'New Control Created';
                return json_encode($json);
            }			

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function GetBinAuditControlsList($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No data'
			);

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Controls Page';
				return json_encode($json);
			}

            $query = "select r.*,f.FacilityName,d.disposition from bin_audit_controls r 
            left join facility f on f.FacilityID = r.FacilityID 
            left join disposition d on d.disposition_id = r.disposition_id
            where r.FacilityID = '".$_SESSION['user']['FacilityID']."' and r.Status != 'Completed' and r.Type = 'Schedule' ";

			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'Type') {
							$query = $query . " AND r.Type like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'BinsCount') {
							$query = $query . " AND r.BinsCount like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'AuditFrequency') {
							$query = $query . " AND r.AuditFrequency like '".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'Accuracy') {
							$query = $query . " AND r.Accuracy like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'Status') {
							$query = $query . " AND r.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'AssignedBinName') {
							$query = $query . " AND r.AssignedBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'CreatedDate') {
							$query = $query . " AND r.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'LastRanDate') {
							$query = $query . " AND r.LastRanDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'part_type') {
							$query = $query . " AND r.part_type like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

					}
				}
			}
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				  if($data['OrderBy'] == 'Type') {
					$query = $query . " order by r.Type ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'BinsCount') {
					$query = $query . " order by r.BinsCount ".$order_by_type." ";
				} else if($data['OrderBy'] == 'AuditFrequency') {
					$query = $query . " order by r.AuditFrequency ".$order_by_type." ";
				} else if($data['OrderBy'] == 'Accuracy') {
					$query = $query . " order by r.Accuracy ".$order_by_type." ";
				} else if($data['OrderBy'] == 'Status') {
					$query = $query . " order by r.Status ".$order_by_type." ";
				} else if($data['OrderBy'] == 'AssignedBinName') {
					$query = $query . " order by r.AssignedBinName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'CreatedDate') {
					$query = $query . " order by r.CreatedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'LastRanDate') {
					$query = $query . " order by r.LastRanDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'part_type') {
					$query = $query . " order by r.part_type ".$order_by_type." ";
				} 
			} else {
				$query = $query . " order by CreatedDate asc ";
			}

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));

            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $i = 0;
                while($row = mysqli_fetch_assoc($q)) {
                    $result[$i] = $row;
                    $i++;
                }
                $json['Success'] = true;
                $json['Result'] = $result;
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Records Available";
            }

            if($data['skip'] == 0) {

                $query1 = "select count(*) from removal_codes r,facility f,disposition d,statusses ss where f.FacilityID = r.FacilityID AND d.disposition_id = r.disposition_id AND r.StatusID = ss.StatusID";

                $query1 = "select count(*) from bin_audit_controls r 
                left join facility f on f.FacilityID = r.FacilityID 
                left join disposition d on d.disposition_id = r.disposition_id
                where r.FacilityID = '".$_SESSION['user']['FacilityID']."' and r.Status != 'Completed' and r.Type = 'Schedule' ";
                if($data[0] && count($data[0]) > 0) {
                    foreach ($data[0] as $key => $value) {
                        if($value != '') {

                            if($key == 'Type') {
                                $query1 = $query1 . " AND r.Type like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'disposition') {
                                $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'FacilityName') {
                                $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'BinsCount') {
                                $query1 = $query1 . " AND r.BinsCount like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'AuditFrequency') {
                                $query1 = $query1 . " AND r.AuditFrequency like '".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'Accuracy') {
                                $query1 = $query1 . " AND r.Accuracy like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'Status') {
                                $query1 = $query1 . " AND r.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
    
                            if($key == 'AssignedBinName') {
                                $query1 = $query1 . " AND r.AssignedBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
    
                            if($key == 'CreatedDate') {
                                $query1 = $query1 . " AND r.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
    
                            if($key == 'LastRanDate') {
                                $query1 = $query1 . " AND r.LastRanDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

							if($key == 'part_type') {
								$query1 = $query1 . " AND r.part_type like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

                        }
                    }
                }

                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row1 = mysqli_fetch_assoc($q1);
                    $count = $row1['count(*)'];
                }
                $json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function GetAuditControlDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Controls Page';
				return json_encode($json);
			}
			$query = "select * from bin_audit_controls where ControlID = '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."'";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);	
				$row['part_type'] = explode(",",$row['part_type']);
                $row['BinsCount'] = intval($row['BinsCount']);
				$row['Accuracy'] = floatval($row['Accuracy']);
				$json['Success'] = true;			
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "Invalid Details";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



    public function GetBinAuditBinsList($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No data'
			);

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Unlock Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Unlock Bin Audit Page';
				return json_encode($json);
			}

            $query = "select r.*,f.FacilityName,d.disposition,c.BinName,DATEDIFF(NOW(),r.CreatedDate) as days_assigned from bin_audit_bins r 
            left join custompallet c on r.CustomPalletID = c.CustomPalletID 
            left join facility f on f.FacilityID = c.FacilityID 
            left join disposition d on d.disposition_id = c.disposition_id
            where r.AuditLocked = 1 and r.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'BinName') {
							$query = $query . " AND c.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}						
						if($key == 'Status') {
							$query = $query . " AND r.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}                        
                        if($key == 'CreatedDate') {
							$query = $query . " AND r.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'Type') {
							$query = $query . " AND r.Type like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if($data['OrderBy'] == 'BinName') {
					$query = $query . " order by c.BinName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'Status') {
					$query = $query . " order by r.Status ".$order_by_type." ";
				} else if($data['OrderBy'] == 'CreatedDate') {
					$query = $query . " order by r.CreatedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'Type') {
					$query = $query . " order by r.Type ".$order_by_type." ";
				} 
			} else {
				$query = $query . " order by r.CreatedDate asc ";
			}

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));

            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $i = 0;
                while($row = mysqli_fetch_assoc($q)) {
                    $result[$i] = $row;
                    $i++;
                }
                $json['Success'] = true;
                $json['Result'] = $result;
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Records Available";
            }

            if($data['skip'] == 0) {

                $query1 = "select count(*) from bin_audit_bins r 
                left join custompallet c on r.CustomPalletID = c.CustomPalletID 
                left join facility f on f.FacilityID = c.FacilityID 
                left join disposition d on d.disposition_id = c.disposition_id
                where r.AuditLocked = 1 and r.FacilityID = '".$_SESSION['user']['FacilityID']."' ";
                if($data[0] && count($data[0]) > 0) {
                    foreach ($data[0] as $key => $value) {
                        if($value != '') {

                            if($key == 'BinName') {
                                $query1 = $query1 . " AND c.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'disposition') {
                                $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'FacilityName') {
                                $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }						
                            if($key == 'Status') {
                                $query1 = $query1 . " AND r.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }                        
                            if($key == 'CreatedDate') {
                                $query1 = $query1 . " AND r.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

							if($key == 'Type') {
								$query1 = $query1 . " AND r.Type like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

                        }
                    }
                }

                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row1 = mysqli_fetch_assoc($q1);
                    $count = $row1['count(*)'];
                }
                $json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function RemoveBinFromAuditList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Unlock Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Unlock Bin Audit Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Unlock Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Unlock Bin Audit Page';
				return json_encode($json);
			}

            $query = "update bin_audit_bins set AuditLocked = 0,Status = 'Unlocked',UnlockedBy = '".$_SESSION['user']['UserId']."',UnlockedDate = NOW() where ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
            
            $query1 = "update custompallet set AuditLocked = 0,StatusID = 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' ";
            $q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if($data['Type'] == 'Target') {
				$query2 = "update bin_audit_controls set Status = 'Completed',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}

            $json['Success'] = true;
			$json['Result'] = 'Bin removed from Pending Bin Audit List';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetPartTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit Controls')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Controls Page';
				return json_encode($json);
			}
            
			$query = "select * from business_rule_attribute_values where attribute_id = '16' order by value";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$part_types = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$part_types[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $part_types;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Values Available";
			}
			return json_encode($json);            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

}
?>