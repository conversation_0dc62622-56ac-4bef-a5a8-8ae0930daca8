<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['managerpendinglabortrackerxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "LaborTrackerOverRide.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Pending Labor Tracking List');
$header = array('Date','First Name','Last Name','User Name','Manager','Shift','Facility','Program','AccountType','Profile');

$sql = "Select ltp.EventDate,ltp.UserId,f.FacilityName,u.FirstName,u.LastName,u.UserName,p.ProfileName,u.OtherManager,
			mu.UserName as otherusername,u.Shift,up.ProgramName,u.AccountType
			from labor_tracker_pending ltp 
			LEFT JOIN users u ON ltp.UserId=u.UserId LEFT JOIN facility f ON u.FacilityID=f.FacilityID 
			LEFT JOIN profile_type p on u.ProfileID=p.ProfileID
			LEFT JOIN users mu on mu.UserId=u.Manager
			LEFT JOIN Program up on up.ProgramID=u.Program
			WHERE 1=1";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                    if ($key == 'EventDate') {
                        $getUsersQuery = $getUsersQuery . " AND ltp.EventDate like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'FacilityName') {
                        $getUsersQuery = $getUsersQuery . " AND f.FacilityName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'FirstName') {
                        $getUsersQuery = $getUsersQuery . " AND u.FirstName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'LastName') {
                        $getUsersQuery = $getUsersQuery . " AND u.LastName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'UserName') {
                        $getUsersQuery = $getUsersQuery . " AND u.UserName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'OtherManager') {
                        $getUsersQuery = $getUsersQuery . " AND u.OtherManager like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'Shift') {
                        $getUsersQuery = $getUsersQuery . " AND u.Shift like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ProgramName') {
                        $getUsersQuery = $getUsersQuery . " AND up.ProgramName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'AccountType') {
                        $getUsersQuery = $getUsersQuery . " AND u.AccountType like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ProfileName') {
                        $getUsersQuery = $getUsersQuery . " AND p.ProfileName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }                   
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }
            if ($data['OrderBy'] == 'EventDate') {
                $getUsersQuery = $getUsersQuery . " order by ltp.EventDate " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'FacilityName') {
                $getUsersQuery = $getUsersQuery . " order by f.FacilityName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'FirstName') {
                $getUsersQuery = $getUsersQuery . " order by u.FirstName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'LastName') {
                $getUsersQuery = $getUsersQuery . " order by u.LastName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'ProfileName') {
                $getUsersQuery = $getUsersQuery . " order by p.ProfileName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'UserName') {
                $getUsersQuery = $getUsersQuery . " order by u.UserName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'OtherManager') {
                $getUsersQuery = $getUsersQuery . " order by u.OtherManager " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'Shift') {
                $getUsersQuery = $getUsersQuery . " order by u.Shift " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'ProgramName') {
                $getUsersQuery = $getUsersQuery . " order by up.ProgramName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'AccountType') {
                $getUsersQuery = $getUsersQuery . " order by u.AccountType " . $order_by_type . " ";
            }
        } else {
            $sql = $sql . " order by ltp.EventDate ASC ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
            while($row = mysqli_fetch_assoc($query))
            {
                $row['EventDate'] = date('M d, Y', strtotime($row['EventDate']));
                if($row['OtherManager'] != '')
                {
                    $row['OtherManager'] = $row['otherusername'];
                }
                $row2  = array($row['EventDate'],$row['FirstName'],$row['LastName'],$row['UserName'],$row['OtherManager'],$row['Shift'],$row['FacilityName'],$row['ProgramName'],$row['AccountType'],$row['ProfileName']);
                $rows[] = $row2;
            }

$sheet_name = 'Labor Tracker Override';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 