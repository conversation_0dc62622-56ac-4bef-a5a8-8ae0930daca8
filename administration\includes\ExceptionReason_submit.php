<?php
session_start();
include_once("../database/ExceptionReason.class.php");
$obj = new ExceptionReasonClass();

if($_POST['ajax'] == "ExceptionReason"){
		$result = $obj->ExceptionReason($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetExceptionReasonDetails"){
  		$result = $obj->GetExceptionReasonDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetExceptionReasonList"){
  		$result = $obj->GetExceptionReasonList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateExceptionReasonListxls") {
		$result = $obj->GenerateExceptionReasonListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAllExceptionReasons") {
		$result = $obj->GetAllExceptionReasons($_POST);
		echo $result;
  }

	if($_POST['ajax'] == "UpdateDefaultExceptionReason") {
		$result = $obj->UpdateDefaultExceptionReason($_POST);
		echo $result;
	}
