<div ng-controller = "RepairTypeList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">
                <div class="body_inner_content">
                    <md-card class="no-margin-h pt-0">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="SanitizationTypeList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="SanitizationTypeList = !SanitizationTypeList" class="material-icons md-primary" ng-show="SanitizationTypeList">keyboard_arrow_up</i>
                                <i ng-click="SanitizationTypeList = !SanitizationTypeList" class="material-icons md-primary" ng-show="! SanitizationTypeList">keyboard_arrow_down</i>
                                <span ng-click="SanitizationTypeList = !SanitizationTypeList">Repair Type List</span>
                                <div flex></div> 
                               
                                <a href="#!/RepairType" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Repair Type
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">                            
                            <p>No Repair Types available </p>
                        </div>
                        
                        <div class="row"  ng-show="SanitizationTypeList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div> 
                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('value')" ng-class="{'orderby' : OrderBy == 'value'}">
                                                        <div>                               
                                                            Repair Type<i class="fa fa-sort pull-right" ng-show="OrderBy != 'value'"></i>                                 
                                                            <span ng-show="OrderBy == 'value'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('description')" ng-class="{'orderby' : OrderBy == 'description'}">
                                                        <div>                               
                                                            Description<i class="fa fa-sort pull-right" ng-show="OrderBy != 'description'"></i>                                 
                                                            <span ng-show="OrderBy == 'description'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('status')" ng-class="{'orderby' : OrderBy == 'status'}">                         
                                                        <div>                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'status'"></i>                                  
                                                            <span ng-show="OrderBy == 'status'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('file_url')" ng-class="{'orderby' : OrderBy == 'file_url'}">                         
                                                        <div>                               
                                                            Uploaded File <i class="fa fa-sort pull-right" ng-show="OrderBy != 'file_url'"></i>                                  
                                                            <span ng-show="OrderBy == 'file_url'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="value" ng-model="filter_text[0].value" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="description" ng-model="filter_text[0].description" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="status" ng-model="filter_text[0].status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="file_url" ng-model="filter_text[0].file_url" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                   
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/RepairType/{{product.value_id}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a></td>
                                                    <td>
                                                        {{product.value}}                            
                                                    </td> 
                                                    <td>
                                                        {{product.description}}                            
                                                    </td>                                                                          
                                                    <td>                                                        
                                                        {{product.status}}
                                                    </td>
                                                    <td>
                                                        <a ng-show="product.file_url != ''" target="_blank" href="../download_s3.php?key={{product.file_url}}">{{product.file_url}}</a>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="7">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>                
                    </md-card>
                </div>
            </article>
        </div>
    </div>
</div>