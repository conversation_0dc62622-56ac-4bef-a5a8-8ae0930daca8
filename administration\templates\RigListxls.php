<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['RigListXLS'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "RigList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Rig List');
$header = array('Rig Name','Description','Work Station Name','Rig Limit','Status');

$sql = "select r.*,S.SiteName,ss.StatusName from Rig r,site S,statusses ss where r.SiteID = S.SiteID and r.Status = ss.StatusID";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                    if ($key == 'Rigname') {
							$query = $query . " AND r.Rigname like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
						if ($key == 'Description') {
							$query = $query . " AND r.Description like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
						if ($key == 'SiteName') {
							$query = $query . " AND S.SiteName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
						if ($key == 'StatusName') {
							$query = $query . " AND ss.StatusName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
						if ($key == 'RigLimit') {
							$query = $query . " AND r.RigLimit like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

           if ($data['OrderBy'] == 'Rigname') {
					$query = $query . " order by r.Rigname " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Description') {
					$query = $query . " order by r.Description " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'SiteName') {
					$query = $query . " order by S.SiteName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StatusName') {
					$query = $query . " order by ss.StatusName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'RigLimit') {
					$query = $query . " order by r.RigLimit " . $order_by_type . " ";
				}
        } else {
            $sql = $sql . " order by r.Rigname desc ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
while($row = mysqli_fetch_assoc($query))
{
    $row2  = array($row['Rigname'],$row['Description'],$row['SiteName'],$row['RigLimit'],$row['StatusName']);
    $rows[] = $row2;
}

$sheet_name = 'Rig List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 