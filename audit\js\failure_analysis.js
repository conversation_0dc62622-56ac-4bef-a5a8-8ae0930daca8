(function () {
    'use strict';

    angular.module('app').controller("failure_analysis", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {


        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Failure Analysis',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.PartTypes = [];
        $scope.COOList = [];


        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPartTypesAndCOO',
            success: function(data){
                if(data.Success) {
                    if(data.PartTypes) {
                        $scope.PartTypes = data.PartTypes;
                    }
                    if(data.COOList) {
                        $scope.COOList = data.COOList;
                    }
                } else {
                    $scope.PartTypes = [];
                    $scope.COOList = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    //console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.asset = {'fa_custom_id': 'n/a','fa_notes' : 'n/a'};
        $scope.Records = [{'fa_custom_id': 'n/a','fa_notes' : 'n/a'}];
        $scope.CustomPalletID = '';
        $scope.InputResults = [];
        $scope.Stations = [];
        $scope.StationCustomPallets = [];
        $scope.Rigs = [];
        $scope.DefaultInputID = '';
        $scope.DefaultRigID = '';
        $scope.Assets = [];
        $scope.RigLimit = 0;
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetInputResults&Workflow=Failure Analysis&workflow_id=2',
            success: function(data){
                if(data.Success) {
                    $scope.InputResults = data.Result;
                    if(data.Default) {
                        $scope.asset.fa_input_id = data.Default;
                        $scope.DefaultInputID = data.Default;
                        $scope.Records[0].fa_input_id = $scope.DefaultInputID;
                    }
                } else {
                    $scope.InputResults = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });


        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilityStations&Workflow=Failure Analysis&workflow_id=2',
            success: function(data){
                if(data.Success) {
                    $scope.Stations = data.Result;
                } else {
                    $scope.Stations = [];
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });


        $scope.GetStationCustomPallets = function () {
            if($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetStationCustomPallets&SiteID='+$scope.SiteID+'&Workflow=Failure Analysis&workflow_id=2&CustomPalletID='+$scope.CustomPalletID,
                    success: function(data){
                        if(data.Success) {
                            $scope.StationCustomPallets = data.Result;
                        } else {
                            $scope.StationCustomPallets = [];
                            $scope.SiteID = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.StationCustomPallets = [];
            }
        };


        $scope.GetStationRigs = function () {
            if($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/failure_analysis_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetStationRigs&SiteID='+$scope.SiteID+'&Workflow=Failure Analysis&workflow_id=2',
                    success: function(data){
                        if(data.Success) {
                            $scope.Rigs = data.Result;
                        } else {
                            $scope.Rigs = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.asset.RigID = '';
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.Rigs = [];
                $scope.asset.RigID = '';
            }
        };

        $scope.MapCustomPalletToDisposition = function (item) {
            if($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MapCustomPalletToDisposition&SiteID='+$scope.SiteID+'&Workflow=Failure Analysis&workflow_id=2'+'&'+$.param(item)+'&SourceCustomPalletID='+$scope.CustomPalletID,
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            item.CustomPalletID = data.CustomPalletID;
                            item.AssetsCount = data.AssetsCount;
                            if(data.LocationID) {
                                item.LocationID = data.LocationID;
                            }
                            if(data.LocationName) {
                                item.LocationName = data.LocationName;
                            }
                            if(data.lable_name) {
                                item.lable_name = data.lable_name;
                            }
                            if(data.LocationType) {
                                item.LocationType = data.LocationType;
                            }
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            item.BinName = '';
                            item.AssetsCount = '';
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.GetCustomPalletDetails = function () {
            if($scope.BinName) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/failure_analysis_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetCustomPalletDetails&BinName='+$scope.BinName+'&SiteID='+$scope.SiteID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.CustomPalletID = data.CustomPalletID;
                            //$scope.GetFAAssets();
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.CustomPalletID = '';
                            $scope.BinName = '';
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.CustomPalletID = '';
            }
        };

       /* $scope.GetFAAssets = function () {
            if($scope.CustomPalletID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/failure_analysis_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetFAAssets&CustomPalletID='+$scope.CustomPalletID,
                    success: function(data) {
                        if(data.Success) {
                            $scope.Assets = data.Result;
                        } else {
                            $scope.Assets = [];
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.Assets = [];
            }
        };
*/
        $scope.busy = false;
        $scope.AssetsSanizedPanel = [];
        $scope.Assets = [];
       // $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            //if($scope.CustomPalletID > 0)  {
            if($scope.SiteID > 0)  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/failure_analysis_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetFAAssets&limit='+$scope.itemsPerPage+'&CustomPalletID='+$scope.CustomPalletID+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text))+'&SiteID='+$scope.SiteID,
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {

                            $scope.Assets = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            // $mdToast.show (
                            //     $mdToast.simple()
                            //         .content(data.Result)
                            //         .action('OK')
                            //         .position('right')
                            //         .hideDelay(0)
                            //         .toastClass('md-toast-danger md-block')
                            // );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'audit/includes/failure_analysis_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetFAAssets&limit='+$scope.itemsPerPage+'&CustomPalletID='+$scope.CustomPalletID+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text))+'&SiteID='+$scope.SiteID,
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.Assets = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        // $mdToast.show (
                        //     $mdToast.simple()
                        //         .content(data.Result)
                        //         .action('OK')
                        //         .position('right')
                        //         .hideDelay(0)
                        //         .toastClass('md-toast-danger md-block')
                        // );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic

        $scope.disposition_color = '';
        $scope.sub_disposition_color = '';
        $scope.ApplyBusinessRule = function (asset) {
            if(asset.fa_input_id > 0 && asset.parttypeid > 0 && asset.COOID > 0) {
                asset.input_id = asset.fa_input_id;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ApplyBusinessRule&workflow_id=2&'+$.param(asset)+'&SiteID='+$scope.SiteID,
                    success: function(data){
                        if(data.Success) {
                            if (data.ExactMPN) {
                                asset.UniversalModelNumber = data.ExactMPN;
                            }
                            asset.disposition_id = data.Result.disposition_id;
                            asset.disposition = data.Result.disposition;
                            asset.fa_rule_id = data.Result.rule_id;
                            if(data.CustomPalletID) {
                                asset.CustomPalletID = data.CustomPalletID;
                                asset.BinName = data.BinName;
                                $scope.GetCurrentTime(asset,'bin_scan_time');
                            } else {
                                asset.CustomPalletID = '';
                                asset.BinName = '';
                            }
                            asset.rule_description = data.Result.rule_description;
                            asset.rule_id_text  = data.Result.rule_id_text;
                            asset.disposition_color = data.Result.color_code;
                            asset.sub_disposition_color = data.Result.sub_color_code;

                            if(data.Result.rule_id_text) {
                                //$scope.FocusAddMore();                                
                                if($scope.Records[0].AssetScanID == asset.AssetScanID) {
                                    $scope.AddFaAsset();
                                }                                
                            }
                            // if($scope.asset.RigID){
                            //   $window.document.getElementById('main_save').focus();
                            //   $window.document.getElementById('scan_for_save').focus();
                            // }else{
                            //   $window.document.getElementById('select_rigid').focus();
                            // }

                        } else {
                            if (data.ExactMPN) {
                                asset.UniversalModelNumber = data.ExactMPN;
                            }
                            asset.disposition_id = '';
                            asset.disposition = '';
                            asset.disposition_color = '';
                            asset.CustomPalletID = '';
                            asset.BinName = '';
                            asset.fa_rule_id = '';
                            asset.rule_description = '';
                            asset.rule_id_text  = '';
                            //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };


        $scope.GetMPNFromSerialFA = function (asset,ind) {

            //Start check If serial already in the list
            var exists = false;
            for(var i=0;i<$scope.Records.length;i++) {
                if(($scope.Records[i].SerialNumber == asset.SerialNumber) && (i != ind)) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content('Serial already added')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                    exists = true;
                }
            }
            //End check If serial already in the list

            if(! exists) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/failure_analysis_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetMPNFromSerialFA&SerialNumber='+asset.SerialNumber+'&CustomPalletID='+$scope.CustomPalletID,
                    success: function(data){
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            asset.UniversalModelNumber = data.Result.UniversalModelNumber;
                            $scope.GetCurrentTime(asset,'mpn_scan_time');
                            asset.AssetScanID = data.Result.AssetScanID;
                            asset.parttypeid = data.Result.parttypeid;
                            asset.COOID = data.Result.COOID;

                            $scope.GetCurrentTime(asset,'part_type_scan_time');
                            $scope.GetCurrentTime(asset,'coo_scan_time');
                            if(data.Result.parttypeid > 0 && data.Result.parttypeid > 0) {
                                $scope.ApplyBusinessRule(asset);
                            } else {
                                if(data.Result.parttypeid == '' || data.Result.parttypeid == null) {
                                    $window.document.getElementById('parttypeid'+ind).focus();
                                } else if(data.Result.COOID == '' || data.Result.COOID == null) {
                                    $window.document.getElementById('COOID'+ind).focus();
                                }
                            }
                        } else {
                            asset.UniversalModelNumber = '';
                            asset.AssetScanID = '';
                            asset.parttypeid = '';
                            asset.COOID = '';
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                        }                    
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }            
        };

        $scope.SerialChanged = function (asset) {
            asset.AssetScanID = '';
            asset.UniversalModelNumber = '';
            asset.disposition_id = '';
            asset.disposition = '';
            asset.disposition_color = '';
            asset.CustomPalletID = '';
            asset.BinName = '';
            asset.fa_rule_id = '';
            asset.rule_description = '';
            asset.rule_id_text  = '';
        };

        $scope.MPNChanged = function (asset) {
            asset.disposition_id = '';
            asset.disposition = '';
            asset.disposition_color = '';
            asset.CustomPalletID = '';
            asset.BinName = '';
            asset.fa_rule_id = '';
            asset.rule_description = '';
            asset.rule_id_text  = '';
            asset.fa_custom_id = 'n/a';
        };

        $scope.canDisableRig = function () {

            if($scope.Records.length > 1) {
                return true;
            } else {
                return false;
            }

            // if($scope.Records.length > 1) {
            //     return true;
            // } else if($scope.Records.length == 1) {
            //     if($scope.Records[0]['SerialNumber'] || $scope.Records[0]['SerialNumber'] != '' || $scope.Records[0]['UniversalModelNumber'] || $scope.Records[0]['UniversalModelNumber'] != '') {
            //         return true;
            //     } else {
            //         return false;
            //     }
            // } else {
            //     return false;
            // }
        };

        $scope.UpdateAssetFailureAnalysis = function (ev) {

            var duplicates = $scope.findDuplicateSerialNumbers($scope.Records);            
            if(duplicates.length > 0) {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Serial '+duplicates[0]+' is Scanned mulitple times')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else {
                if($scope.Records.length > 1) {
                    if(! $scope.Records[0]['SerialNumber'] && !$scope.Records[0]['UniversalModelNumber'] && $scope.Records[0]['fa_notes'] == 'n/a') {
                        $scope.Records.splice(0,1);
                        if($scope.areAllFaInputIdsSame($scope.Records)) {
                            var confirm = $mdDialog.confirm()
                                .title('Test Result for all Scanned Serials are same, you want to continue ?')
                                .content('')
                                .ariaLabel('Lucky day')
                                .targetEvent(ev)
                                .ok('Yes')
                                .cancel('No');
                                $mdDialog.show(confirm).then(function() {
                                    $scope.ProcessRecords(ev);
                                }, function() {
                                });
                        } else {
                            $scope.ProcessRecords(ev);
                        } 
                    } else {
                        if($scope.Records[0]['disposition'] == '' || $scope.Records[0]['BinName'] == '') {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content('Some required fields are missing')
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        } else {
                            if($scope.areAllFaInputIdsSame($scope.Records)) {
                                var confirm = $mdDialog.confirm()
                                    .title('Test Result for all Scanned Serials are same, you want to continue ?')
                                    .content('')
                                    .ariaLabel('Lucky day')
                                    .targetEvent(ev)
                                    .ok('Yes')
                                    .cancel('No');
                                    $mdDialog.show(confirm).then(function() {
                                        $scope.ProcessRecords(ev);
                                    }, function() {
                                    });
                            } else {
                                $scope.ProcessRecords(ev);
                            }
                        }
                    }                          
                } else {
                    if($scope.Records[0]['disposition'] == '' || !$scope.Records[0]['disposition'] || $scope.Records[0]['BinName'] == '' || !$scope.Records[0]['BinName']) {
                        $mdToast.show (
                            $mdToast.simple()
                                .content('Some required fields are missing')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    } else {
                        $scope.ProcessRecords(ev);
                    }
                }
            }
        };

        $scope.ProcessRecords = function (ev) {

            $scope.asset.Records = $scope.Records;       
            if($scope.asset.CloseBin) {                    
                var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Empty BIN ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Empty BIN')
                .cancel('No');
                $mdDialog.show(confirm).then(function() {
                    $scope.asset.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'audit/includes/failure_analysis_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateAssetFailureAnalysis&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID+'&CloseBin=1',
                        success: function(data){
                            if(data.Success) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );

                                $scope.CallServerFunction(0);
                                $scope.GetStationCustomPallets();
                                if(data.CloseMessage) {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.CloseMessage)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-info md-block')
                                    );
                                }
                                if(data.BinClosed == '1') {
                                    $scope.CustomPalletID = '';
                                    $scope.BinName = '';
                                    $scope.Assets = [];
                                }
                                $scope.ClearAsset();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }, function() {
                    $scope.asset.CloseBin = false;
                    $scope.asset.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'audit/includes/failure_analysis_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateAssetFailureAnalysis&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID,
                        success: function(data){
                            if(data.Success) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );                                    
                                $scope.CallServerFunction(0);
                                $scope.GetStationCustomPallets();

                                if(data.CloseMessage) {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.CloseMessage)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-info md-block')
                                    );
                                }

                                $scope.ClearAsset();
                                $window.document.getElementById('SerialNumber').focus();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });

                });

            } else {
                $scope.asset.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/failure_analysis_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=UpdateAssetFailureAnalysis&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID,
                    success: function(data){
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.CallServerFunction(0);
                            $scope.GetStationCustomPallets();
                                                                                                                        
                            if(data.CloseMessage) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.CloseMessage)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-info md-block')
                                );
                            }
                            $scope.ClearAsset();
                            $window.document.getElementById('SerialNumber0').focus();
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            }

        };

        $scope.ClearAsset = function () {

            $scope.Records = [{'fa_custom_id': 'n/a','fa_notes' : 'n/a','fa_input_id' : $scope.DefaultInputID}];

            //$scope.asset = {'fa_input_id' : $scope.DefaultInputID,'RigID' : $scope.DefaultRigID};
            // $scope.asset.SerialNumber = '';
            // $scope.asset.AssetScanID = '';
            // $scope.asset.UniversalModelNumber = '';
            // $scope.asset.disposition_id = '';
            // $scope.asset.disposition = '';
            // $scope.disposition_color = '';
            // $scope.asset.CustomPalletID = '';
            // $scope.asset.BinName = '';
            // $scope.asset.fa_rule_id = '';
            // $scope.asset.rule_description = '';
            // $scope.asset.rule_id_text  = '';
            // if($scope.CopyCustomID) {

            // } else {
            //     $scope.asset.fa_custom_id = 'n/a';
            // }
            // $scope.asset.fa_notes = 'n/a';
        };

        $scope.GetRigLimit = function () {            
            $scope.RigLimit = 0;

            for(var i=0;i<$scope.Rigs.length;i++) {
                if($scope.Rigs[i]['RigID'] == $scope.asset.RigID) {
                    $scope.RigLimit = $scope.Rigs[i]['RigLimit'];
                }
            }
            setTimeout(function () {
                $window.document.getElementById('SerialNumber0').focus();
            }, 300);
        };


        $scope.AddFaAsset = function () {
            if($scope.Records.length < $scope.RigLimit) {
                //$scope.Records.unshift({'fa_custom_id': 'n/a','fa_notes' : 'n/a','fa_input_id' : $scope.DefaultInputID});
                $scope.Records.unshift({'fa_custom_id': 'n/a','fa_notes' : 'n/a','fa_input_id' : $scope.Records[0].fa_input_id});

                setTimeout(function () {
                    var len = $scope.Records.length - 1;
                    //var id = 'SerialNumber'+len;
                    var id = 'SerialNumber0';
                    $window.document.getElementById(id).focus();
                }, 100);            
            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Rig Limit Reached')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        $scope.RemoveAsset = function (ev,ind) {

            if($scope.Records.length == 1) {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Minimum one record is required')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else {
                var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
                $mdDialog.show(confirm).then(function() {
                    $scope.Records.splice(ind,1);
                    if(ind != 0) {
                        //$scope.AddFaAsset();
                    }
                }, function() {
                });
            }
        };

        $scope.FocusAddMore = function () {
            $window.document.getElementById('add_button').focus();
        };


        $scope.findDuplicateSerialNumbers = function(dataArray) {
            const serialNumbers = {};
            const duplicates = [];
        
            // Iterate through the array
            dataArray.forEach(item => {
                const { SerialNumber } = item;
        
                // If SerialNumber is already seen, it's a duplicate
                if (serialNumbers[SerialNumber]) {
                    duplicates.push(SerialNumber);
                } else {
                    serialNumbers[SerialNumber] = true;
                }
            });
        
            return duplicates;
        };


        $scope.areAllFaInputIdsSame = function(dataArray) {
            if (dataArray.length === 0) return false;
        
            const firstFaInputId = dataArray[0].fa_input_id;
        
            for (let i = 1; i < dataArray.length; i++) {
                if (dataArray[i].fa_input_id !== firstFaInputId) {
                    return false;
                }
            }
            return true;
        };

        function MoveBinTPVRController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };
            $scope.focusNextField = function (id) {
                $window.document.getElementById(id).focus();                
            };

            $scope.MoveBinToNewLocationGroup = function (ev) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveBinToNewLocationGroup&'+$.param($scope.confirmDetails)+'&'+$.param($scope.CurrentPallet),
                    success: function(data) {
                        if(data.Success) {
                            // $mdToast.show (
                            //     $mdToast.simple()
                            //     .content(data.Result)
                            //     .action('OK')
                            //     .position('right')
                            //     .hideDelay(0)
                            //     .toastClass('md-toast-success md-block')
                            // );
                            
                            var message = 'Location Group Updated : Sub Location (<b>'+data.newLocationName+'</b>) from  Location Group (<b>'+data.GroupName+'</b>) has been assigned to BIN (<b>'+$scope.CurrentPallet.BinName+'</b>)';
    
                            $mdToast.show({
                                template: `
                                    <md-toast class="md-toast-success md-block">
                                        <span class="md-toast-text" flex>${message}</span>
                                        <md-button class="md-highlight" ng-click="closeToast()">OK</md-button>
                                    </md-toast>
                                `,
                                controller: function($scope, $mdToast) {
                                    $scope.closeToast = function() {
                                        $mdToast.hide();
                                    };
                                },
                                hideDelay: 0,
                                position: 'right',
                                toastClass: 'md-toast-success md-block'
                            });
                            
                            $scope.hide();
                        } else {                                                        
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
                
            };


            function LocationChange1(text) {
                $scope.confirmDetails.group = text;
            }
    
            function selectedLocationChange1(item) {
                if (item) {
                    if (item.value) {
                        $scope.confirmDetails.group = item.value;
                    } else {
                        $scope.confirmDetails.group = '';
                    }
                } else {
                    $scope.confirmDetails.group = '';
                }
            }
    
            $scope.queryLocationSearch1 = queryLocationSearch1;
            $scope.LocationChange1 = LocationChange1;
            $scope.selectedLocationChange1 = selectedLocationChange1;
            function queryLocationSearch1(query) {
                if (query) {
                    if (query != '' && query != 'undefined') {                    
                        return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&LocationType=WIP')
                            .then(function (res) {
                                if (res.data.Success == true) {
                                    if (res.data.Result.length > 0) {
                                        var result_array = [];
                                        for (var i = 0; i < res.data.Result.length; i++) {
                                            result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                        }
                                        return result_array;
                                    } else {
                                        return [];
                                    }
                                } else {
                                    return [];
                                }
                            });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }


        }

        $scope.CurrentPallet = {};
        $scope.confirmDetails = {};
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();            
        }

        $scope.MoveBinToStationLocationGroup = function(bin,SiteID,ev) {
            bin.SiteID = SiteID;
             //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: MoveBinTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose:true,
                resolve: {
                    CurrentPallet: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails) {  
                $scope.GetStationCustomPallets();
            }, function(confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });

        };



        //Start TPVR for Consolidation

        function ConsolidationTPVRController($scope,$mdDialog,CurrentCustomPalletPallet,$mdToast,$window) {
            $scope.CurrentCustomPalletPallet = CurrentCustomPalletPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails3);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails3);
            };
            $scope.focusNextField = function (id) {
                $window.document.getElementById(id).focus();                
            };
  
            $scope.ConsolidateBin = function (ev) {
              console.log($scope.confirmDetails4);
              console.log($scope.CurrentCustomPalletPallet);
              $rootScope.$broadcast('preloader:active');
  
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=ConsolidateBin&FromBinName='+$scope.CurrentCustomPalletPallet.BinName+'&ToBinName='+$scope.confirmDetails4.ToBinName+'&FromCustomPalletID='+$scope.CurrentCustomPalletPallet.CustomPalletID+'&ToShipmentContainer='+$scope.confirmDetails4.ToShipmentContainer,
                  success: function(data) {
                      $rootScope.$broadcast('preloader:hide');
                      if(data.Success) {
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-success md-block')
                          );      
                          $scope.hide();
                      } else {                                                        
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }                    
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      //alert(data.Result);
                      //alert("3");
                      $scope.error = data;
                      initSessionTime(); $scope.$apply();
                  }
              });
  
            };
            $scope.MoveBinToNewLocationGroup = function (ev) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveBinToNewLocationGroup&'+$.param($scope.confirmDetails3)+'&'+$.param($scope.CurrentPallet),
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                            );      
                            $scope.hide();
                        } else {                                                        
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
                
            };
          }
  
        $scope.CurrentCustomPalletPallet = {};
        $scope.confirmDetails4 = {};
        function afterShowAnimation14 () {            
          $window.document.getElementById("newbin").focus();            
        }
  
        $scope.ConsolidateBin = function(bin,SiteID,ev) {
          console.log(bin);
            bin.SiteID = SiteID;
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: ConsolidationTPVRController,
                templateUrl: 'password4.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation14,
                clickOutsideToClose:true,
                resolve: {
                    CurrentCustomPalletPallet: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails4) {  
                $scope.GetStationCustomPallets();
            }, function(confirmDetails4) {
                $scope.confirmDetails4 = confirmDetails4;
            });
  
        };
  
        //End TPVR for Consolidation

        // Actions Functions for Bin Management

        // Create Bin Function
        $scope.CreateBin = function(bin, SiteID, ev) {
            $scope.createBinData = {
                binName: '',
                dispositionId: bin.disposition_id || '',
                notes: ''
            };
            $scope.createBinBusy = false;

            $mdDialog.show({
                templateUrl: 'createBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope,
                preserveScope: true
            });
        };

        $scope.createBin = function() {
            $scope.createBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'audit/includes/failure_analysis_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CreateBin&' + $.param($scope.createBinData) + '&SiteID=' + $scope.SiteID,
                success: function(data) {
                    $scope.createBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if(data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $mdDialog.hide();
                        $scope.GetStationCustomPallets();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(data) {
                    $scope.createBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Close Bin Function
        $scope.CloseBin = function(bin, SiteID, ev) {
            $scope.CurrentBin = bin;
            $scope.closeBinData = {
                reason: '',
                comments: ''
            };
            $scope.closeBinBusy = false;

            $mdDialog.show({
                templateUrl: 'closeBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope,
                preserveScope: true
            });
        };

        $scope.closeBin = function() {
            $scope.closeBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'audit/includes/failure_analysis_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CloseBin&' + $.param($scope.closeBinData) + '&' + $.param($scope.CurrentBin) + '&SiteID=' + $scope.SiteID,
                success: function(data) {
                    $scope.closeBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if(data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $mdDialog.hide();
                        $scope.GetStationCustomPallets();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(data) {
                    $scope.closeBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Nest to Bin Function
        $scope.NestToBin = function(bin, SiteID, ev) {
            $scope.nestToBinData = {
                BinName: bin.BinName,
                CustomPalletID: bin.CustomPalletID,
                parentBin: ''
            };
            $scope.nestToBinBusy = false;
            $scope.AvailableParentBins = [];

            // Get available parent bins
            jQuery.ajax({
                url: host + 'audit/includes/failure_analysis_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetAvailableParentBins&SiteID=' + SiteID + '&CurrentBinID=' + bin.CustomPalletID,
                success: function(data) {
                    if(data.Success) {
                        $scope.AvailableParentBins = data.Result;
                    }
                    initSessionTime();
                    $scope.$apply();
                }
            });

            $mdDialog.show({
                templateUrl: 'nestToBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope,
                preserveScope: true
            });
        };

        $scope.nestToBin = function() {
            $scope.nestToBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'audit/includes/failure_analysis_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=NestToBin&' + $.param($scope.nestToBinData) + '&SiteID=' + $scope.SiteID,
                success: function(data) {
                    $scope.nestToBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if(data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $mdDialog.hide();
                        $scope.GetStationCustomPallets();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(data) {
                    $scope.nestToBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Cancel function for modals
        $scope.cancel = function() {
            $mdDialog.cancel();
        };


    });

})();
