<div ng-controller = "SiteList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="SiteList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="SiteList = !SiteList" class="material-icons md-primary" ng-show="SiteList">keyboard_arrow_up</i>
                                <i ng-click="SiteList = !SiteList" class="material-icons md-primary" ng-show="! SiteList">keyboard_arrow_down</i>
                                <span ng-click="SiteList = !SiteList">Work Stations</span>
                                <div flex></div> 
                                <a href="#!/siteList" ng-click="SiteListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>

                                <a href="#!/siteList" ng-click="SiteListxls()" class="md-button md-raised md-default dis_open_v mr-5" style="display:none; min-width: 40px;">
                                    <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon>
                                </a>
                                <div class="upload-btn-wrapper text-center mt-5">
                                    <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                                    <input type="file" ng-file-select="onFileSelect($files)" id="SiteFile">  
                                    <a href="../../sample_files/upload_WorkStation_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                                </div> 


                                <a href="#!/site" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Work Station
                                </a>

                                <a href="#!/site" class="md-button md-raised btn-w-md md-default dis_open2"  style="display:none;">
                                    <i class="material-icons">add</i> Create New
                                </a> 

                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="SiteList">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div> 
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                       
                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SiteName')" ng-class="{'orderby' : OrderBy == 'SiteName'}">
                                                        <div>                               
                                                            Work Station Name<i class="fa fa-sort pull-right" ng-show="OrderBy != 'SiteName'"></i>                                 
                                                            <span ng-show="OrderBy == 'SiteName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">                           
                                                        <div>                               
                                                            Work Station Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>                                    
                                                            <span ng-show="OrderBy == 'Description'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                         
                                                        <div style="min-width: 140px;">                               
                                                            Facility Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                  
                                                            <span ng-show="OrderBy == 'FacilityName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('GroupName')" ng-class="{'orderby' : OrderBy == 'GroupName'}">                         
                                                        <div style="min-width: 120px;">                               
                                                            Location Group <i class="fa fa-sort pull-right" ng-show="OrderBy != 'GroupName'"></i>                                  
                                                            <span ng-show="OrderBy == 'GroupName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>                                  
                                                            <span ng-show="OrderBy == 'StatusName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('workflow')" ng-class="{'orderby' : OrderBy == 'workflow'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Workflow <i class="fa fa-sort pull-right" ng-show="OrderBy != 'workflow'"></i>                                  
                                                            <span ng-show="OrderBy == 'workflow'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Recoverytype')" ng-class="{'orderby' : OrderBy == 'Recoverytype'}">                         
                                                        <div style="min-width: 150px;">                               
                                                            Recovery Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Recoverytype'"></i>                                  
                                                            <span ng-show="OrderBy == 'Recoverytype'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LockType')" ng-class="{'orderby' : OrderBy == 'LockType'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Locked <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LockType'"></i>                                  
                                                            <span ng-show="OrderBy == 'LockType'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th>
                                                        Unlock
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SiteName" ng-model="filter_text[0].SiteName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Description" ng-model="filter_text[0].Description" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="GroupName" ng-model="filter_text[0].GroupName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="workflow" ng-model="filter_text[0].workflow" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Recoverytype" ng-model="filter_text[0].Recoverytype" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LockType" ng-model="filter_text[0].LockType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    <td></td>                 
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/site/{{product.SiteID}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a></td>
                                                    <td>
                                                        {{product.SiteName}}                            
                                                    </td>                       
                                                    <td>
                                                        {{product.Description}}
                                                    </td>
                                                    <td>
                                                        {{product.FacilityName}}
                                                    </td>

                                                    <td>
                                                        {{product.GroupName}}
                                                    </td>

                                                    <td>
                                                        {{product.StatusName}}
                                                    </td>
                                                    <td>
                                                        {{product.workflow}}
                                                    </td>
                                                    <td>
                                                        {{product.Recoverytype}}
                                                    </td>  
                                                    <td>
                                                        {{product.LockType}}
                                                        <span ng-show="product.Locked == '1'"> ({{product.FirstName}} {{product.LastName}})</span>
                                                    </td>  
                                                    
                                                    <td>
                                                        <button class="md-button md-raised md-primary" style="min-height: 30px; min-width: 100px;" ng-click="UnlockStation(product,$event,$index)" ng-show="product.Locked == '1'">
                                                            <!-- <i class="material-icons">unlock</i>  -->
                                                            Unlock
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="8">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>