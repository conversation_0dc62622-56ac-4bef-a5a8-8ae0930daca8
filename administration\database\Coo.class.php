<?php
session_start();
include_once("admin.class.php");
include_once("../../common_functions.php");
class COOClass extends AdminClass
{
  public function COO($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/
		//return json_encode($json);
		if ($data['COOID'] == '') { //If New Class
			//$sql = "CALL ClassCreate('".mysqli_real_escape_string($this->connectionlink,$data['ProductClassName'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProductClassDesc'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProductClassStatus'])."')";
			$duplicate = $this->CheckDuplicate('New', 'COO', 'COO', $data['COO'], true, '', '');
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'COO already Exists';
				return json_encode($json);
			}
			$query = "insert into COO (COO,Description,Status,DateCreated,CreatedBy) values ('" . mysqli_real_escape_string($this->connectionlink, $data['COO']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',NOW(),'" . $_SESSION['user']['UserId'] . "')";
		} else {
			$duplicate = $this->CheckDuplicate('Edit', 'COO', 'COO', $data['COO'], true, 'COOID', $data['COOID']);
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'COO already Exists';
				return json_encode($json);
			}
			//Start get old data before update
			$query10 = "select * from COO where `COOID` = '".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$old = mysqli_fetch_assoc($q10);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid COOID';
				return json_encode($json);
			}
			//End get old data before update

			$query = "update COO set COO='" . mysqli_real_escape_string($this->connectionlink, $data['COO']) . "',Description='" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',UpdatedDate =NOW(),UpdatedBy='" . $_SESSION['user']['UserId'] . "' where COOID='" . mysqli_real_escape_string($this->connectionlink, $data['COOID']) . "'";
		}
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		if ($data['COOID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New COO created";
			$json['parttypeid'] = $insert_id;

			$Action = " COO '".$data['COO']."' created ";
			$this->RecordAdminTracking('COO','New',$data['COO'],$Action);

		} else {

				$fields = array();
				$fields[0]['field'] = 'COO';
				$fields[0]['Display'] = 'COO';

				$fields[1]['field'] = 'Description';
				$fields[1]['Display'] = 'Description';

				$fields[2]['field'] = 'Status';
				$fields[2]['Display'] = 'Status';
				$fields[2]['Reference'] = '1';
				$fields[2]['Table'] = 'statusses';
				$fields[2]['TableColumn'] = 'StatusName';
				$fields[2]['TableIDColumn'] = 'StatusID';

				$json['Update'] = $this->RecordAdminTracking1('COO',$old,$data,$fields,$data['COO']);


			$json['Success'] = true;
			$json['Result'] = "COO modified";
		}
		return json_encode($json);
	}

  public function GetCOOList($data)
	{
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['COOID']
			);

			/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Recoverytype Page';
				return json_encode($json);
			}*/

			$query = "select C.*,ss.StatusName from COO C join statusses ss on C.Status = ss.StatusID";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'COO') {
							$query = $query . " AND C.COO like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Description') {
							$query = $query . " AND C.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'StatusName') {
							$query = $query . " AND ss.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'COO') {
					$query = $query . " order by C.COO " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Description') {
					$query = $query . " order by C.Description " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StatusName') {
					$query = $query . " order by ss.StatusName " . $order_by_type . " ";
				}
 			} else {
				$query = $query . " order by COO desc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Data Available";
			}

			if ($data['skip'] == 0) {

				$query1 = "select count(*) from COO C join statusses ss on C.Status = ss.StatusID";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'COO') {
								$query1 = $query1 . " AND C.COO like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Description') {
								$query1 = $query1 . " AND C.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'StatusName') {
								$query1 = $query1 . " AND ss.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

  public function GenerateCOOListxls($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$transaction = 'Administration ---> eViridis Administration ---> COO';
		$description = 'COO List Exported';
		$this->RecordUserTransaction($transaction, $description);

		$_SESSION['COOListxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

  public function GetCOODetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/

		//return json_encode($json);
		$query = "select * from COO where COOID = '" . mysqli_real_escape_string($this->connectionlink, $data['COOID']) . "' ORDER BY COO";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid COO ID";
		}
		return json_encode($json);
	}

  public function GetAllCOOs($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Workflow Page';
				return json_encode($json);
			}
			$query = "select COOID, COO from COO where Status = '1' order by `COO`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Dispositions Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
}
