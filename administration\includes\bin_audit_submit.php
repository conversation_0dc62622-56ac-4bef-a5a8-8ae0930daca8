<?php
	session_start();
	include_once("../database/bin_audit.class.php");
	$obj = new BinAuditClass();
	
	if($_POST['ajax'] == "GetAllDispositions") {
		$result = $obj->GetAllDispositions($_POST);
		echo $result;
	}
    
    if($_POST['ajax'] == "GetFacilities") {
		$result = $obj->GetFacilities($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "SaveBinAuditSchedule") {
		$result = $obj->SaveBinAuditSchedule($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "SaveBinAuditTarget") {
		$result = $obj->SaveBinAuditTarget($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "GetBinAuditControlsList") {
		$result = $obj->GetBinAuditControlsList($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "GetAuditControlDetails") {
		$result = $obj->GetAuditControlDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBinAuditBinsList") {
		$result = $obj->GetBinAuditBinsList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "RemoveBinFromAuditList") {
		$result = $obj->RemoveBinFromAuditList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetPartTypes") {
		$result = $obj->GetPartTypes($_POST);
		echo $result;
	}
?>