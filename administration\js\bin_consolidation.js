(function () {
    'use strict';
    angular.module('app').controller("bin_consolidation", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {
        $scope.FromCustomPallet = {};
        $scope.ToCustomPallet = {};
        $scope.SearchCustomPallet = {};
        $scope.GetCustomPalletDetails = function (type,BinName) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/consolidation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCustomPalletDetails&BinName='+BinName,                    
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        if(type == 'From') {
                            $scope.FromCustomPallet = data.Result;
                            setTimeout(function () {
                                $window.document.getElementById("ToBinName").focus();
                            }, 300);
                        } else if(type == 'Search') {
                            $scope.SearchCustomPallet = data.Result;                                                        
                            setTimeout(function () {
                                $window.document.getElementById("SerialNumber").focus();
                            }, 300);
                        } else {
                            $scope.ToCustomPallet = data.Result;
                            setTimeout(function () {
                                $window.document.getElementById("BinConsolidateButton").focus();
                            }, 300);
                            
                        }                        
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        if(type == 'From') {
                            $scope.FromCustomPallet = {};
                        } else {
                            $scope.ToCustomPallet = {};
                        }
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {          
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.AddSerialToBin = function (SerialNumber,CustomPallet,type) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/consolidation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=AddSerialToBin&SerialNumber='+SerialNumber+'&CustomPalletID='+CustomPallet.CustomPalletID,
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        if(type == 'From') {                            
                            $scope.GetCustomPalletDetails('From',CustomPallet.BinName);
                            $scope.SerialNumber = '';
                        } else if(type == 'Search') {
                            $scope.GetCustomPalletDetails('Search',CustomPallet.BinName);
                            $scope.SearchSerialNumber = '';
                        } else {
                            $scope.GetCustomPalletDetails('To',CustomPallet.BinName);
                        }    
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );               
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                        
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {          
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.CloseBIN = function (CustomPallet,type,ev) {
            var confirm = $mdDialog.confirm()
            .title('Are you sure, You want to Close the BIN ?')
            .content('')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('Close')
            .cancel('Cancel');
            $mdDialog.show(confirm).then(function() {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/consolidation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CloseBIN&CustomPalletID='+CustomPallet.CustomPalletID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {                                
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );               
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );                        
                        }                        
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {          
                        $rootScope.$broadcast('preloader:hide');     
                        initSessionTime(); $scope.$apply();
                    }
                });

            }, function() {
            });
        };


        $scope.ConsolidateBIN = function (ev) {
            if($scope.FromCustomPallet.CustomPalletID > 0 && $scope.ToCustomPallet.CustomPalletID > 0) {
                
                if($scope.FromCustomPallet.CustomPalletID == $scope.ToCustomPallet.CustomPalletID) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content("From and To Bins can't be same")
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {
                    
                    var confirm = $mdDialog.confirm()
                    .title('Are you sure, You want to Consolidate BINs ?')
                    .content('')
                    .ariaLabel('Lucky day')
                    .targetEvent(ev)
                    .ok('Yes')
                    .cancel('Cancel');
                    $mdDialog.show(confirm).then(function() {
                        $rootScope.$broadcast('preloader:active');
                        $scope.busy = true;
                        jQuery.ajax({
                            url: host+'administration/includes/consolidation_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=ConsolidateBIN&FromCustomPalletID='+$scope.FromCustomPallet.CustomPalletID+'&ToCustomPalletID='+$scope.ToCustomPallet.CustomPalletID,
                            success: function(data) {
                                $scope.busy = false;
                                $rootScope.$broadcast('preloader:hide');
                                if(data.Success) {                                
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );               
                                    location.reload();
                                } else {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );                        
                                }                                
                                initSessionTime(); $scope.$apply();
                            }, error : function (data) {     
                                $scope.busy = false;     
                                $rootScope.$broadcast('preloader:hide');     
                                initSessionTime(); $scope.$apply();
                            }
                        });
        
                    }, function() {
                    });
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Select From and To BINs')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        
    });
})(); 