<div ng-controller = "UserActivityList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="BinList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">    
                                 <i ng-click="BinList = !BinList" class="material-icons md-primary" ng-show="BinList">keyboard_arrow_up</i>
                                <i ng-click="BinList = !BinList" class="material-icons md-primary" ng-show="! BinList">keyboard_arrow_down</i>
                                <span ng-click="BinList = !BinList">User Activity</span>
                                <div flex></div>

                                 <a ng-click="UserActivityxls()" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>                                                            
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="BinList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div> 
                                                    
                                        <table class="table table-striped">
                                            <thead>
                                                <tr class="th_sorting">                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FirstName')" ng-class="{'orderby' : OrderBy == 'FirstName'}">
                                                        <div>                               
                                                            User <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FirstName'"></i>                                 
                                                            <span ng-show="OrderBy == 'FirstName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ProfileName')" ng-class="{'orderby' : OrderBy == 'ProfileName'}">
                                                        <div>                               
                                                            Profile <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ProfileName'"></i>                                 
                                                            <span ng-show="OrderBy == 'ProfileName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>                                                                                   
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('TransactionType')" ng-class="{'orderby' : OrderBy == 'TransactionType'}">                           
                                                        <div>                               
                                                            Transaction Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'TransactionType'"></i>                                    
                                                            <span ng-show="OrderBy == 'TransactionType'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                           
                                                        <div>                               
                                                            Facility Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                    
                                                            <span ng-show="OrderBy == 'FacilityName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">                           
                                                        <div>                               
                                                            Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>                                    
                                                            <span ng-show="OrderBy == 'Description'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('TransactionIPAddress')" ng-class="{'orderby' : OrderBy == 'TransactionIPAddress'}">                          
                                                        <div>                               
                                                            IP Address <i class="fa fa-sort pull-right" ng-show="OrderBy != 'TransactionIPAddress'"></i>                                    
                                                            <span ng-show="OrderBy == 'TransactionIPAddress'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('TransactionTime')" ng-class="{'orderby' : OrderBy == 'TransactionTime'}">                           
                                                        <div>                               
                                                            Time <i class="fa fa-sort pull-right" ng-show="OrderBy != 'TransactionTime'"></i>                                    
                                                            <span ng-show="OrderBy == 'TransactionTime'">                                   
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">                                                      
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FirstName" ng-model="filter_text[0].FirstName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ProfileName" ng-model="filter_text[0].ProfileName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="TransactionType" ng-model="filter_text[0].TransactionType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Description" ng-model="filter_text[0].Description" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                                                   
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="TransactionIPAddress" ng-model="filter_text[0].TransactionIPAddress" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="TransactionTime" ng-model="filter_text[0].TransactionTime" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                                                    
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">                                                    
                                                    <td>
                                                        {{product.FirstName}}  {{product.LastName}}
                                                    </td> 
                                                    <td>
                                                        {{product.ProfileName}}                            
                                                    </td>
                                                    <td>
                                                        {{product.TransactionType}}                            
                                                    </td>
                                                    <td>
                                                        {{product.FacilityName}}                            
                                                    </td>
                                                    <td>
                                                        {{product.Description}}                            
                                                    </td>                                                     
                                                    <td>
                                                        {{product.TransactionIPAddress}}
                                                    </td>
                                                    <td>
                                                        {{product.TransactionTime | toDate | date:'MMM dd, yyyy hh:mm a'}}
                                                    </td>                                                    
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="5">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>
                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>                
                    </md-card>
                </div>
            </article>
        </div>
    </div>
</div>