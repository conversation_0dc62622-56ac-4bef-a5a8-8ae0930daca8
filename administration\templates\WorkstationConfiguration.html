<div class="row page" data-ng-controller="WorkstationConfiguration">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Work Station Configuration</span>
                        <div flex></div>
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="site_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Group Name</label>
                                    <input type="text" name="GroupName"  ng-model="WorkstationConfiguration['GroupName']"  required ng-maxlength="45" />
                                    <div class="error-space">
                                    <div ng-messages="site_form.GroupName.$error" multiple ng-if='site_form.GroupName.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Work Station</label>
                                    <md-select name="GroupID" ng-model="WorkstationConfiguration.SiteID" required aria-label="select" multiple>
                                        <md-option ng-repeat="siteinformation in site" value="{{siteinformation.SiteID}}"> {{siteinformation.SiteName}} </md-option>

                                    </md-select>
                                    <div ng-messages="site_form.SiteID.$error" multiple ng-if='site_form.SiteID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="WorkstationConfiguration.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In active </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="site_form.Status.$error" multiple ng-if='site_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/siteList" style="text-decoration: none;">
                                    <md-button class="md-button md-raised btn-w-md  md-default">
                                        Cancel
                                    </md-button>
                                </a>

                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="site_form.$invalid || WorkstationConfiguration.busy" ng-click="SaveWorkstationConfiguration()">
                                <span ng-show="! WorkstationConfiguration.busy">Save</span>
                                <span ng-show="WorkstationConfiguration.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>

            <md-card class="no-margin-h pt-0">
                <md-toolbar class="md-table-toolbar md-default" ng-init="WorkstationConfigurationList = true;">
                    <div class="md-toolbar-tools" style="cursor: pointer;">

                        <i ng-click="WorkstationConfigurationList = !WorkstationConfigurationList" class="material-icons md-primary" ng-show="WorkstationConfigurationList">keyboard_arrow_up</i>
                        <i ng-click="WorkstationConfigurationList = !WorkstationConfigurationList" class="material-icons md-primary" ng-show="! WorkstationConfigurationList">keyboard_arrow_down</i>
                        <span ng-click="WorkstationConfigurationList = !WorkstationConfigurationList">Work Station Configuration List</span>
                        <div flex></div>
                        <!-- <a href="#!/SortConfiguration" ng-click="sortconfigurationListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                            <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                        </a> -->
                    </div>
                </md-toolbar>

                <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                    <p>No Work Station Configuration Available </p>
                </div>

                <div class="row"  ng-show="WorkstationConfigurationList">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div ng-show="pagedItems" class="pull-right mt-10">
                                    <small>
                                    Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                    to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                        <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                    of <span style="font-weight:bold;">{{total}}</span>
                                    </small>
                            </div>
                            <div style="clear:both;"></div>
                            <div class="table-responsive" style="overflow: auto;">
                                <table class="table table-striped mb-0">
                                    <thead>

                                        <tr class="th_sorting">
                                            <th style="min-width: 40px;">Edit</th>

                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('GroupName')" ng-class="{'orderby' : OrderBy == 'GroupName'}">
                                                <div style="min-width: 80px;">
                                                    Group Name<i class="fa fa-sort pull-right" ng-show="OrderBy != 'GroupName'"></i>
                                                    <span ng-show="OrderBy == 'GroupName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>

                                             <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('SiteName')" ng-class="{'orderby' : OrderBy == 'SiteName'}">
                                                <div style="min-width: 130px;">
                                                    Work Station<i class="fa fa-sort pull-right" ng-show="OrderBy != 'SiteName'"></i>
                                                    <span ng-show="OrderBy == 'SiteName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th> -->
                                            <th>Work Station</th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">
                                                <div style="min-width: 100px;">
                                                    Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>
                                                    <span ng-show="OrderBy == 'StatusName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                        </tr>

                                        <tr class="errornone">
                                            <td>&nbsp;</td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="GroupName" ng-model="filter_text[0].GroupName" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td></td>
                                            <!-- <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="SiteName" ng-model="filter_text[0].SiteName" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td> -->
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td>
                                        </tr>
                                    </thead>

                                    <tbody ng-show="pagedItems.length > 0">
                                        <tr ng-repeat="product in pagedItems">
                                            <td class="action-icons" style="min-width: 40px;">
                                                <span ng-click="EditWorkstationConfiguration(product,$event)"><i class="material-icons text-danger edit">edit</i></span>
                                            </td>
                                            <td>
                                                {{product.GroupName}}
                                            </td>
                                            <td>
                                                {{product.Sitenamefinal}}
                                            </td>
                                            <td>
                                                {{product.StatusName}}
                                            </td>
                                        </tr>
                                    </tbody>

                                    <tfoot>
                                        <tr>
                                            <td colspan="4">
                                                <div>
                                                    <ul class="pagination">
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="firstPage()"><< First</a>
                                                        </li>
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="prevPage()"><< Prev</a>
                                                        </li>
                                                        <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                            <a style="cursor:pointer;">{{n+1}}</a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="nextPage()">Next >></a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="lastPage()">Last >></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </md-card>
        </article>
    </div>
</div>
