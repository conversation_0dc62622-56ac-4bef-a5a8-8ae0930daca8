<div class="row page" data-ng-controller="Workflow">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Workflow Creation(s)</span>
                        <div flex></div>
                            <a href="#!/WorkflowList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i> Back To List
                            </a>
                    </div>
                </md-toolbar>
                <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                <div class="row">
                    <div class="col-md-12">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Workflow Name</label>
                                    <input type="text" name="workflow"  ng-model="Workflow['workflow']"  required ng-maxlength="45" />
                                    <div ng-messages="material_signup_form.workflow.$error" multiple ng-if='material_signup_form.workflow.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Workflow Description</label>
                                    <input type="text" name="workflow_description"  ng-model="Workflow['workflow_description']"  required ng-maxlength="250" />
                                    <div ng-messages="material_signup_form.workflow_description.$error" multiple ng-if='material_signup_form.workflow_description.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>
                                    </div>

                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="status" ng-model="Workflow.status" required aria-label="select">
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="Inactive"> In Active </md-option>
                                    </md-select>
                                    <div ng-messages="material_signup_form.status.$error" multiple ng-if='material_signup_form.status.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/WorkflowList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="workflowSave()">
                                <span ng-show="! Workflow.busy">Save</span>
                                <span ng-show="Workflow.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                    </div>
                </div>
                </form>
            </md-card>
        </article>


           <!--   <div class="panel panel-info" ng-if="Workflow.workflow_id">
            <div class="panel-heading">{{convert('Workflow Information')}}</div>
            <div class="panel-body">
                <div class="form-group ng-scope">
                    <div class="col-sm-2">
                        <input type="text" class="form-control" ng-model="input" name="input" placeholder="Input Name" required maxlength="50">
                    </div>
                    <div class="col-sm-2">
                        <input type="button" value="Add" class="btn btn-primary" ng-disabled="!input" ng-click="Addinputtype(input)">
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-6 col-md-offset-3">
                            <table class="table" ng-show="workflowinput.length > 0">
                                <thead>
                                    <tr>
                                        <th>Input Name</th>
                                        <th>Input Type</th>
                                        <th>Delete</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="workflow in workflowinput">
                                        <td>{{workflow.input}}</td>
                                        <td>{{workflow.input_type}}</td>
                                        <td>
                                            <span style="cursor: pointer;color:red;" ng-click="Deleteinputtype($index,workflow.input_id)">Delete</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->


        <md-card class="no-margin-h" ng-show="Workflow.workflow_id">
            <div class="row">
                <form>
                    <div class="col-md-12">
                        <div class="col-md-3">

                            <md-input-container class="md-block">
                                <label>Input</label>
                                <input type="text" name="input"  ng-model="Workflow['input']"  required ng-maxlength="45" />
                                <div ng-messages="material_signup_form.input.$error" multiple ng-if='material_signup_form.input.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 45.</div>
                                </div>
                            </md-input-container>

                        </div>
                        <div class="col-md-3">
                            <md-input-container class="md-block">
                                <label>Input Type</label>
                                <md-select name="input_type" ng-model="Workflow.input_type" required aria-label="select">
                                    <!-- <md-option value="Pass"> Pass </md-option>
                                    <md-option value="Damage"> Damage </md-option>
                                    <md-option value="Sideline"> Sideline </md-option>
                                    <md-option value="Fail"> Fail </md-option>
                                    <md-option value="Bypass"> Bypass </md-option>
                                    <md-option value="Retest"> Retest </md-option> -->

                                    <md-option value="FALabTesting-Bypass">FALabTesting-Bypass</md-option>
                                    <md-option value="FALabTesting-Damage">FALabTesting-Damage</md-option>
                                    <md-option value="FALabTesting-Fail">FALabTesting-Fail</md-option>
                                    <md-option value="FALabTesting-Pass">FALabTesting-Pass</md-option>
                                    <md-option value="FALabTesting-Retest">FALabTesting-Retest</md-option>
                                    <md-option value="Harvest-Bypass">Harvest-Bypass</md-option>
                                    <md-option value="Harvest-Damage">Harvest-Damage</md-option>
                                    <md-option value="Harvest-Fail">Harvest-Fail</md-option>
                                    <md-option value="Harvest-Pass">Harvest-Pass</md-option>
                                    <md-option value="Receive-Damage">Receive-Damage</md-option>
                                    <md-option value="Receive-Pass">Receive-Pass</md-option>
                                    <md-option value="Repair-Bypass">Repair-Bypass</md-option>
                                    <md-option value="Repair-Damage">Repair-Damage</md-option>
                                    <md-option value="Repair-Fail">Repair-Fail</md-option>
                                    <md-option value="Repair-Pass">Repair-Pass</md-option>
                                    <md-option value="RMAInvestigation-Bypass">RMAInvestigation-Bypass</md-option>
                                    <md-option value="RMAInvestigation-Damag">RMAInvestigation-Damage</md-option>
                                    <md-option value="RMAInvestigation-Fail">RMAInvestigation-Fail</md-option>
                                    <md-option value="RMAInvestigation-Pass">RMAInvestigation-Pass</md-option>
                                    <md-option value="Sanitization-Bypass">Sanitization-Bypass</md-option>
                                    <md-option value="Sanitization-Damage">Sanitization-Damage</md-option>
                                    <md-option value="Sanitization-Fail">Sanitization-Fail</md-option>
                                    <md-option value="Sanitization-Pass">Sanitization-Pass</md-option>
                                    <md-option value="Sanitization-Resanitize">Sanitization-Resanitize</md-option>

                                    <md-option value="Sort-Pass">Sort-Pass</md-option>
                                    <md-option value="Sort-Damage">Sort-Damage</md-option>
                                    <md-option value="Recovery-Pass">Recovery-Pass</md-option>

                                </md-select>
                                <div ng-messages="material_signup_form.input_type.$error" multiple ng-if='material_signup_form.input_type.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px;" ng-disabled="!Workflow.input || !Workflow.input_type" ng-click="Addinputtype()">Add</button>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div class="table-responsive" style="overflow: auto;">
                                <table md-table md-row-select  ng-show="workflowinput.length > 0">
                                    <thead md-head>
                                        <tr md-row>
                                            <th md-column>Input</th>
                                            <th md-column>Input Type</th>
                                            <th md-column>Default Value</th>
                                            <th md-column style="padding-left: 5px;">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody md-body>
                                        <tr md-row ng-repeat="workflow in workflowinput">
                                            <td md-cell>{{workflow.input}}</td>
                                            <td md-cell>{{workflow.input_type}}</td>
                                            <td md-cell><md-switch ng-model="workflow.default" ng-change="UpdateDefault(workflow.input_id,workflow.default,workflow)">Access</md-switch></td>
                                            <!-- <td md-cell>
                                                <span class="text-danger actionlinkicon" ng-click="Deleteinputtype(workflow.input_id)"><i class="material-icons">delete</i>  </span>
                                            </td> -->
                                            <td md-cell>
                                                <!-- <md-switch ng-model="workflow.Active" ng-change="Activeinputtype(workflow.input_id,workflow.Active)">Active</md-switch> -->
                                                <md-switch ng-model="workflow.status" aria-label="default" class="md-default" ng-true-value="'Active'" ng-false-value="'Inactive'" ng-change="ChangeWorkflowInputStatus(workflow,$event)" ng-show="!workflow.loading" ng-disabled="workflow.default">{{workflow.status}}</md-switch>
                                                <i class="fa fa-spinner" style="font-size: 16px;" aria-hidden="true" ng-show="workflow.loading"></i>

                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </md-card>


        <md-card class="no-margin-h" ng-show="Workflow.workflow_id">

            <md-toolbar class="md-table-toolbar md-default">
                <div class="md-toolbar-tools">
                    <span>Map Dispositions</span>
                </div>
            </md-toolbar>

            <div class="row">
                <form>
                    <div class="col-md-12">
                        <!--<h4> Map Dispositions</h4>-->
                        <div class="col-md-3">
                            <md-input-container class="md-block">
                                <label>Disposition</label>
                                <md-select name="disposition_id" ng-model="Workflow.disposition_id" required aria-label="select">
                                    <md-option ng-repeat="dis_position in Dispositions" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px;" ng-disabled="!Workflow.disposition_id" ng-click="AddDispositionToWorkflow($event)">Add</button>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-12">
                                <table md-table md-row-select  ng-show="MappedDispositions.length > 0">
                                    <thead md-head>
                                        <tr md-row>
                                            <th md-column>Disposition</th>
                                            <th md-column>Delete</th>
                                        </tr>
                                    </thead>
                                    <tbody md-body>
                                        <tr md-row ng-repeat="disp in MappedDispositions">
                                            <td md-cell>{{disp.disposition}} <span style="color:red;" ng-show="disp.sub_disposition > 0">(Sub Disposition)</span></td>
                                            <td md-cell>
                                                <span class="text-danger actionlinkicon" ng-click="DeleteMappedDisposition(disp,$index,$event)" role="button" tabindex="0"><i class="material-icons">delete_forever</i> Delete  </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                        </div>
                    </div>
                </form>
            </div>
        </md-card>


    </div>
</div>
