(function () {
    'use strict';
    angular.module('app').controller("disposition_override", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Disposition Override',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
        

        $scope.record = {};
        $scope.AllDispositions = [];
        $scope.BusinessRules = [];
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllDispositions',
            success: function(data) {
                $rootScope.$broadcast('preloader:hide');
                if(data.Success) {
                   $scope.AllDispositions = data.Result;                       
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );    
                    $scope.AllDispositions = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetBusinessRuleNames',
            success: function(data) {                
                if(data.Success) {
                   $scope.BusinessRules = data.Result;                       
                } else {
                    // $mdToast.show (
                    //     $mdToast.simple()
                    //         .content(data.Result)
                    //         .action('OK')
                    //         .position('right')
                    //         .hideDelay(0)
                    //         .toastClass('md-toast-danger md-block')
                    // );    
                    $scope.BusinessRules = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.UpdateSerialDisposition = function () {
            if($scope.record.disposition_id && $scope.record.BinName && $scope.record.SerialNumber && $scope.record.OverrideReason && $scope.record.RequesterLogin) {
                if($scope.record.disposition_id == '4' && !$scope.record.rule_id) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content('Select Business Rule')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'administration/includes/disposition_override_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateSerialDisposition&'+$.param($scope.record),
                        success: function(data) {
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {
                                $scope.record.SerialNumber = '';
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );                                 
                            }                        
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {          
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });

                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Enter New Disposition,Requester Login,Bin Name, Override Reason and Serial Number')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        $scope.UpdateBulkDisposition = function () {
            if($scope.record.disposition_id_bulk && $scope.record.ToBinName && $scope.record.FromBinName && $scope.record.OverrideReason_bulk && $scope.record.RequesterLogin_bulk) {

                if($scope.record.disposition_id_bulk == '4' && !$scope.record.rule_id_bulk) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content('Select Business Rule')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'administration/includes/disposition_override_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateBulkDisposition&'+$.param($scope.record),
                        success: function(data) {
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {
                                $scope.record.FromBinName = '';
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );                                 
                            }                        
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {          
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }

            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Enter New Disposition,Requester Login,Override Reason, From and To Bins')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


    });



    angular.module('app').controller("disposition_override_assets", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {
        $scope.record = {'Eligible': false,'NotesRequired': false};


        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Disposition Override Assets',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.AllDispositions = [];
        $scope.BusinessRules = [];
        $scope.OverrideReasons = [];
        $scope.UserInputs = [];
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllDispositions',
            success: function(data) {
                $rootScope.$broadcast('preloader:hide');
                if(data.Success) {
                   $scope.AllDispositions = data.Result;                       
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );    
                    $scope.AllDispositions = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllUserInputs',
            success: function(data) {                
                if(data.Success) {
                   $scope.UserInputs = data.Result;                       
                } else {                        
                    $scope.UserInputs = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {                            
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositionOverrideReasons',
            success: function(data) {                
                if(data.Success) {
                   $scope.OverrideReasons = data.Result;                       
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );    
                    $scope.OverrideReasons = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {                          
                initSessionTime(); $scope.$apply();
            }
        });


        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetLoggedinUserDetails',
            success: function(data) {                
                if(data.Success) {
                    $scope.record.RequesterLogin = data.Result.FirstName + ' '+data.Result.LastName;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );                        
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetBusinessRuleNames',
            success: function(data) {                
                if(data.Success) {
                   $scope.BusinessRules = data.Result;                       
                } else {
                    // $mdToast.show (
                    //     $mdToast.simple()
                    //         .content(data.Result)
                    //         .action('OK')
                    //         .position('right')
                    //         .hideDelay(0)
                    //         .toastClass('md-toast-danger md-block')
                    // );    
                    $scope.BusinessRules = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.CheckDispositionOverrideEligibility = function () {
            if($scope.record.FromDispositionID > 0 && $scope.record.ToDispositionID > 0) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/disposition_override_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CheckDispositionOverrideEligibility&DispositionFrom='+$scope.record.FromDispositionID+'&DispositionTo='+$scope.record.ToDispositionID+'&EligibilityType=SN-Level',
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {                            
                            $scope.record.Eligible = true;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );    
                            $scope.record.Eligible = false;                              
                        }                        
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {          
                        $rootScope.$broadcast('preloader:hide');     
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.record.Eligible = false;
            }
        };

        $scope.ValidateBinDisposition = function () {
            if($scope.record.BinName != '') {
                if($scope.record.ToDispositionID > 0) {

                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'administration/includes/disposition_override_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateBinDisposition&DispositionTo='+$scope.record.ToDispositionID+'&BinName='+$scope.record.BinName,
                        success: function(data) {
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {                                                            
                                $scope.record.ToCustomPalletID = data.Result.CustomPalletID;
                                $scope.record.AssetQuantity = data.Result.AssetsCount+' / '+data.Result.MaximumAssets ;
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );    
                                $scope.record.ToCustomPalletID = '';
                                $scope.record.AssetQuantity = '';                       
                            }                        
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {          
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });

                } else {
                    $scope.record.ToCustomPalletID = '';
                    $scope.record.AssetQuantity = '';

                    c  
                }
            } else {
                $scope.record.ToCustomPalletID = '';
                $scope.record.AssetQuantity = '';
            }
        };

        $scope.ValidateNotesRequired = function () {
            $scope.record.NotesRequired = false;
            $scope.record.Notes = 'n/a';
            if($scope.record.OverrideReason > 0) {
                if($scope.OverrideReasons) {
                    for(var i=0;i<$scope.OverrideReasons.length;i++) {
                        if($scope.OverrideReasons[i].ReasonID == $scope.record.OverrideReason) {
                            if($scope.OverrideReasons[i].NotesRequired == '1') {
                                $scope.record.NotesRequired = true;
                                $scope.record.Notes = '';                                
                                setTimeout(function () {
                                    $window.document.getElementById("Notes").focus();
                                }, 300);
                            }
                        }
                    }
                }                
            } else {
                $scope.record.NotesRequired = false;
                $scope.record.Notes = 'n/a';
            }
        };

        $scope.ScannedSerials = [];
        $scope.ValidateSerial = function () {
            // Prevent double-clicking by checking if validation is already in progress
            if ($scope.record.validatingSerial) {
                return;
            }

            // Set flag immediately to prevent any double-clicks
            $scope.record.validatingSerial = true;

            //Start check IF Serial already scanned
            if($scope.ScannedSerials.length >= 100) {
                $scope.record.validatingSerial = false; // Reset flag
                $mdToast.show (
                    $mdToast.simple()
                        .content('Asset quantity exceeds its maximum number.Please clear it up or choose another Bin.')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }
            for(var i=0;i<$scope.ScannedSerials.length;i++) {
                if($scope.ScannedSerials[i].SerialNumber == $scope.record.SerialNumber) {
                    $scope.record.validatingSerial = false; // Reset flag
                    $mdToast.show (
                        $mdToast.simple()
                            .content('Serial already Scanned')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                    return;
                }
            }
            //End check IF Serial already scanned
            if($scope.record.FromDispositionID > 0 && $scope.record.ToDispositionID > 0 && $scope.record.Eligible && $scope.record.ToCustomPalletID > 0) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/disposition_override_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ValidateSerial&'+$.param($scope.record),
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.record.validatingSerial = false; // Reset flag

                        if(data.Success) {
                            $scope.record.SerialNumber = '';
                            $window.document.getElementById("SerialNumber").focus();
                            $scope.ScannedSerials.unshift(data.Result);
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.record.validatingSerial = false; // Reset flag on error
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.record.validatingSerial = false; // Reset flag
                $mdToast.show (
                    $mdToast.simple()
                        .content('All inputs are not entered')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


        $scope.CreateDispositionOverride = function () {
            if($scope.record.Eligible) {
                $rootScope.$broadcast('preloader:active');
                $scope.record.busy = true;
                jQuery.ajax({
                    url: host+'administration/includes/disposition_override_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateDispositionOverride&'+$.param($scope.record)+'&OverrideType=SN-Level&PageName=Disposition Override Assets',
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.record.busy = false;
                        if(data.Success) {        
                            if(data.OverrideID) {
                                $scope.record.OverrideID = data.OverrideID;                                
                                setTimeout(function () {
                                    $window.document.getElementById("SerialNumber").focus();
                                }, 100);  
                            }                            
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.record.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                console.log($scope.record);

                $mdToast.show (
                    $mdToast.simple()
                        .content('Facility,Disposition From and Disposition To are not eligible for Disposition Override')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }            
        };


        $scope.DeleteSerial = function (rec,ind,ev) {

            var confirm = $mdDialog.confirm()
            .title('Would you like to delete Serial ?')
            .content('')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('Delete')
            .cancel('No, leave it');
            $mdDialog.show(confirm).then(function() {                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/disposition_override_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteOverrideSerial&RecordID='+rec.RecordID,
                    success: function(data){
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );                            
                            $scope.ScannedSerials.splice(ind,1);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        alert('error');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }, function() {
                //$scope.status = 'You decided to keep your debt.';
            });

        };

        function DispositionOverrideTPVRController($scope,$mdDialog,$mdToast,$window) {            
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };
        }
        $scope.confirmDetails = {};
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();
        }
        $scope.DispositionOverrideTPVR = function (ev) {                        
            $mdDialog.show({
                controller: DispositionOverrideTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose:true
            })
            .then(function(confirmDetails) {
                $rootScope.$broadcast('preloader:active');
                $scope.confirmDetails = confirmDetails;
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',                    
                    data: 'ajax=ValidateDispositionOVerrideController&'+$.param($scope.confirmDetails),
                    success: function(data){
                        if(data.Success) {
                            $scope.confirmDetails.PasswordVerified = true;                            
                            $scope.CompleteDispositionOverrideAssets();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.confirmDetails.PasswordVerified = false;
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.confirmDetails.PasswordVerified = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });

            }, function(confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });
        };


        $scope.CompleteDispositionOverrideAssets = function () {
            $scope.record.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/disposition_override_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CompleteDispositionOverrideAssets&'+$.param($scope.record)+'&'+$.param($scope.confirmDetails),
                success: function(data) {
                    $scope.record.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {  
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );                             
                        $scope.ScannedSerials = [];                        
                        $scope.ValidateBinDisposition();
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                                 
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {   
                    $scope.record.busy = false;       
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.ReloadPage = function () {
            location.reload();
        };

        $scope.UpdateSerialDisposition = function () {
            if($scope.record.disposition_id && $scope.record.BinName && $scope.record.SerialNumber && $scope.record.OverrideReason && $scope.record.RequesterLogin) {
                if($scope.record.disposition_id == '4' && !$scope.record.rule_id) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content('Select Business Rule')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'administration/includes/disposition_override_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateSerialDisposition&'+$.param($scope.record),
                        success: function(data) {
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {
                                $scope.record.SerialNumber = '';
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );                                 
                            }                        
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {          
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });

                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Enter New Disposition,Requester Login,Bin Name, Override Reason and Serial Number')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        $scope.UpdateBulkDisposition = function () {
            if($scope.record.disposition_id_bulk && $scope.record.ToBinName && $scope.record.FromBinName && $scope.record.OverrideReason_bulk && $scope.record.RequesterLogin_bulk) {

                if($scope.record.disposition_id_bulk == '4' && !$scope.record.rule_id_bulk) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content('Select Business Rule')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'administration/includes/disposition_override_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateBulkDisposition&'+$.param($scope.record),
                        success: function(data) {
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {
                                $scope.record.FromBinName = '';
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );                                 
                            }                        
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {          
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }

            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Enter New Disposition,Requester Login,Override Reason, From and To Bins')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


    });


    angular.module('app').controller("disposition_override_bulk", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Disposition Override Bulk',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.record = {'Eligible': false,'NotesRequired': false};
        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.AllDispositions = [];
        $scope.BusinessRules = [];
        $scope.OverrideReasons = [];
        $scope.UserInputs = []; 
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllDispositions',
            success: function(data) {
                $rootScope.$broadcast('preloader:hide');
                if(data.Success) {
                   $scope.AllDispositions = data.Result;                       
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );    
                    $scope.AllDispositions = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllUserInputs',
            success: function(data) {                
                if(data.Success) {
                   $scope.UserInputs = data.Result;                       
                } else {                        
                    $scope.UserInputs = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {                            
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositionOverrideReasons',
            success: function(data) {                
                if(data.Success) {
                   $scope.OverrideReasons = data.Result;                       
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );    
                    $scope.OverrideReasons = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {                          
                initSessionTime(); $scope.$apply();
            }
        });


        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetLoggedinUserDetails',
            success: function(data) {                
                if(data.Success) {
                    $scope.record.RequesterLogin = data.Result.FirstName + ' '+data.Result.LastName;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );                        
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetBusinessRuleNames',
            success: function(data) {                
                if(data.Success) {
                   $scope.BusinessRules = data.Result;                       
                } else {
                    // $mdToast.show (
                    //     $mdToast.simple()
                    //         .content(data.Result)
                    //         .action('OK')
                    //         .position('right')
                    //         .hideDelay(0)
                    //         .toastClass('md-toast-danger md-block')
                    // );    
                    $scope.BusinessRules = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.CheckDispositionOverrideEligibility = function () {
            if($scope.record.FromDispositionID > 0 && $scope.record.ToDispositionID > 0) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/disposition_override_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CheckDispositionOverrideEligibility&DispositionFrom='+$scope.record.FromDispositionID+'&DispositionTo='+$scope.record.ToDispositionID+'&EligibilityType=Bin-Level',
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {                            
                            $scope.record.Eligible = true;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );    
                            $scope.record.Eligible = false;                              
                        }                        
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {          
                        $rootScope.$broadcast('preloader:hide');     
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.record.Eligible = false;
            }
        };

        $scope.ValidateFromBinDisposition = function () {
            if($scope.record.FromBinName != '') {
                if($scope.record.FromDispositionID > 0) {

                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'administration/includes/disposition_override_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateFromBinDisposition&DispositionFrom='+$scope.record.FromDispositionID+'&BinName='+$scope.record.FromBinName,
                        success: function(data) {
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {    

                                $scope.record.FromCustomPalletID = data.Result.CustomPalletID;
                                $scope.record.FromAssetQuantity = data.Result.AssetsCount+' / '+data.Result.MaximumAssets ;
                                $scope.record.FromAssetsCount = data.Result.AssetsCount;
                                $scope.record.FromAssetsMaximumAssets = data.Result.MaximumAssets;

                                if($scope.record.ToCustomPalletID > 0) {
                                    if($scope.record.FromAssetsCount > ($scope.record.ToAssetsMaximumAssets - $scope.record.ToAssetsCount)) {
                                        $mdToast.show (
                                            $mdToast.simple()
                                                .content('Asset Qty exceeds its maximum number. Please clear it up or choose another Bin.')
                                                .action('OK')
                                                .position('right')
                                                .hideDelay(0)
                                                .toastClass('md-toast-danger md-block')
                                        );
                                    }
                                }
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );    
                                $scope.record.FromCustomPalletID = '';
                                $scope.record.FromAssetQuantity = '';                       
                                $scope.record.FromAssetsCount = '';
                                $scope.record.FromAssetsMaximumAssets = '';
                            }                        
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {          
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });

                } else {
                    $scope.record.FromCustomPalletID = '';
                    $scope.record.FromAssetQuantity = '';
                    $scope.record.FromAssetsCount = '';
                    $scope.record.FromAssetsMaximumAssets = '';                    
                }
            } else {
                $scope.record.FromCustomPalletID = '';
                $scope.record.FromAssetQuantity = '';
                $scope.record.FromAssetsCount = '';
                $scope.record.FromAssetsMaximumAssets = '';
            }
        };

        $scope.ValidateBinDisposition = function () {
            if($scope.record.BinName != '') {
                if($scope.record.ToDispositionID > 0) {

                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'administration/includes/disposition_override_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateBinDisposition&DispositionTo='+$scope.record.ToDispositionID+'&BinName='+$scope.record.BinName,
                        success: function(data) {
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {                                                            
                                $scope.record.ToCustomPalletID = data.Result.CustomPalletID;
                                $scope.record.AssetQuantity = data.Result.AssetsCount+' / '+data.Result.MaximumAssets ;
                                $scope.record.ToAssetsCount = data.Result.AssetsCount;
                                $scope.record.ToAssetsMaximumAssets = data.Result.MaximumAssets;

                                if($scope.record.FromCustomPalletID > 0) {
                                    if($scope.record.FromAssetsCount > ($scope.record.ToAssetsMaximumAssets - $scope.record.ToAssetsCount)) {
                                        $mdToast.show (
                                            $mdToast.simple()
                                                .content('Asset Qty exceeds its maximum number. Please clear it up or choose another Bin.')
                                                .action('OK')
                                                .position('right')
                                                .hideDelay(0)
                                                .toastClass('md-toast-danger md-block')
                                        );
                                    }
                                }

                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );    
                                $scope.record.ToCustomPalletID = '';
                                $scope.record.AssetQuantity = '';
                                $scope.record.ToAssetsCount = '';
                                $scope.record.ToAssetsMaximumAssets = '';
                            }                        
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {          
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });

                } else {
                    $scope.record.ToCustomPalletID = '';
                    $scope.record.AssetQuantity = '';
                    $scope.record.ToAssetsCount = '';
                    $scope.record.ToAssetsMaximumAssets = '';
                }
            } else {
                $scope.record.ToCustomPalletID = '';
                $scope.record.AssetQuantity = '';
                $scope.record.ToAssetsCount = '';
                $scope.record.ToAssetsMaximumAssets = '';
            }
        };        
        
        $scope.ValidateNotesRequired = function () {
            $scope.record.NotesRequired = false;
            $scope.record.Notes = 'n/a';
            if($scope.record.OverrideReason > 0) {
                if($scope.OverrideReasons) {
                    for(var i=0;i<$scope.OverrideReasons.length;i++) {
                        if($scope.OverrideReasons[i].ReasonID == $scope.record.OverrideReason) {
                            if($scope.OverrideReasons[i].NotesRequired == '1') {
                                $scope.record.NotesRequired = true;
                                $scope.record.Notes = '';                                
                                setTimeout(function () {
                                    $window.document.getElementById("Notes").focus();
                                }, 300);
                            }
                        }
                    }
                }                
            } else {
                $scope.record.NotesRequired = false;
                $scope.record.Notes = 'n/a';
            }
        };

        function DispositionOverrideTPVRController($scope,$mdDialog,$mdToast,$window) {            
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };
        }
        $scope.confirmDetails = {};
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();
        }
        $scope.DispositionOverrideTPVR = function (ev) {     
            if($scope.record.FromDispositionID > 0 && $scope.record.ToDispositionID > 0 && $scope.record.Eligible && $scope.record.ToCustomPalletID > 0 && $scope.record.FromCustomPalletID > 0) {                
                if($scope.record.FromAssetsCount > ($scope.record.ToAssetsMaximumAssets - $scope.record.ToAssetsCount)) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content('Asset Qty exceeds its maximum number. Please clear it up or choose another Bin.')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {

                    $mdDialog.show({
                        controller: DispositionOverrideTPVRController,
                        templateUrl: 'password.html',
                        parent: angular.element(document.body),
                        targetEvent: ev,
                        onComplete: afterShowAnimation,
                        clickOutsideToClose:true
                    })
                    .then(function(confirmDetails) {
                        $rootScope.$broadcast('preloader:active');
                        $scope.confirmDetails = confirmDetails;
                        jQuery.ajax({
                            url: host + 'receive/includes/receive_submit.php',
                            dataType: 'json',
                            type: 'post',                    
                            data: 'ajax=ValidateDispositionOVerrideController&'+$.param($scope.confirmDetails),
                            success: function(data){
                                if(data.Success) {
                                    $scope.confirmDetails.PasswordVerified = true;                            
                                    $scope.CompleteDispositionOverrideBulk();
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                    $scope.confirmDetails.PasswordVerified = false;
                                }
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();
                            }, error : function (data) {
                                $scope.confirmDetails.PasswordVerified = false;
                                $rootScope.$broadcast('preloader:hide');
                                $scope.data = data;
                                initSessionTime(); $scope.$apply();
                            }
                        });
        
                    }, function(confirmDetails) {
                        $scope.confirmDetails = confirmDetails;
                    });

                }
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('All inputs are not validated')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }            
        };


        $scope.CompleteDispositionOverrideBulk = function () {
            $scope.record.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/disposition_override_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CompleteDispositionOverrideBulk&'+$.param($scope.record)+'&'+$.param($scope.confirmDetails),
                success: function(data) {
                    $scope.record.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {  
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );                                       
                        $scope.ValidateBinDisposition();
                        $scope.record.FromCustomPalletID = '';
                        $scope.record.FromAssetQuantity = '';   
                        $scope.record.FromAssetsCount = '';
                        $scope.record.FromAssetsMaximumAssets = '';
                        $scope.record.FromBinName = '';
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                                 
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {   
                    $scope.record.busy = false;       
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.ReloadPage = function () {
            location.reload();
        };
    });


    angular.module('app').controller("disposition_override_container", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Disposition Override Container',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.record = {'Eligible': false,'NotesRequired': false,'ValidContainerID': false};
        $scope.AllDispositions = [];        
        $scope.OverrideReasons = [];
        $scope.UserInputs = [];
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllDispositions',
            success: function(data) {
                $rootScope.$broadcast('preloader:hide');
                if(data.Success) {
                   $scope.AllDispositions = data.Result;                       
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );    
                    $scope.AllDispositions = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });


        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllUserInputs',
            success: function(data) {                
                if(data.Success) {
                   $scope.UserInputs = data.Result;                       
                } else {                        
                    $scope.UserInputs = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {                            
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositionOverrideReasons',
            success: function(data) {                
                if(data.Success) {
                   $scope.OverrideReasons = data.Result;                       
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );    
                    $scope.OverrideReasons = [];               
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {                          
                initSessionTime(); $scope.$apply();
            }
        });


        jQuery.ajax({
            url: host+'administration/includes/disposition_override_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetLoggedinUserDetails',
            success: function(data) {                
                if(data.Success) {
                    $scope.record.RequesterLogin = data.Result.FirstName + ' '+data.Result.LastName;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );                        
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });        

        $scope.CheckDispositionOverrideEligibility = function () {
            if($scope.record.FromDispositionID > 0 && $scope.record.ToDispositionID > 0) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/disposition_override_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CheckDispositionOverrideEligibility&DispositionFrom='+$scope.record.FromDispositionID+'&DispositionTo='+$scope.record.ToDispositionID+'&EligibilityType=Container-Level',
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {                            
                            $scope.record.Eligible = true;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );    
                            $scope.record.Eligible = false;                              
                        }                        
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {          
                        $rootScope.$broadcast('preloader:hide');     
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.record.Eligible = false;
            }
        };

        $scope.ValidateShippingContainerDisposition = function () {
            if($scope.record.ShippingContainerID != '') {
                if($scope.record.FromDispositionID > 0) {

                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'administration/includes/disposition_override_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateShippingContainerDisposition&DispositionFrom='+$scope.record.FromDispositionID+'&ShippingContainerID='+$scope.record.ShippingContainerID,
                        success: function(data) {
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {                                
                                $scope.record.ValidContainerID = true;
                                $scope.record.ContainerAssetQuantity = data.Result.ContainerAssetQuantity;
                                if(data.Result.GroupName) {
                                    $scope.record.GroupName = data.Result.GroupName;
                                }
                                if(data.Result.group) {
                                    $scope.record.group = data.Result.group;
                                }
                                if(data.Result.LocationName) {
                                    $scope.record.LocationName = data.Result.LocationName;
                                }
                                $scope.record.FacilityID = data.Result.FacilityID;
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );    
                                $scope.record.ValidContainerID = false;
                                $scope.record.ContainerAssetQuantity = '';   
                                $scope.record.GroupName = '';
                                $scope.record.group = '';     
                                $scope.record.LocationName = '';
                                $scope.record.FacilityID = '';
                            }                        
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {          
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });

                } else {
                    $scope.record.ValidContainerID = false;
                    $scope.record.ContainerAssetQuantity = '';
                    $scope.record.GroupName = '';
                    $scope.record.group = '';     
                    $scope.record.LocationName = '';
                    $scope.record.FacilityID = '';
                }
            } else {
                $scope.record.ValidContainerID = false;
                $scope.record.ContainerAssetQuantity = '';
                $scope.record.GroupName = '';
                $scope.record.group = '';     
                $scope.record.LocationName = '';
                $scope.record.FacilityID = '';
            }
        };  
        
        $scope.ClearContainer = function () {
            $scope.record.ValidContainerID = false;
            $scope.record.ContainerAssetQuantity = '';
            $scope.record.GroupName = '';
            $scope.record.group = '';     
            $scope.record.LocationName = '';
            $scope.record.FacilityID = '';
        };


        function LocationChange1(text) {
            $scope.record.group = text;
        }

        function selectedLocationChange1(item) {
            if (item) {
                if (item.value) {
                    $scope.record.group = item.value;
                } else {
                    $scope.record.group = '';
                }
            } else {
                $scope.record.group = '';
            }
        }

        $scope.queryLocationSearch1 = queryLocationSearch1;
        $scope.LocationChange1 = LocationChange1;
        $scope.selectedLocationChange1 = selectedLocationChange1;
        function queryLocationSearch1(query) {
            if (query) {
                if (query != '' && query != 'undefined') {                    
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + $scope.record.FacilityID+'&LocationType=Outbound Storage')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }




        $scope.ValidateNotesRequired = function () {
            $scope.record.NotesRequired = false;
            $scope.record.Notes = 'n/a';
            if($scope.record.OverrideReason > 0) {
                if($scope.OverrideReasons) {
                    for(var i=0;i<$scope.OverrideReasons.length;i++) {
                        if($scope.OverrideReasons[i].ReasonID == $scope.record.OverrideReason) {
                            if($scope.OverrideReasons[i].NotesRequired == '1') {
                                $scope.record.NotesRequired = true;
                                $scope.record.Notes = '';                                
                                setTimeout(function () {
                                    $window.document.getElementById("Notes").focus();
                                }, 300);
                            }
                        }
                    }
                }                
            } else {
                $scope.record.NotesRequired = false;
                $scope.record.Notes = 'n/a';
            }
        };        

        function DispositionOverrideTPVRController($scope,$mdDialog,$mdToast,$window) {            
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };
        }
        $scope.confirmDetails = {};
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();
        }
        $scope.DispositionOverrideTPVR = function (ev) {     

            $rootScope.$broadcast('preloader:active');            
            jQuery.ajax({
                url: host+'administration/includes/disposition_override_submit.php',
                dataType: 'json',
                type: 'post',                    
                data: 'ajax=ValidateContainerSeal&'+$.param($scope.record),
                success: function(data){
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        
                        if($scope.record.FromDispositionID > 0 && $scope.record.ToDispositionID > 0 && $scope.record.Eligible && $scope.record.ValidContainerID) {

                            $mdDialog.show({
                                controller: DispositionOverrideTPVRController,
                                templateUrl: 'password.html',
                                parent: angular.element(document.body),
                                targetEvent: ev,
                                onComplete: afterShowAnimation,
                                clickOutsideToClose:true
                            })
                            .then(function(confirmDetails) {
                                $rootScope.$broadcast('preloader:active');
                                $scope.confirmDetails = confirmDetails;
                                jQuery.ajax({
                                    url: host + 'receive/includes/receive_submit.php',
                                    dataType: 'json',
                                    type: 'post',                    
                                    data: 'ajax=ValidateDispositionOVerrideController&'+$.param($scope.confirmDetails),
                                    success: function(data){
                                        if(data.Success) {
                                            $scope.confirmDetails.PasswordVerified = true;                            
                                            $scope.CompleteDispositionOverrideContainer();
                                        } else {
                                            $mdToast.show(
                                                $mdToast.simple()
                                                    .content(data.Result)
                                                    .action('OK')
                                                    .position('right')
                                                    .hideDelay(0)
                                                    .toastClass('md-toast-danger md-block')
                                            );
                                            $scope.confirmDetails.PasswordVerified = false;
                                        }
                                        $rootScope.$broadcast('preloader:hide');
                                        initSessionTime(); $scope.$apply();
                                    }, error : function (data) {
                                        $scope.confirmDetails.PasswordVerified = false;
                                        $rootScope.$broadcast('preloader:hide');
                                        $scope.data = data;
                                        initSessionTime(); $scope.$apply();
                                    }
                                });
                
                            }, function(confirmDetails) {
                                $scope.confirmDetails = confirmDetails;
                            });
            
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content('All inputs are not validated')
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }


                        
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                        
                    }                    
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {                    
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });                        
        };


        $scope.CompleteDispositionOverrideContainer = function () {
            $scope.record.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/disposition_override_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CompleteDispositionOverrideContainer&'+$.param($scope.record)+'&'+$.param($scope.confirmDetails),
                success: function(data) {
                    $scope.record.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {  
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );                                       
                        location.reload();
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                                 
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {   
                    $scope.record.busy = false;       
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.ReloadPage = function () {
            location.reload();
        };
    });


})(); 