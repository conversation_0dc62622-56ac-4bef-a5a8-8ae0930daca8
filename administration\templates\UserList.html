<div ng-controller = "UserList" class="page">

    <div ng-show="loading" class="loading" style="text-align:center;"><img src="../images/loading2.gif" /> LOADING...</div>

    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="UserList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                               <!--  <i class="material-icons md-primary" ng-show="UserList">keyboard_arrow_up</i>
                                <i class="material-icons md-primary" ng-show="! UserList">keyboard_arrow_down</i>
                                <span>User List</span> -->

                                <i ng-click="UserList = !UserList" class="material-icons md-primary" ng-show="UserList">keyboard_arrow_up</i>
                                <i ng-click="UserList = !UserList" class="material-icons md-primary" ng-show="! UserList">keyboard_arrow_down</i>
                                <span ng-click="UserList = !UserList">User List</span>

                                <div flex></div>
                                <a href="#!/UserList" ng-click="UserListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>
                                <div class="upload-btn-wrapper text-center mt-5">
                                    <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                                    <input type="file" ng-file-select="onFileSelect($files)" id="UserFile">  
                                    <a href="../../sample_files/upload_UserList_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                                </div> 

                                <a href="#!/UserList" ng-click="UserListxls()" class="md-button md-raised md-default dis_open_v mr-5" style="min-width: 40px; display: none;">
                                    <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon>
                                 </a>

                                <a href="#!/User" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New User
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="UserList">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>
                                    
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                         
                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('UserName')" ng-class="{'orderby' : OrderBy == 'UserName'}">
                                                        <div>                               
                                                            User Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'UserName'"></i>                                 
                                                            <span ng-show="OrderBy == 'UserName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                                                                                                            
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FirstName')" ng-class="{'orderby' : OrderBy == 'FirstName'}">                           
                                                        <div>                               
                                                            FirstName <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FirstName'"></i>                                    
                                                            <span ng-show="OrderBy == 'FirstName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LastName')" ng-class="{'orderby' : OrderBy == 'LastName'}">                          
                                                        <div>                               
                                                            LastName <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LastName'"></i>                                  
                                                            <span ng-show="OrderBy == 'LastName'">                                  
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('Email')" ng-class="{'orderby' : OrderBy == 'Email'}">                          
                                                        <div>                               
                                                            Email <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Email'"></i>                                    
                                                            <span ng-show="OrderBy == 'Email'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                          
                                                        <div>                               
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                    
                                                            <span ng-show="OrderBy == 'FacilityName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ProfileName')" ng-class="{'orderby' : OrderBy == 'ProfileName'}">                           
                                                        <div>                               
                                                            Profile <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ProfileName'"></i>                                    
                                                            <span ng-show="OrderBy == 'ProfileName'">                                   
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> 
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ProgramName')" ng-class="{'orderby' : OrderBy == 'ProgramName'}">                         
                                                        <div style="min-width: 120px;">                               
                                                            Program <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ProgramName'"></i>                                  
                                                            <span ng-show="OrderBy == 'ProgramName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('MangerName')" ng-class="{'orderby' : OrderBy == 'MangerName'}">                         
                                                        <div style="min-width: 120px;">                               
                                                            Manager <!--<i class="fa fa-sort pull-right" ng-show="OrderBy != 'MangerName'"></i>                                  
                                                            <span ng-show="OrderBy == 'MangerName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>-->
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Shift')" ng-class="{'orderby' : OrderBy == 'Shift'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Shift <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Shift'"></i>                                  
                                                            <span ng-show="OrderBy == 'Shift'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('AccountType')" ng-class="{'orderby' : OrderBy == 'AccountType'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Accout Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'AccountType'"></i>                                  
                                                            <span ng-show="OrderBy == 'AccountType'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>                                  
                                                            <span ng-show="OrderBy == 'StatusName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="UserName" ng-model="filter_text[0].UserName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FirstName" ng-model="filter_text[0].FirstName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LastName" ng-model="filter_text[0].LastName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Email" ng-model="filter_text[0].Email" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ProfileName" ng-model="filter_text[0].ProfileName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ProgramName" ng-model="filter_text[0].ProgramName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="MangerName" ng-model="filter_text[0].MangerName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Shift" ng-model="filter_text[0].Shift" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="AccountType" ng-model="filter_text[0].AccountType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                                                                  
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                       
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/User/{{product.UserId}}"><md-icon class="material-icons text-danger">edit</md-icon></a></td>

                                                    <td>
                                                        {{product.UserName}}                            
                                                    </td>                       
                                                    <td>
                                                        {{product.FirstName}}
                                                    </td>
                                                    <td>
                                                        {{product.LastName}}
                                                    </td>
                                                    <!-- <td>
                                                        {{product.Email}}
                                                    </td> -->
                                                    <td>
                                                        {{product.FacilityName}}
                                                    </td>

                                                    <td>
                                                        {{product.ProfileName}}
                                                    </td>
                                                    <td>
                                                        {{product.ProgramName}}
                                                    </td>
                                                    <td>
                                                        {{product.MangerName}}
                                                    </td>
                                                    <td>
                                                        {{product.Shift}}
                                                    </td>
                                                    <td>
                                                        {{product.AccountType}}
                                                    </td>
                                                    <td>
                                                        {{product.StatusName}}
                                                    </td>
                                                    <!-- <td>
                                                        <span ng-if="product.Status == '1'">Active</span>
                                                        <span ng-if="product.Status != '1'">Inactive</span>
                                                    </td> -->                   
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="8">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>