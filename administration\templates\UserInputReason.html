<div class="row page" data-ng-controller="DispositionOverrideUserInputReasons">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>User Input Reason Creation</span>
                        <div flex></div>
                           <a href="#!/UserInputReasonList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                                <div class="col-md-4">
                                    <md-input-container class="md-block">
                                        <label>User Input</label>
                                        <input type="text" name="UserInput"  ng-model="UserInput['UserInput']"  required ng-maxlength="200" />
                                         <div class="error-sapce">
                                        <div ng-messages="material_signup_form.UserInput.$error" multiple ng-if='material_signup_form.UserInput.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 200.</div>                           
                                        </div>
                                    </div>
                                    </md-input-container>
                                </div>

                                 <div class="col-md-4">
                                    <md-switch class="mt-10" ng-model="UserInput.ActivateReasonCode" aria-label="Activate Reason Code" ng-true-value="'1'" ng-false-value="'0'"> Activate as Reason Code</md-switch>
                                </div>

                                <div class="col-md-4">
                                    <md-switch class="mt-10" ng-model="UserInput.NotesRequired" aria-label="Notes Required" ng-true-value="'1'" ng-false-value="'0'"> Notes Required</md-switch>
                                </div>

                                <div class="col-md-12 btns-row">
                                    <a href="#!/UserInputReasonList" style="text-decoration: none;">
                                        <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                    </a>
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="material_signup_form.$invalid" ng-click="UserInputReasonSave()">
                                    <span ng-show="! UserInput.busy">Save</span>
                                    <span ng-show="UserInput.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                                </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>