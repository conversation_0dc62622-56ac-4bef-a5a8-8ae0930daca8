(function () {
    'use strict';
    angular.module('app').controller("bin_audit_controls", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Bin Audit Controls',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
        
        $scope.bin_audit_schedule = {'Accuracy': 100};
        $scope.bin_audit_targeted_bin = {'Accuracy': 100};

        $scope.AllDispositions = [];
        $scope.Facilities = [];
        $scope.PartTypes = [];
        $rootScope.$broadcast('preloader:active');

        jQuery.ajax({
            url: host+'administration/includes/bin_audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPartTypes',
            success: function (data) {
                if (data.Success) {
                    $scope.PartTypes = data.Result;
                } else {
                    $scope.PartTypes = [];
                }                
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/bin_audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                    if($scope.Facilities.length == '1') {
                        $scope.bin_audit_schedule.FacilityID = $scope.Facilities[0]['FacilityID'];
                        $scope.bin_audit_targeted_bin.FacilityID = $scope.Facilities[0]['FacilityID'];
                    }
                } else {
                    $scope.Facilities = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });



        jQuery.ajax({
            url: host+'administration/includes/bin_audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllDispositions',
            success: function(data) {
                $rootScope.$broadcast('preloader:hide');
                if(data.Success) {
                   $scope.AllDispositions = data.Result;                       
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );    
                    $scope.AllDispositions = [];
                }                        
                initSessionTime(); $scope.$apply();
            }, error : function (data) {          
                $rootScope.$broadcast('preloader:hide');     
                initSessionTime(); $scope.$apply();
            }
        });


        $scope.SaveBinAuditSchedule = function () {
            $scope.bin_audit_schedule.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/bin_audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SaveBinAuditSchedule&'+$.param($scope.bin_audit_schedule),
                success: function (data) {
                    if (data.Success) {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        window.location = "#!/BinAuditControls";
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );

                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    $scope.bin_audit_schedule.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.bin_audit_schedule.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        $scope.SaveBinAuditTarget = function () {
            $scope.bin_audit_targeted_bin.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/bin_audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SaveBinAuditTarget&'+$.param($scope.bin_audit_targeted_bin),
                success: function (data) {
                    if (data.Success) {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        window.location = "#!/BinAuditControls";
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );

                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    $scope.bin_audit_targeted_bin.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.bin_audit_targeted_bin.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        if($stateParams.ControlID) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/bin_audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetAuditControlDetails&ControlID='+$stateParams.ControlID,
                success: function(data){
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    if(data.Success) {   
                        if(data.Result.CustomPalletID > 0) {
                            $scope.bin_audit_targeted_bin = data.Result;
                        } else {
                            $scope.bin_audit_schedule = data.Result;
                        }
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );

                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }
        

    });


    angular.module('app').controller("BinAuditControlList", function ($scope,$location,$http,$rootScope,$mdToast) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Bin Audit Controls',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.busy = false;
        $scope.RemovalCodeList = [];
        $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];


        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if($scope.CurrentStatus != '' )  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/bin_audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetBinAuditControlsList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                            var op = data.Result.split(' ');                            
                            if( op[0] == "No" && op[1] == 'Access') {
                                window.location = host;
                            }
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'administration/includes/bin_audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetBinAuditControlsList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );

                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic        
    });


    angular.module('app').controller("BinAuditBins", function ($scope,$location,$http,$rootScope,$mdToast,$mdDialog,$window) {
        $scope.busy = false;
        $scope.RemovalCodeList = [];
        $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];


        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if($scope.CurrentStatus != '' )  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/bin_audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetBinAuditBinsList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );

                            var op = data.Result.split(' ');                            
                            if( op[0] == "No" && op[1] == 'Access') {
                                window.location = host;
                            }
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'administration/includes/bin_audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetBinAuditBinsList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );

                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic    
        
        
        $scope.RemoveBinFromAuditList = function (bin,ev) {

            if($scope.filter_text[0].BinName != '' && $scope.filter_text[0].BinName) {

                if($scope.filter_text[0].BinName == bin.BinName) {

                    var confirm = $mdDialog.confirm()
                        .title('Are you sure, You want to remove BIN ('+bin.BinName+') from Pending Bin Audit list?')
                        .content('')
                        .ariaLabel('Lucky day')
                        .targetEvent(ev)
                        .ok('Remove')
                        .cancel('Cancel');
                    $mdDialog.show(confirm).then(function () {

                        $scope.CurrentShipment = {};
                        $rootScope.$broadcast('preloader:active');                
                        jQuery.ajax({                        
                            url: host+'administration/includes/bin_audit_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=RemoveBinFromAuditList&' + $.param(bin),
                            success: function (data) {                        
                                $rootScope.$broadcast('preloader:hide');
                                if (data.Success) {                                
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );
                                    $scope.filter_text[0].BinName = '';
                                    $scope.CallServerFunction($scope.currentPage);                                                            
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );

                                    var op = data.Result.split(' ');                            
                                    if( op[0] == "No" && op[1] == 'Access') {
                                        window.location = host;
                                    }
                                }                            
                                initSessionTime(); $scope.$apply();;
                            }, error: function (data) {                        
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();;
                            }
                        });

                    }, function () {

                    });

                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched BinName is different from Processed Bin')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("BinName_filter").focus();    
                }
            }  else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter BinName in Bin Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("BinName_filter").focus();
            }

        };

    });


})(); 