<div ng-controller = "LockedSourceBinsList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="BinList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">    
                                 <i ng-click="BinList = !BinList" class="material-icons md-primary" ng-show="BinList">keyboard_arrow_up</i>
                                <i ng-click="BinList = !BinList" class="material-icons md-primary" ng-show="! BinList">keyboard_arrow_down</i>
                                <span ng-click="BinList = !BinList">Locked Source Bins</span>                                                                
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="BinList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div> 
                                                    
                                        <table class="table table-striped">
                                            <thead>
                                                <tr class="th_sorting">                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('BinName')" ng-class="{'orderby' : OrderBy == 'BinName'}">
                                                        <div>                               
                                                            Bin Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'BinName'"></i>                                 
                                                            <span ng-show="OrderBy == 'BinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                                                                                                            
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}">                           
                                                        <div>                               
                                                            Locked Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>                                    
                                                            <span ng-show="OrderBy == 'CreatedDate'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('UserName')" ng-class="{'orderby' : OrderBy == 'UserName'}">                           
                                                        <div>                               
                                                            Locked By <i class="fa fa-sort pull-right" ng-show="OrderBy != 'UserName'"></i>                                    
                                                            <span ng-show="OrderBy == 'UserName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>  
                                                    <th>
                                                        <div>Unlock</div>
                                                    </th>                                                  
                                                </tr>
                                                
                                                <tr class="errornone">                                                      
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="BinName" ng-model="filter_text[0].BinName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="UserName" ng-model="filter_text[0].UserName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                                                   
                                                    <td></td>                                                                                                       
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">                                                    
                                                    <td>
                                                        {{product.BinName}}
                                                    </td> 
                                                    <td>
                                                        {{product.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}                            
                                                    </td>                       
                                                    <td>
                                                        {{product.UserName}}                            
                                                    </td>                                                  
                                                    <td>
                                                        <button class="md-button md-raised md-primary" style="min-height: 30px; min-width: 100px;" ng-click="UnlockSourceBIN(product,$event,$index)" >
                                                            Unlock
                                                        </button>
                                                    </td>                                                    
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="4">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>
                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>                
                    </md-card>
                </div>
            </article>
        </div>
    </div>
</div>