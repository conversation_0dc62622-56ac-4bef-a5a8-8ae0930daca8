<div ng-controller = "WarrantySerialList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="FleetriskList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="FleetriskList = !FleetriskList" class="material-icons md-primary" ng-show="FleetriskList">keyboard_arrow_up</i>
                                <i ng-click="FleetriskList = !FleetriskList" class="material-icons md-primary" ng-show="! FleetriskList">keyboard_arrow_down</i>
                                <span ng-click="FleetriskList = !FleetriskList">Warranty Serials</span>
                                <div flex></div> 
                               
                                
                                
                                <div>
                                    <div class="upload-btn-wrapper text-center mt-10">
                                        <md-button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right; line-height: 26px;">
                                            <span ng-show="! busy">
                                                <i class="material-icons mr-5" style="margin-top: 2px; float: left;">file_upload</i>Upload File
                                            </span>
                                            
                                            <span ng-show="busy">
                                                <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular>
                                            </span>
                                        </md-button>                           
                                        <input type="file" ng-file-select="onFileSelect($files)" id="WarrantySerialFile">
                                        <a href="../../sample_files/WarrantySerials.xlsx" target="_blank" class="md-button btn-w-md mr-5" style="float: right; margin-top: 0px; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a>                                        
                                    </div>
                                </div>
                                <a href="#!/WarrantySerial" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-top: 2px;">
                                    <i class="material-icons">add</i> Create New
                                </a>

                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="FleetriskList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div> 
                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SerialNumber')" ng-class="{'orderby' : OrderBy == 'SerialNumber'}">
                                                        <div>                               
                                                            Serial Number<i class="fa fa-sort pull-right" ng-show="OrderBy != 'SerialNumber'"></i>                                 
                                                            <span ng-show="OrderBy == 'SerialNumber'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('rack_asset_id')" ng-class="{'orderby' : OrderBy == 'rack_asset_id'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Rack Asset ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rack_asset_id'"></i>                                  
                                                            <span ng-show="OrderBy == 'rack_asset_id'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('host_id')" ng-class="{'orderby' : OrderBy == 'host_id'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Host ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'host_id'"></i>                                  
                                                            <span ng-show="OrderBy == 'host_id'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('host_asset_id')" ng-class="{'orderby' : OrderBy == 'host_asset_id'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Host Asset ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'host_asset_id'"></i>                                  
                                                            <span ng-show="OrderBy == 'host_asset_id'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('warranty_expiration_date')" ng-class="{'orderby' : OrderBy == 'warranty_expiration_date'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Warranty Expiration Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'warranty_expiration_date'"></i>                                  
                                                            <span ng-show="OrderBy == 'warranty_expiration_date'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SerialNumber" ng-model="filter_text[0].SerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rack_asset_id" ng-model="filter_text[0].rack_asset_id" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="host_id" ng-model="filter_text[0].host_id" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="host_asset_id" ng-model="filter_text[0].host_asset_id" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="warranty_expiration_date" ng-model="filter_text[0].warranty_expiration_date" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/WarrantySerial/{{product.warranty_id}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a></td>
                                                    <td>
                                                        {{product.SerialNumber}}                            
                                                    </td>                       

                                                    <td>
                                                        {{product.rack_asset_id}}                                                        
                                                    </td>

                                                    <td>
                                                        {{product.host_id}}
                                                    </td>
                                                    <td>
                                                        {{product.host_asset_id}}
                                                    </td>

                                                    <td>
                                                        {{product.warranty_expiration_date | toDate | date:'MMM dd, yyyy'}}
                                                    </td>

                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="9">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>