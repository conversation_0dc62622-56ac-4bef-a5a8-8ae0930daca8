<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['parttypeListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "ParttypeList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Parttype List');
$header = array('Facility','PartType','Serialized','Description','Status');

$sql = "select us.*,ss.StatusName,F.FacilityName from parttype us left join statusses ss on us.Status = ss.StatusID left join facility F on us.FacilityID = F.FacilityID where us.AccountID = '" . $_SESSION['user']['AccountID'] . "' AND us.FacilityID = '" . $_SESSION['user']['FacilityID'] . "'";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                    if ($key == 'parttype') {
							$query = $query . " AND us.parttype like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
						if ($key == 'serialized') {
							$query = $query . " AND us.serialized like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
						if ($key == 'Description') {
							$query = $query . " AND us.Description like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
						if ($key == 'StatusName') {
							$query = $query . " AND ss.StatusName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
						if ($key == 'FacilityName') {
								$query = $query . " AND F.FacilityName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
						}
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

           if ($data['OrderBy'] == 'parttype') {
					$query = $query . " order by us.parttype " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'serialized') {
					$query = $query . " order by us.serialized " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Description') {
					$query = $query . " order by us.Description " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StatusName') {
					$query = $query . " order by ss.StatusName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by F.FacilityName " . $order_by_type . " ";
				}
        } else {
            $sql = $sql . " order by us.parttype desc ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
while($row = mysqli_fetch_assoc($query))
{
    $row2  = array($row['FacilityName'],$row['parttype'],$row['serialized'],$row['Description'],$row['StatusName']);
    $rows[] = $row2;
}

$sheet_name = 'Parttype List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 