<div class="page" data-ng-controller="PartsRecoveryInfo1">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">  
                <style>
                    @media (min-width: 960px) {
                        .md-tooltip.md-origin-top {
                            margin-top: -2px;
                        }
                    }
                </style>
                
                <script type="text/ng-template" id="password.html">

                    <div style="max-width:767px">
  
                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>Inbound Storage Location</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>
  
                        <md-dialog-content>
                            <div class="md-dialog-content">
  
                                <div class="form-horizontal verification-form">
                                    <form name="tpvForm">
                                      <div class="autocomplete insideuse">
                                          <md-autocomplete required
                                              id="locationAutoComplete"
                                              md-no-cache="noCache"
                                              md-search-text-change="ContainerLocationChange(CurrentPallet.location,CurrentPallet)"
                                              md-search-text="CurrentPallet.location"
                                              md-items="item in queryContainerLocationSearch(CurrentPallet.location,CurrentPallet)"
                                              md-item-text="item.LocationName"
                                              md-selected-item-change="selectedContainerLocationChange(item,CurrentPallet)"
                                              md-min-length="0"
                                              ng-model-options='{ debounce: 1000 }'
                                              placeholder="Search Location">
                                              <md-item-template>
                                                  <span md-highlight-text="CurrentPallet.location" md-highlight-flags="^i">{{item.LocationName}}</span>
                                              </md-item-template>
                                              <md-not-found>
                                                  No Records matching "{{pallet.location}}" were found.
                                              </md-not-found>
                                          </md-autocomplete>
                                      </div>
                                    </form>
                                </div>
  
                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                  <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button>
                                    <!--   <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="ProcessPendingDestruction($event)" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                </div>
                        <md-dialog-content>
                    </div>
                </script>
                
                <script type="text/ng-template" id="password1.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <!-- TPVR for Server Recovery -->
                            {{TpvrReason}}
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">                                
                                <form name="tpvForm">
                                    <div ng-bind-html="ErrorMessage"></div>
                                    <md-input-container class="md-block">
                                        <label>Server Recovery Controller</label>
                                        <input required name="AuditController" id="AuditController" ng-model="confirmDetails1.AuditController" ng-maxlength="50" type="text" ng-enter="FocusPassword()" autocomplete="off">
                                        <div ng-messages="tpvForm.AuditController.$error" multiple ng-if='tpvForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="Password1" ng-model="confirmDetails1.Password" ng-maxlength="50" type="password" ng-enter="hide()" autocomplete="off">
                                        <div ng-messages="tpvForm.Password.$error" multiple ng-if='tpvForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="!confirmDetails1.Password">Continue</button>
                        </div>
                    </div>
                </script>



                <script type="text/ng-template" id="password2.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <!-- TPVR for Media Recovery -->                            
                            {{TpvrReason1}}
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">                                
                                <form name="tpvForm">
                                    <!-- {{ErrorMessage}} -->
                                    <div ng-bind-html="ErrorMessage1"></div>
                                    <md-input-container class="md-block">
                                        <label>Audit Controller</label>
                                        <input required name="AuditController" id="AuditController1" ng-model="confirmDetails2.AuditController" ng-maxlength="50" type="text" autocomplete="off" ng-enter="NavigateToPassword()">
                                        <div ng-messages="tpvForm.AuditController.$error" multiple ng-if='tpvForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="Password2" ng-model="confirmDetails2.Password" ng-maxlength="50" type="password" ng-enter="hide()" autocomplete="off">
                                        <div ng-messages="tpvForm.Password.$error" multiple ng-if='tpvForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="!confirmDetails2.Password">Continue</button>
                        </div>
                    </div>
                </script>



                <script type="text/ng-template" id="password3.html">

                    <div style="max-width:900px">

                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>Move & Scan New BIN</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">

                                <div class="form-horizontal verification-form">                                
                                    <form name="tpvForm">      
                                        <!-- <div>{{CurrentPallet}}</div> -->
                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Current Bin Location</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.LocationName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Location Label</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.lable_name}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Location Type</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.LocationType}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-8">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Current Bin</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.BinName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4" >                                        
                                            <div class="autocomplete" style="padding:0px;">
                                                <md-autocomplete flex id="AuditController3" style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                    md-input-name="group"
                                                    md-input-maxlength="100"                                                    
                                                    md-no-cache="noCache"
                                                    md-search-text-change="LocationChange1(confirmDetails3.group)"
                                                    md-search-text="confirmDetails3.group"
                                                    md-items="item in queryLocationSearch1(confirmDetails3.group)"
                                                    md-item-text="item.GroupName"
                                                    md-selected-item-change="selectedLocationChange1(item)"
                                                    md-min-length="0"
                                                    ng-model-options='{ debounce: 1000 }'
                                                    md-escape-options="clear"
                                                    md-floating-label="WIP Location Group"
                                                    >
                                                    <md-item-template>
                                                        <span md-highlight-text="confirmDetails3.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                    </md-item-template>
                                                    <md-not-found>
                                                        No Records matching "{{confirmDetails3.group}}" were found.
                                                    </md-not-found>
                                                    <div ng-messages="tpvForm.group.$error" ng-if="tpvForm.group.$touched">
                                                        <div ng-message="required">No Records matching.</div>
                                                        <div ng-message="minlength">Min length 2.</div>
                                                        <div ng-message="maxlength">Max length 100.</div>
                                                    </div>
                                                </md-autocomplete>
                                            </div>
                                        </div>


                                                                                                                
                                    </form>
                                </div>                        
                            
                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                    <!-- <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                    <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="MoveBinToNewLocationGroup($event)" ng-disabled="tpvForm.$invalid">Continue</button>
                                </div>
                        <md-dialog-content>
                    </div>
                </script>


                <script type="text/ng-template" id="password4.html">

                    <div style="max-width:900px">

                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>Consolidate BIN</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">

                                <div class="form-horizontal verification-form">                                
                                    <form name="tpvForm">      
                                        <!-- <div>{{CurrentPallet}}</div> -->
                                        <div class="col-md-12">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>From Bin</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentCustomPalletPallet.BinName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>  
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>To BIN</label>
                                                <input required name="ToBinName" id="newbin" ng-model="confirmDetails4.ToBinName" ng-maxlength="200" type="text" autocomplete="off" ng-change="confirmDetails4.ToShipmentContainer = 'n/a'">
                                                <div ng-messages="tpvForm.ToBinName.$error" multiple ng-if='tpvForm.ToBinName.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                        
                                        <!-- <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>To Shipment Container</label>
                                                <input required name="ToShipmentContainer" id="ToShipmentContainer" ng-model="confirmDetails4.ToShipmentContainer" ng-maxlength="200" type="text" autocomplete="off" ng-init="confirmDetails4.ToShipmentContainer = 'n/a'" ng-change="confirmDetails4.ToBinName = 'n/a'">
                                                <div ng-messages="tpvForm.ToShipmentContainer.$error" multiple ng-if='tpvForm.ToShipmentContainer.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div> -->
                                    </form>
                                </div>                        
                            
                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                    <!-- <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                    <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="ConsolidateBin($event)" ng-disabled="tpvForm.$invalid">Consolidate</button>
                                </div>
                        <md-dialog-content>
                    </div>
                </script>


                <script type="text/ng-template" id="password5.html">

                    <div style="max-width:900px">

                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>Consolidate Shipment Container</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">

                                <div class="form-horizontal verification-form">                                
                                    <form name="tpvForm">      
                                        <!-- <div>{{CurrentPallet}}</div> -->
                                        <div class="col-md-12">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>From Container</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentShipmentContainer.ShippingContainerID}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>                                          
                                        
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>To Shipment Container</label>
                                                <input required name="ToShipmentContainer" id="ToShipmentContainer" ng-model="confirmDetails5.ToShipmentContainer" ng-maxlength="200" type="text" autocomplete="off" >
                                                <div ng-messages="tpvForm.ToShipmentContainer.$error" multiple ng-if='tpvForm.ToShipmentContainer.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                    </form>
                                </div>                        
                            
                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                    <!-- <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                    <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="ConsolidateShipmentContainer($event)" ng-disabled="tpvForm.$invalid">Consolidate</button>
                                </div>
                        <md-dialog-content>
                    </div>
                </script>

                <script type="text/ng-template" id="createBinModal.html">
                    <div style="max-width:800px">
                        <md-toolbar>
                            <div class="md-toolbar-tools">
                                <h2>Create Bin</h2>
                                <span flex></span>
                                <md-button class="md-icon-button" ng-click="cancel()">
                                    <md-icon class="material-icons">close</md-icon>
                                </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">
                                <div class="form-horizontal verification-form">
                                    <form name="createBinForm">

                                        <!-- First Row: Bin Type, Bin Name with Generate Button -->
                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Bin Type</label>
                                                <md-select name="idPackage" ng-model="createBinData.idPackage" required aria-label="select" ng-change="onBinTypeChange()">
                                                    <md-option ng-repeat="packageType in PackageTypes" value="{{packageType.idPackage}}"> {{packageType.packageName}} </md-option>
                                                </md-select>
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.idPackage.$error" multiple ng-if='createBinForm.idPackage.$dirty'>
                                                        <div ng-message="required">Bin Type is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Bin Name</label>
                                                <input name="BinName" ng-model="createBinData.BinName" value="" required />
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.BinName.$error" multiple ng-if='createBinForm.BinName.$dirty'>
                                                        <div ng-message="required">Bin Name is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <!-- Second Row: Facility, Location Type -->
                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Facility</label>
                                                <md-select name="FacilityID" ng-model="createBinData.FacilityID" required aria-label="select" ng-disabled="true">
                                                    <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                                </md-select>
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.FacilityID.$error" multiple ng-if='createBinForm.FacilityID.$dirty'>
                                                        <div ng-message="required">This is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Location Type</label>
                                                <md-select name="LocationType" ng-model="createBinData.LocationType" required aria-label="select" ng-disabled="true">
                                                    <md-option value="WIP">WIP</md-option>
                                                </md-select>
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.LocationType.$error" multiple ng-if='createBinForm.LocationType.$dirty'>
                                                        <div ng-message="required">This is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <!-- Third Row: Location Group, Disposition -->
                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Location Group</label>
                                                <input type="text" name="LocationGroup" ng-model="createBinData.LocationGroup" ng-disabled="true" />
                                                <div class="error-space"></div>
                                            </md-input-container>
                                        </div>



                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Disposition</label>
                                                <input type="text" name="Disposition" ng-model="createBinData.Disposition" ng-disabled="true" />
                                                <div class="error-space"></div>
                                            </md-input-container>
                                        </div>

                                        <!-- Fourth Row: Notes -->
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>Notes</label>
                                                <input type="text" name="Notes" ng-model="createBinData.Notes" ng-maxlength="250" />
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.Notes.$error" multiple ng-if='createBinForm.Notes.$dirty'>
                                                        <div ng-message="maxlength">Max length 250.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                    </form>
                                </div>

                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</button>
                                    <button type="button" class="md-button md-raised btn-w-md md-primary" ng-click="createBin()" ng-disabled="createBinForm.$invalid || createBinBusy">
                                        <span ng-show="!createBinBusy">Create Bin</span>
                                        <span ng-show="createBinBusy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular></span>
                                    </button>
                                </div>
                            </div>
                        </md-dialog-content>
                    </div>
                </script>

                <!-- Nest to Bin Modal -->
                <script type="text/ng-template" id="nestToBinModal.html">
                    <div style="max-width:600px">
                        <md-toolbar>
                            <div class="md-toolbar-tools">
                                <h2>Nest to Bin</h2>
                                <span flex></span>
                                <md-button class="md-icon-button" ng-click="cancel()">
                                    <md-icon class="material-icons">close</md-icon>
                                </md-button>
                            </div>
                        </md-toolbar>
                        <md-dialog-content>
                            <div class="md-dialog-content">
                                <form name="nestToBinForm" novalidate>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h4>Current Bin: {{nestToBinData.BinName}}</h4>
                                            <p>Select a parent bin to nest this bin under:</p>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>Parent Bin *</label>
                                                <input required name="parentBin" ng-model="nestToBinData.parentBin" ng-maxlength="500" type="text" placeholder="Enter Parent Bin Name">
                                                <div ng-messages="nestToBinForm.parentBin.$error" multiple ng-if='nestToBinForm.parentBin.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 500.</div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                    </div>
                                </form>

                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</button>
                                    <button type="button" class="md-button md-raised btn-w-md md-primary" ng-click="nestToBin()" ng-disabled="nestToBinForm.$invalid || nestToBinBusy || !nestToBinData.parentBin">
                                        <span ng-show="!nestToBinBusy">Nest to Bin</span>
                                        <span ng-show="nestToBinBusy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular></span>
                                    </button>
                                </div>
                            </div>
                        </md-dialog-content>
                    </div>
                </script>

                <!-- Close Bin Modal -->
                <script type="text/ng-template" id="closeBinModal.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Closing Bin ({{CurrentBin.BinName}})
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">
                                <form name="closeBinForm">
                                    <md-input-container class="md-block">
                                        <label>Controller</label>
                                        <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="100" type="text" ng-enter="FocusNextField('password','0')">
                                        <div ng-messages="closeBinForm.AuditController.$error" multiple ng-if='closeBinForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>
                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="FocusNextField('SealID','0')">
                                        <div ng-messages="closeBinForm.Password.$error" multiple ng-if='closeBinForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Seal ID</label>
                                        <input required name="SealID" id="SealID" ng-model="confirmDetails.NewSealID" ng-maxlength="100" type="text" ng-enter="FocusNextField('BinWeight','0')">
                                        <div ng-messages="closeBinForm.SealID.$error" multiple ng-if='closeBinForm.SealID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Weight</label>
                                        <input required name="BinWeight" id="BinWeight" ng-model="confirmDetails.BinWeight" ng-max="999999" ng-min="0" type="number" ng-enter="hide()">
                                        <div ng-messages="closeBinForm.BinWeight.$error" multiple ng-if='closeBinForm.BinWeight.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="max">Maximum value is 999999.</div>
                                            <div ng-message="min">Minimum value is 0.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="closeBinForm.$invalid">Continue</button>
                        </div>
                    </div>
                </script>


                <md-card class="no-margin-h pt-0">

                    <md-toolbar class="md-table-toolbar md-default" ng-init="Stationblock = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="Stationblock = !Stationblock">
                            <i class="material-icons md-primary" ng-show="Stationblock">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! Stationblock">keyboard_arrow_down</i>
                            <span>Parts Recovery</span>
                        </div>
                    </md-toolbar>

                    <div class="row" ng-show="Stationblock">

                        <div class="col-md-12">

                            <div class="bg-grey-light">
                                <div class="row">
                                    <div class="col-md-12">

                                        <div class="col-lg-5 pr_md0">
                                            <div class="row">
                                                <!-- {{PartsRecovery}} -->
                                                <div class="col-md-6 pr_md0">
                                                    <md-input-container class="md-block">
                                                        <label>Recovery Type</label>
                                                        <md-select name="RecoveryType" id="RecoveryType" ng-model="PartsRecovery.RecoveryType" required aria-label="select" ng-disabled="DisableRecoveryType" ng-init="DisableRecoveryType=false" ng-change="GetRecoveryTypeName();GetCurrentTime(PartsRecovery,'recovery_type_scan_time');GetRecoveryStations();GetPartTypesByRecoverytype();GetTopLevelAssetDetails(false)">
                                                            <md-option ng-repeat="Recovery in Recoverys" value="{{Recovery.Recoverytypeid}}"> {{Recovery.Recoverytype}} </md-option>

                                                        </md-select>
                                                        <!--<div class="error-sapce"></div>-->
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-6 pr_md0">
                                                    <md-input-container class="md-block">
                                                        <label>Workstation</label>
                                                        <md-select name="SiteID" id="SiteID" ng-model="PartsRecovery.SiteID" required ng-change="GetCurrentTime(PartsRecovery,'workstation_scan_time');GetStationCustomPallets();GetTopLevelAssetDetails(false);GetStationLocationInfo()" ng-init="DisableWorkstation=false" ng-disabled="DisableWorkstation">
                                                            <md-option value="{{station.SiteID}}" ng-repeat="station in Stations">{{station.SiteName}}</md-option>
                                                        </md-select>
                                                        <!--
                                                        <div class="error-sapce">
                                                        <div ng-messages="shipmentForm.Workstation.$error" multiple ng-if='shipmentForm.Workstation.$dirty'>
                                                            <div ng-message="required">This is required.</div>
                                                        </div>
                                                        </div>-->
                                                    </md-input-container>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-7">
                                            <div class="row">
                                                <div class="col-md-5 pr_md0">
                                                    <md-input-container class="md-block includedsearch">
                                                        <label>Unique Identifier</label>
                                                        <!--<md-icon md-svg-src="../assets/images/print.svg" style="cursor: pointer;"></md-icon>-->
                                                        <input required name="TopLevelSerialNumber" id="TopLevelSerialNumber" ng-init="DisableUniqueIdentifier=false" ng-disabled="DisableUniqueIdentifier" ng-model="PartsRecovery.TopLevelSerialNumber" required ng-maxlength="100" ng-minlength="3" ng-enter="GetCurrentTime(PartsRecovery,'origin_container_id_scan_time');GetTopLevelAssetDetails();GetWIPRecoveredPartsByTopLeveAssetID();">
                                                        <!--<md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!PartsRecovery.SerialNumber" ng-click="GetMPNFromSerialrepair()">
                                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                        </md-button>-->
                                                        <md-button style="right: 36px;" class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!PartsRecovery.TopLevelSerialNumber"  ng-click="GetCurrentTime(PartsRecovery,'origin_container_id_scan_time');GetTopLevelAssetDetails();GetWIPRecoveredPartsByTopLeveAssetID();">
                                                            <md-icon md-svg-src="../assets/images/search.svg" style="display:inline-block; cursor: pointer;"></md-icon>
                                                        </md-button>
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="! DisableUniqueIdentifier"  ng-click="PrintTopLevel()">                                                    
                                                            <i class="material-icons mt-3"  aria-label="print">print</i>
                                                        </md-button>
                                                        <!--
                                                        <div class="error-sapce">
                                                            <div ng-messages="audit_form.SerialNumber.$error" multiple ng-if='audit_form.SerialNumber.$dirty'>
                                                                <div ng-message="required">This is required.</div>
                                                                <div ng-message="minlength">Min length 3.</div>
                                                                <div ng-message="maxlength">Max length 100.</div>
                                                            </div>
                                                        </div>
                                                        -->
                                                    </md-input-container>
                                                </div>                                                

                                                <div class="col-md-3 pr_md0">
                                                    <md-input-container class="md-block">
                                                        <label>MPN</label>
                                                        <input name="TopLevelMPN" ng-model="PartsRecovery.TopLevelMPN" value="" ng-max="22" ng-min="0" type="text" ng-disabled="true">
                                                        <!--<div class="error-sapce"></div>-->
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>Part Type</label>
                                                        <input name="TopLevelPartType" ng-model="PartsRecovery.TopLevelPartType" value="" type="text" ng-disabled="true">
                                                        <!--<div class="error-sapce"></div>-->
                                                    </md-input-container>
                                                </div>
                                               
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="col-md-3" ng-show="PartsRecovery.RecoveryTypeName == 'Component'">
                                                    <md-input-container class="md-block">
                                                        <label>Evaluation Result</label>
                                                        <md-select name="TopLevelInputID"  ng-model="PartsRecovery.TopLevelInputID" required ng-change="GetComponentDispositionDetails()" >
                                                            <md-option value="{{ip.input_id}}" ng-repeat="ip in Inputs">{{ip.input}}</md-option>
                                                        </md-select>                                                       
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-3" ng-show="PartsRecovery.RecoveryTypeName == 'Component'">
                                                    <md-input-container class="md-block">
                                                        <label>Disposition To</label>
                                                        <md-select name="TopLevelDispositionToID" ng-model="PartsRecovery.TopLevelDispositionToID"  ng-disabled="true">
                                                            <md-option value="{{disp.disposition_id}}" ng-repeat="disp in AllDispositions">{{disp.disposition}}</md-option>
                                                        </md-select>                                                       
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-3" ng-show="PartsRecovery.RecoveryTypeName == 'Component'">
                                                    <md-input-container class="md-block">
                                                        <label>Bin To</label>
                                                        <input name="TopLevelBinToName" ng-model="PartsRecovery.TopLevelBinToName" type="text" ng-disabled="true">
                                                        <!--<div class="error-sapce"></div>-->
                                                    </md-input-container>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    <div style="clear: both;"></div>

                                </div>

                            </div>

                        </div>
                      </div>
                </md-card>
                <!--Station info Close-->  
                
                
                <!--Serialized Section Form Start-->
                <md-card class="no-margin-h">
                    <md-toolbar class="md-table-toolbar md-default" ng-init="SwitchServerPannel = true">
                        <div class="md-toolbar-tools">
                            <i class="material-icons md-primary" ng-show="SwitchServerPannel" style="cursor: pointer;" ng-click="SwitchServerPannel = !SwitchServerPannel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! SwitchServerPannel" style="cursor: pointer;" ng-click="SwitchServerPannel = !SwitchServerPannel">keyboard_arrow_down</i>
                            <span>Serialized</span>
                            <!-- <div flex ng-show="PartsRecovery.RecoveryTypeName != 'Rack' && PartsRecovery.RecoveryTypeName != 'Assembly'"></div>
                            <span ng-show="PartsRecovery.RecoveryTypeName != 'Rack' && PartsRecovery.RecoveryTypeName != 'Assembly'">Total Count: <strong class="text-success">{{SerialRecoveryPartsCount}}</strong></span> -->                            
                            <div flex ></div>
                            <span >Total Count: <strong class="text-success">{{SerialRecoveryPartsCount}}</strong></span>                            
                            
                            <div flex ng-show="PartsRecovery.RecoveryTypeName == 'Rack' && PartsRecovery.MaterialType == 'Media Rack'"></div>
                            <span ng-show="PartsRecovery.RecoveryTypeName == 'Rack' && PartsRecovery.MaterialType == 'Media Rack'"><strong class="mr-5">Server Count:</strong><span ng-class="{'badge bg-danger': GetTotalServers() != PartsRecovery.ServerCount,'badge bg-success': GetTotalServers() == PartsRecovery.ServerCount}">{{GetTotalServers()}} / {{PartsRecovery.ServerCount}}</span></span>
                            <div flex ng-show="PartsRecovery.RecoveryTypeName == 'Rack' && PartsRecovery.MaterialType == 'Media Rack'"></div>
                            <span ng-show="PartsRecovery.RecoveryTypeName == 'Rack' && PartsRecovery.MaterialType == 'Media Rack'"><strong class="mr-5">Discrepancy:</strong><span ng-class="{'badge bg-danger': GetFailedServerCount() > 0,'badge bg-success': GetFailedServerCount() == 0}">{{GetFailedServerCount()}} </span></span>

                            <div flex ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack' && PartsRecovery.ServerID > 0"></div>
                            <span ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack' && PartsRecovery.ServerID > 0"><strong class="mr-5">HDD Count:</strong><span ng-class="{'badge bg-danger': GetHDDS() != PartsRecovery.HDDCount,'badge bg-success': GetHDDS() == PartsRecovery.HDDCount}">{{GetHDDS()}} / {{PartsRecovery.HDDCount}}</span></span>

                            <div flex ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack' && PartsRecovery.ServerID > 0"></div>
                            <span ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack' && PartsRecovery.ServerID > 0"><strong class="mr-5">HDD Discrepancy:</strong><span ng-class="{'badge bg-danger': GetFailedHDDCount() > 0,'badge bg-success': GetFailedHDDCount() == 0}">{{GetFailedHDDCount()}} </span></span>

                            <div flex ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack' && PartsRecovery.ServerID > 0"></div>                            
                            <span ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack' && PartsRecovery.ServerID > 0"><strong class="mr-5">SSD Count:</strong><span ng-class="{'badge bg-danger': GetSSDS() != PartsRecovery.SSDCount,'badge bg-success': GetSSDS() == PartsRecovery.SSDCount}">{{GetSSDS()}} / {{PartsRecovery.SSDCount}}</span></span>

                            <div flex ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack' && PartsRecovery.ServerID > 0"></div>
                            <span ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack' && PartsRecovery.ServerID > 0"><strong class="mr-5">SSD Discrepancy:</strong><span ng-class="{'badge bg-danger': GetFailedSSDCount() > 0,'badge bg-success': GetFailedSSDCount() == 0}">{{GetFailedSSDCount()}} </span></span>

                        </div>
                    </md-toolbar>

                    <div class="row mt-5" ng-show="SwitchServerPannel">                          
                        <form name="serialized_form" ng-if="PartsRecovery.RecoveryTypeName == 'Container' && PartsRecovery.BatchRecovery == '1' && PartsRecovery.BatchRecoveryCompleted == '0'">
                            <div class="col-md-12">                             
                                <div class="row">                                    
                                    <div class="col-md-12">
                                        <div class="col-md-12"><strong style="display: flex; line-height: 24px; color:#7a7a7a;"><i class="material-icons">replay</i> <span>Batch Recovery</span></strong></div>
                                       <div class="col-md-3">
                                           <md-input-container class="md-block">
                                               <label>Evaluation Result</label>
                                               <md-select name="EvaluationResult" ng-model="SerialPartRecovery.EvaluationResultID" ng-change="GetCurrentTime(SerialPartRecovery,'result_scan_time')" required>
                                                   <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults1">{{ir.input}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-md-3">
                                           <md-input-container class="md-block">
                                               <label>COO</label>
                                               <md-select name="COO" ng-model="SerialPartRecovery.COOID" ng-change="GetCurrentTime(SerialPartRecovery,'coo_scan_time');GetBatchRecoveryDispositionDetails()" required>
                                                   <md-option value="{{COO.COOID}}" ng-repeat="COO in COOList1">{{COO.COO}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       
                                       <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Disposition</label>
                                                <!-- <input name="disposition" ng-model="SerialPartRecovery.disposition" value="" ng-disabled="true"> -->

                                                <md-select name="disposition_id" ng-model="SerialPartRecovery.disposition_id" ng-change="GetBatchRecoveryDispositionDetails1()" required>
                                                    <md-option value="{{dis.disposition_id}}" ng-repeat="dis in BulkRecoveryDispositions" ng-if="dis.sub_disposition == '0' && dis.disposition != 'BRE'">{{dis.disposition}}</md-option>
                                                </md-select>

                                            </md-input-container>
                                        </div>

                                        <div class="col-md-3">
                                            <md-input-container class="md-block">
                                                <label>Disposition Bin</label>
                                                <input name="dispositionbin" ng-model="SerialPartRecovery.DispositionBin" style="text-align:right;direction: rtl;" required value="" ng-disabled="true"/>
                                            </md-input-container>
                                        </div>                                                                            

                                        <div class="col-md-12 btns-row">     
                                            <button class="md-button md-raised btn-w-md  md-default" ng-click="ReloadPage()">Cancel</button>                                       
                                            <md-button class="md-raised btn-w-md md-primary btn-w-md" 
                                                data-ng-disabled="serialized_form.$invalid  || PartsRecovery.busy || !PartsRecovery.idPallet" ng-click="SaveBatchRecoveryParts($event);" >
                                                <span ng-show="!PartsRecovery.busy">Save & Close</span>
                                                <span ng-show="PartsRecovery.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                            </md-button>                                            
                                        </div> 

                                    </div>                                    
                                </div>
                           </div>
                        </form>
                        
                        <form name="serialized_form" ng-if="PartsRecovery.RecoveryTypeName == 'Container' && ((PartsRecovery.BatchRecovery == '0' || !PartsRecovery.BatchRecovery ) || (PartsRecovery.BatchRecoveryCompleted == '1' || !PartsRecovery.BatchRecoveryCompleted))">
                            <div class="col-md-12">
                                <div class="row">                                    
                                    <div class="col-lg-6 col-md-12 pr_md0">

                                       <div class="col-lg-3 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>Part Type</label><!-- GetSerializedPartTypeRecoveryDetails() -->
                                               <md-select name="PartType" id="PartTypeId" ng-model="SerialPartRecovery.parttypeid" required aria-label="select" ng-change="GetCurrentTime(SerialPartRecovery,'part_type_scan_time');GetEvaluationResultsByPart()">
                                                   <md-option ng-repeat="PartType in SerializedPartTypes" value="{{PartType.parttypeid}}"> {{PartType.parttype}} </md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-lg-4 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>Evaluation Result</label>
                                               <md-select name="EvaluationResult" ng-model="SerialPartRecovery.EvaluationResultID" ng-change="GetCurrentTime(SerialPartRecovery,'result_scan_time');GetDispositionDetailsByPart()" required>
                                                   <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-lg-2 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>COO</label>
                                               <md-select name="COO" ng-model="SerialPartRecovery.COOID" ng-change="GetCurrentTime(SerialPartRecovery,'coo_scan_time');focusNextField('SerialPartSerialNumber');" required>
                                                   <md-option value="{{COO.COOID}}" ng-repeat="COO in COOList">{{COO.COO}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>

                                       <div class="col-lg-3 col-md-3 pr_md0">
                                            <md-input-container class="md-block includedsearch">
                                                <label>SN</label>
                                                <input required name="SerialNumber" ng-model="SerialPartRecovery.SerialNumber" required ng-maxlength="45" ng-minlength="3" ng-change="ContainerRecoveryMPNChanged();" ng-enter="GetCurrentTime(SerialPartRecovery,'serial_scan_time');GetMPNFromSerial();" id="SerialPartSerialNumber1" >
                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.SerialNumber" ng-click="GetCurrentTime(SerialPartRecovery,'serial_scan_time');GetMPNFromSerial();">
                                                    <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                </md-button>
                                            </md-input-container>
                                        </div>                                        

                                    </div>
                                    <div class="col-lg-6 col-md-12">

                                        <div class="col-lg-3 col-md-3 pl_md0">
                                            <md-input-container class="md-block includedsearch">
                                                <label>MPN</label>
                                                <input required name="MPN1" ng-model="SerialPartRecovery.MPN" required ng-maxlength="100" ng-minlength="3" ng-enter="GetCurrentTime(SerialPartRecovery,'mpn_scan_time');MPNValidate()" id="MPN1" ng-change="ContainerRecoveryMPNChanged();PartsRecovery.ValidMPN = false;">
                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.MPN"  ng-click="GetCurrentTime(SerialPartRecovery,'mpn_scan_time');MPNValidate();">
                                                    <md-icon md-svg-src="../assets/images/search.svg" style="display:inline-block; cursor: pointer;"></md-icon>
                                                </md-button>
                                            </md-input-container>
                                        </div>

                                        <div class="col-lg-4 col-md-3 pl_md0">
                                            <md-input-container class="md-block">
                                                <label>Disposition</label>
                                                <input name="disposition" ng-model="SerialPartRecovery.disposition" value="" ng-disabled="true">
                                            </md-input-container>
                                        </div>

                                       <div class="col-lg-5 col-md-6 pl_md0">
                                           <md-input-container class="md-block">
                                               <label>Disposition Bin</label>
                                               <input name="dispositionbin" ng-model="SerialPartRecovery.DispositionBin" style="text-align:right;direction: rtl;" value="" ng-disabled="true"/>
                                           </md-input-container>
                                       </div>                                        

                                    </div>

                                </div>
                           </div>
                        </form>


                        <form name="serialized_form" ng-if="PartsRecovery.RecoveryTypeName == 'Component'">
                            <div class="col-md-12">
                                <div class="row">

                                    <div class="col-lg-6 col-md-12 pr_md0">

                                       <div class="col-lg-3 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>Part Type</label><!-- GetSerializedPartTypeRecoveryDetails() -->
                                               <md-select name="PartType" id="PartTypeId" ng-model="SerialPartRecovery.parttypeid" required aria-label="select" ng-change="GetCurrentTime(SerialPartRecovery,'part_type_scan_time');GetEvaluationResultsByPart()">
                                                   <md-option ng-repeat="PartType in SerializedPartTypes" value="{{PartType.parttypeid}}"> {{PartType.parttype}} </md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-lg-4 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>Evaluation Result</label>
                                               <md-select name="EvaluationResult" ng-model="SerialPartRecovery.EvaluationResultID" ng-change="GetCurrentTime(SerialPartRecovery,'result_scan_time');GetDispositionDetailsByPart()" required>
                                                   <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-lg-2 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>COO</label>
                                               <md-select name="COO" ng-model="SerialPartRecovery.COOID" ng-change="GetCurrentTime(SerialPartRecovery,'coo_scan_time');focusNextField('SerialPartSerialNumber');" required>
                                                   <md-option value="{{COO.COOID}}" ng-repeat="COO in COOList">{{COO.COO}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>

                                       <div class="col-lg-3 col-md-3 pr_md0">
                                            <md-input-container class="md-block includedsearch">
                                                <label>SN</label>
                                                <input required name="SerialNumber" ng-model="SerialPartRecovery.SerialNumber" required ng-maxlength="45" ng-minlength="3" ng-enter="GetCurrentTime(SerialPartRecovery,'serial_scan_time');GetMPNFromSerial1()" data-ng-disabled="true" id="SerialPartSerialNumber4" >
                                                <!--
                                                <md-button class="md-raised md-mini md-primary" ng-click="GenerateSerialNumber()">
                                                    Generate & Print
                                                </md-button>
                                                -->
                                                <!--
                                                <md-button style="right: 36px;" class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.SerialNumber" ng-click="GetCurrentTime(SerialPartRecovery,'serial_scan_time');GetMPNFromSerial1()">
                                                    <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                </md-button>
                                                -->

                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.parttypeid" ng-click="GenerateSerialNumber()">
                                                    <md-tooltip md-direction="top">GENARATE</md-tooltip>
                                                    <i class="material-icons" style="margin-top: 3px;">sync</i>
                                                </md-button>

                                                <!-- <md-button class="md-fab md-raised md-mini md-primary md-fab-bottom-right" ng-click="Print()" ng-disabled="!SerialPartRecovery.SerialNumber">                                                    
                                                    <i class="material-icons mt-3">print</i>
                                                </md-button> -->
                                            </md-input-container>                                            

                                        </div>                                        

                                    </div>
                                    <div class="col-lg-6 col-md-12">

                                        <div class="col-lg-3 col-md-3 pl_md0">
                                            <md-input-container class="md-block includedsearch">
                                                <label>MPN</label>
                                                <input required name="MPN1" ng-model="SerialPartRecovery.MPN" required ng-maxlength="100" ng-minlength="3" ng-enter="GetCurrentTime(SerialPartRecovery,'mpn_scan_time');MPNValidate()" id="MPN4" ng-change="PartsRecovery.ValidMPN = false;">
                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.MPN"  ng-click="GetCurrentTime(SerialPartRecovery,'mpn_scan_time');MPNValidate();">
                                                <md-icon md-svg-src="../assets/images/search.svg" style="display:inline-block; cursor: pointer;"></md-icon>
                                                </md-button>
                                            </md-input-container>
                                        </div>

                                        <div class="col-lg-4 col-md-3 pl_md0">
                                            <md-input-container class="md-block">
                                                <label>Disposition</label>
                                                <input name="disposition" ng-model="SerialPartRecovery.disposition" value="" ng-disabled="true">
                                            </md-input-container>
                                        </div>

                                       <div class="col-lg-5 col-md-6 pl_md0">
                                           <md-input-container class="md-block">
                                               <label>Disposition Bin</label>
                                               <input name="dispositionbin" ng-model="SerialPartRecovery.DispositionBin" style="text-align:right;direction: rtl;" value="" ng-disabled="true"/>
                                           </md-input-container>
                                       </div>                                        

                                    </div>

                                </div>
                           </div>
                        </form>

                        <form name="serialized_form" ng-if="PartsRecovery.RecoveryTypeName == 'Rack'">
                            <div class="col-md-12">
                                <div class="row">

                                    <div class="col-lg-6 col-md-12 pr_md0">

                                       <div class="col-lg-3 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>Part Type</label><!-- GetSerializedPartTypeRecoveryDetails() -->
                                               <md-select name="PartType" id="PartTypeId" ng-model="SerialPartRecovery.parttypeid" required aria-label="select" ng-change="GetCurrentTime(SerialPartRecovery,'part_type_scan_time');GetEvaluationResultsByPart()">
                                                   <md-option ng-repeat="PartType in SerializedPartTypes" value="{{PartType.parttypeid}}"> {{PartType.parttype}} </md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-lg-4 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>Evaluation Result</label>
                                               <md-select name="EvaluationResult" ng-model="SerialPartRecovery.EvaluationResultID" ng-change="GetCurrentTime(SerialPartRecovery,'result_scan_time');GetDispositionDetailsByPart()" required>
                                                   <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-lg-2 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>COO</label>
                                               <md-select name="COO" ng-model="SerialPartRecovery.COOID" ng-change="GetCurrentTime(SerialPartRecovery,'coo_scan_time');focusNextField('SerialPartSerialNumber');" required>
                                                   <md-option value="{{COO.COOID}}" ng-repeat="COO in COOList">{{COO.COO}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>

                                       <div class="col-lg-3 col-md-3 pr_md0">
                                            <md-input-container class="md-block includedsearch">
                                                <label>SN</label>
                                                <input required name="SerialNumber" ng-model="SerialPartRecovery.SerialNumber" required ng-maxlength="45" ng-minlength="3" ng-enter="GetCurrentTime(SerialPartRecovery,'serial_scan_time');ValidateRackItemSerial();" id="SerialPartSerialNumber2" >
                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.SerialNumber" ng-click="GetCurrentTime(SerialPartRecovery,'serial_scan_time');ValidateRackItemSerial();">
                                                    <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                </md-button>
                                            </md-input-container>
                                        </div>                                        

                                    </div>
                                    <div class="col-lg-6 col-md-12" >

                                        <!-- //<div class="col-lg-3 col-md-3 pl_md0" ng-if="SerialPartRecovery.parttypeid != '7'"> -->
                                        <div class="col-lg-3 col-md-3 pl_md0" ng-if="SerialPartRecovery.PartTypeName != 'SERVER' && SerialPartRecovery.PartTypeName != 'Server' && SerialPartRecovery.PartTypeName != 'server'">
                                            <md-input-container class="md-block includedsearch">
                                                <label>MPN</label>
                                                <input required name="MPN1" ng-model="SerialPartRecovery.MPN" required ng-maxlength="100" ng-minlength="3" ng-enter="GetCurrentTime(SerialPartRecovery,'mpn_scan_time');MPNValidate()" id="MPN1" ng-change="PartsRecovery.ValidMPN = false;">
                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.MPN"  ng-click="GetCurrentTime(SerialPartRecovery,'mpn_scan_time');MPNValidate();">
                                                <md-icon md-svg-src="../assets/images/search.svg" style="display:inline-block; cursor: pointer;"></md-icon>
                                                </md-button>
                                            </md-input-container>
                                        </div>

                                        <div class="col-lg-4 col-md-3 pl_md0">
                                            <md-input-container class="md-block">
                                                <label>Disposition</label>
                                                <input name="disposition" ng-model="SerialPartRecovery.disposition" value="" ng-disabled="true">
                                            </md-input-container>
                                        </div>

                                       <div class="col-lg-5 col-md-6 pl_md0">
                                           <md-input-container class="md-block">
                                               <label>Disposition Bin</label>
                                               <input name="dispositionbin" ng-model="SerialPartRecovery.DispositionBin" style="text-align:right;direction: rtl;" value="" ng-disabled="true"/>
                                           </md-input-container>
                                       </div>                                        

                                    </div>

                                </div>
                           </div>
                        </form>

                        <form name="serialized_form" ng-if="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.idPallet != ''">
                            <div class="col-md-12">
                                <div class="row" ng-show="PartsRecovery.idPallet != ''">                                    
                                    <div class="col-lg-6 col-md-12 pr_md0">

                                       <div class="col-lg-3 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>Part Type</label><!-- GetSerializedPartTypeRecoveryDetails() -->
                                               <md-select name="PartType" id="PartTypeId" ng-model="SerialPartRecovery.parttypeid" required aria-label="select" ng-change="GetCurrentTime(SerialPartRecovery,'part_type_scan_time');GetEvaluationResultsByPart()">
                                                   <md-option ng-repeat="PartType in SerializedPartTypes" value="{{PartType.parttypeid}}"> {{PartType.parttype}} </md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-lg-4 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>Evaluation Result</label>
                                               <md-select name="EvaluationResult" ng-model="SerialPartRecovery.EvaluationResultID" ng-change="GetCurrentTime(SerialPartRecovery,'result_scan_time');GetDispositionDetailsByPart()" required>
                                                   <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>
                                       <div class="col-lg-2 col-md-3 pr_md0">
                                           <md-input-container class="md-block">
                                               <label>COO</label>
                                               <md-select name="COO" ng-model="SerialPartRecovery.COOID" ng-change="GetCurrentTime(SerialPartRecovery,'coo_scan_time');focusNextField('SerialPartSerialNumber');" required>
                                                   <md-option value="{{COO.COOID}}" ng-repeat="COO in COOList">{{COO.COO}}</md-option>
                                               </md-select>
                                           </md-input-container>
                                       </div>

                                       <div class="col-lg-3 col-md-3 pr_md0">
                                            <md-input-container class="md-block includedsearch">
                                                <label>SN</label>
                                                <input required name="SerialNumber" ng-model="SerialPartRecovery.SerialNumber" required ng-maxlength="45" ng-minlength="3" ng-enter="GetCurrentTime(SerialPartRecovery,'serial_scan_time');MediaScanned();" id="SerialPartSerialNumber3" >
                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.SerialNumber" ng-click="GetCurrentTime(SerialPartRecovery,'serial_scan_time');MediaScanned();">
                                                    <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                </md-button>
                                            </md-input-container>
                                        </div>                                        

                                    </div>
                                    <div class="col-lg-6 col-md-12" >

                                        <div class="col-lg-3 col-md-3 pl_md0">
                                            <md-input-container class="md-block includedsearch">
                                                <label>MPN</label>
                                                <input required name="MPN1" ng-model="SerialPartRecovery.MPN" required ng-maxlength="100" ng-minlength="3" ng-enter="GetCurrentTime(SerialPartRecovery,'mpn_scan_time');MPNValidate()" id="MPN3" ng-change="PartsRecovery.ValidMPN = false;">
                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!SerialPartRecovery.MPN"  ng-click="GetCurrentTime(SerialPartRecovery,'mpn_scan_time');MPNValidate();">
                                                <md-icon md-svg-src="../assets/images/search.svg" style="display:inline-block; cursor: pointer;"></md-icon>
                                                </md-button>
                                            </md-input-container>
                                        </div>

                                        <div class="col-lg-4 col-md-3 pl_md0">
                                            <md-input-container class="md-block">
                                                <label>Disposition</label>
                                                <input name="disposition" ng-model="SerialPartRecovery.disposition" value="" ng-disabled="true">
                                            </md-input-container>
                                        </div>

                                       <div class="col-lg-5 col-md-6 pl_md0">
                                           <md-input-container class="md-block">
                                               <label>Disposition Bin</label>
                                               <input name="dispositionbin" ng-model="SerialPartRecovery.DispositionBin" style="text-align:right;direction: rtl;" value="" ng-disabled="true"/>
                                           </md-input-container>
                                       </div>                                        

                                    </div>

                                </div>
                           </div>
                        </form>

                        <div class="col-md-12 mt-5">
                            <div class="col-md-12">
                                <div class="table-responsive" style="overflow: auto;" ng-show="SerialRecoveryParts.length>0">

                                    <table class="table" md-table md-row-select>

                                        <thead md-head>

                                            <tr md-row class="bg-grey">
                                                <th md-column>
                                                    <div style="min-width: 30px; width: 30px;">
                                                        Edit
                                                    </div>
                                                </th>
                                                <th md-column>
                                                    <div>
                                                        Part Type
                                                    </div>
                                                </th>
                                                <th md-column>
                                                    <div>
                                                        Evaluation Result
                                                    </div>
                                                </th>
                                                <th md-column>
                                                    <div>
                                                        COO
                                                    </div>
                                                </th>
                                                <th md-column>
                                                    <div>
                                                        MPN
                                                    </div>
                                                </th>


                                                <th md-column>
                                                    <div>
                                                        Disposition
                                                    </div>
                                                </th>

                                                <th md-column>
                                                    <div style="min-width: 160px;">
                                                        Bin
                                                    </div>
                                                </th>
                                                <th md-column>
                                                    <div style="min-width: 120px;">
                                                        Serial No#
                                                    </div>
                                                </th>

                                            </tr>
                                        </thead>
                                        <tbody md-body>

                                          <tr md-row ng-repeat="SerialPart in SerialRecoveryParts" ng-class = "{'danger' : SerialPart.ValidSerial == false && PartsRecovery.MaterialType == 'Media Rack' && (SerialPart.parttype == 'Server' || SerialPart.parttype == 'SERVER' || SerialPart.parttype == 'HDD' || SerialPart.parttype == 'SSD')}" ng-hide="(SerialPart.ServerID > 0 && PartsRecovery.RecoveryTypeName == 'Rack') || (SerialPart.MediaID > 0 && PartsRecovery.RecoveryTypeName == 'Assembly')">
                                              <td md-cell class="actionicons" style="min-width: 74px; width: 100px;">
                                                <div ng-hide="(SerialPart.ServerID > 0 && PartsRecovery.RecoveryTypeName == 'Rack') || (SerialPart.MediaID > 0 && PartsRecovery.RecoveryTypeName == 'Assembly')">
                                                    <i class="material-icons text-danger edit" style="cursor: pointer;" ng-click="EditSerialRecoverPart(SerialPart)">edit</i>
                                                    <i class="material-icons text-danger" ng-click="DeleteSerialRecoveryPart(SerialPart,PartsRecovery.RecoveryTypeName)">delete</i>
                                                    <!-- <i class="material-icons text-danger"  >print</i> -->

                                                    <a href="{{host}}label/master/examples/receiveseriallabel.php?id={{SerialPart.AssetScanID}}&parttype={{SerialPart.parttype}}" target="_blank" ng-show="PartsRecovery.RecoveryTypeName == 'Container'">
                                                        <i class="material-icons text-danger"  aria-label="print">print</i>
                                                    </a>

                                                    <a href="{{host}}label/master/examples/serverlabel.php?id={{PartsRecovery.TopLevelSerialNumber}}&serial={{SerialPart.SerialNumber}}" target="_blank" ng-show="PartsRecovery.RecoveryTypeName == 'Rack'">
                                                        <i class="material-icons text-danger"  aria-label="print">print</i>
                                                    </a>

                                                    <a href="{{host}}label/master/examples/medialabel.php?rack_id={{SerialPart.idPallet}}&host_id={{SerialPart.ServerSerialNumber}}&serial={{SerialPart.SerialNumber}}&mpn={{SerialPart.MPN}}" target="_blank" ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && SerialPart.ServerID > 0">
                                                        <i class="material-icons text-danger"  aria-label="print">print</i>
                                                    </a>

                                                    <a href="{{host}}label/master/examples/serverlabel.php?id={{PartsRecovery.TopLevelSerialNumber}}&serial={{SerialPart.SerialNumber}}" target="_blank" ng-show="PartsRecovery.RecoveryTypeName == 'Assembly' && SerialPart.AssetScanID > 0">
                                                        <i class="material-icons text-danger"  aria-label="print">print</i>
                                                    </a>

                                                    <a href="{{host}}label/master/examples/media_label.php?id={{SerialPart.SerialNumber}}" target="_blank" ng-show="PartsRecovery.RecoveryTypeName == 'Component'">
                                                        <i class="material-icons text-danger"  aria-label="print">print</i>
                                                    </a>


                                                    
                                                </div>
                                              </td>
                                              <td md-cell>
                                                  {{SerialPart.parttype}}
                                              </td>
                                              <td md-cell>
                                                  {{SerialPart.input}}
                                              </td>
                                              <td md-cell>
                                                  {{SerialPart.COO}}
                                              </td>
                                              <td md-cell>
                                                  {{SerialPart.MPN}}
                                              </td>

                                              <td md-cell>
                                                <a style="cursor: pointer;" ng-show="SerialPart.rule_name != '' && SerialPart.rule_name != null" ng-click="ShowRuleDetails(SerialPart)">
                                                    <i class="material-icons text-danger"  aria-label="open_in_new">open_in_new</i>
                                                </a>
                                                <span style="background-color: {{SerialPart.color_code}};">{{SerialPart.disposition}}</span>
                                              </td>
                                              <td md-cell>
                                                  {{SerialPart.DispositionBin}}
                                              </td>
                                              <td md-cell>
                                                <span ng-show="(SerialPart.parttype == 'Server' || SerialPart.parttype == 'SERVER' || SerialPart.parttype == 'HDD' || SerialPart.parttype == 'SSD') && PartsRecovery.MaterialType == 'Media Rack'">
                                                    <md-icon class="material-icons verified_field text-success" ng-if="SerialPart.ValidSerial == true">check</md-icon>
                                                    <md-icon class="material-icons notverified_field text-danger" ng-if="SerialPart.ValidSerial == false">close</md-icon>
                                                </span>
                                                  {{SerialPart.SerialNumber}}
                                              </td>
                                          </tr>                                            
                                        </tbody>

                                        <tfoot>
                                            <tr md-row>
                                                <td colspan="8">
                                                </td>
                                            </tr>
                                        </tfoot>

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </md-card>
                <!--Serialized Section Form Close-->


                <!--Unserialized Section Form Start-->
                <md-card class="no-margin-h">
                    <md-toolbar class="md-table-toolbar md-default"ng-init="UnserializedPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="UnserializedPanel = !UnserializedPanel">
                            <i class="material-icons md-primary" ng-show="UnserializedPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! UnserializedPanel">keyboard_arrow_down</i>
                            <span>Unserialized</span>
                            <div flex></div>
                            <span>Count: <strong class="text-success">{{UnserialRecoveryPartsCount}}</strong></span>
                        </div>
                    </md-toolbar>
                    <div class="row" ng-show="UnserializedPanel">
                        <form name="GeneralComponentForm" class="form-validation">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div class="col-md-4" ng-repeat="PartType in UnserializedPartTypes">
                                        <span style="display: flex;" class="mb-10">
                                            <md-checkbox ng-model="PartType.Added" ng-true-value="'1'" ng-false-value="'0'"  style="margin-bottom: 0px; width:25px !important" ng-change="GetUnserializedPartTypeRecoveryDetails(PartType);"></md-checkbox>
                                            {{PartType.parttype}}
                                        </span>
                                    </div>                                    
                                </div>
                            </div>
                        </form>                        
                    </div>
                </md-card>
                <!--Unserialized Section Form Close-->

                <md-card class="no-margin-h">
                    <div>
                        <!--Serilaized Parts Start-->
                        <div class="col-md-12" ng-show="SerilaizedCustomPallets.length > 0">
                                <md-table-container class="table-responsive" style="overflow: auto;">
                                    <table class="md-table mb-10 mt-10">
                                        <thead ng-init="SerializedPartListTable = true">
                                            <tr class="bg-grey">
                                                <th style="width: 300px; display: flex;">
                                                    <i class="material-icons md-primary" ng-show="SerializedPartListTable" style="cursor: pointer; font-size: 24px;" ng-click="SerializedPartListTable = !SerializedPartListTable">keyboard_arrow_up</i>
                                                    <i class="material-icons md-primary" ng-show="! SerializedPartListTable" style="cursor: pointer; font-size: 24px;" ng-click="SerializedPartListTable = !SerializedPartListTable">keyboard_arrow_down</i>
                                                    Serialized Disposition
                                                </th>
                                                <th style="min-width: 200px;">Bin ID</th>
                                                <th class="text-center">Count</th>
                                                <th class="text-center" style="min-width: 140px;">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody ng-show="SerializedPartListTable">

                                            <tr ng-repeat="CustomPallet in SerilaizedCustomPallets">
                                                <td>
                                                    {{CustomPallet.disposition}}
                                                </td>
                                                <td ng-hide="CustomPallet.ShippingContainerID">
                                                    <md-input-container class="md-block md-no-float includedsearch tdinput">
                                                        <input required name="BinName" ng-model="CustomPallet.BinName" required ng-enter="MapCustomPalletToDisposition(CustomPallet)" ng-disabled="CustomPallet.CustomPalletID">
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="MapCustomPalletToDisposition(CustomPallet)" ng-disabled="!CustomPallet.BinName">
                                                            <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                        </md-button>
                                                    </md-input-container>
                                                </td>
                                                <td ng-show="CustomPallet.ShippingContainerID">
                                                    <md-input-container class="md-block md-no-float includedsearch tdinput">
                                                        <input required name="BinName" ng-model="CustomPallet.ShippingContainerID" required ng-enter="MapCustomPalletToDisposition(CustomPallet)">
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="MapCustomPalletToDisposition(CustomPallet)" ng-disabled="!CustomPallet.ShippingContainerID">
                                                            <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                        </md-button>
                                                    </md-input-container>
                                                </td>
                                                <td class="text-center">{{CustomPallet.AssetsCount}}</td>
                                                <td class="text-center">
                                                    <md-menu md-position-mode="target-right target">
                                                        <!-- Actions Menu Button -->
                                                        <md-button aria-label="Actions" ng-click="$mdMenu.open($event)" style="min-width: 80px; padding: 6px 12px;">
                                                            Actions <md-icon class="material-icons" style="font-size: 16px;">arrow_drop_down</md-icon>
                                                        </md-button>

                                                        <!-- Menu Content -->
                                                        <md-menu-content>
                                                            <!-- Create Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="CreateBin(CustomPallet,PartsRecovery.SiteID,$event)" class="create-action">
                                                                    Create Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Move Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="MoveBinToStationLocationGroup(CustomPallet,PartsRecovery.SiteID,$event)" class="move-action">
                                                                    Move Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Close Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="CloseBin(CustomPallet,PartsRecovery.SiteID,$event)" class="close-action">
                                                                    Close Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Consolidate Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="ConsolidateBin(CustomPallet,PartsRecovery.SiteID,$event)" class="consolidate-action">
                                                                    Consolidate Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Nest to Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="NestToBin(CustomPallet,PartsRecovery.SiteID,$event)" class="nest-action">
                                                                    Nest to Bin
                                                                </md-button>
                                                            </md-menu-item>                                                            
                                                        </md-menu-content>
                                                    </md-menu>
                                                </td>
                                            </tr>
                                            <tr><td colspan="4"></td></tr>

                                        </tbody>
                                        <tfoot>
                                        </tfoot>
                                    </table>
                                </md-table-container>
                        </div>
                        <!--Serilaized Parts Close-->

                        <!--Unserilaized Parts Start-->
                        <div class="col-md-12" ng-show="UnserializedCustomPallets.length > 0">
                                <md-table-container class="table-responsive" style="overflow: auto;">
                                    <table class="md-table mt-0 mb-10">
                                        <thead ng-init="UnserializedPartListTable = true">
                                            <tr class="bg-grey">
                                                <th style="width: 300px; display: flex;">
                                                    <i class="material-icons md-primary" ng-show="UnserializedPartListTable" style="cursor: pointer; font-size: 24px;" ng-click="UnserializedPartListTable = !UnserializedPartListTable">keyboard_arrow_up</i>
                                                    <i class="material-icons md-primary" ng-show="! UnserializedPartListTable" style="cursor: pointer; font-size: 24px;" ng-click="UnserializedPartListTable = !UnserializedPartListTable">keyboard_arrow_down</i>
                                                    Unserialized Disposition
                                                </th>
                                                <th style="min-width: 200px;">Container ID</th>
                                                <th class="text-center">Count</th>
                                                <th class="text-center" style="min-width: 140px;">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody ng-show="UnserializedPartListTable">

                                            <tr ng-repeat="pallet in UnserializedCustomPallets">
                                                <td>
                                                    {{pallet.disposition}}
                                                </td>
                                                <td ng-hide="pallet.ShippingContainerID">
                                                    <md-input-container class="md-block md-no-float includedsearch tdinput">
                                                        <input required name="BinNameUS1" ng-model="pallet.BinName" required ng-enter="MapCustomPalletToDisposition(pallet)">
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="MapCustomPalletToDisposition(pallet)" ng-disabled="!pallet.BinName">
                                                            <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                        </md-button>
                                                    </md-input-container>
                                                </td>
                                                <td ng-show="pallet.ShippingContainerID">
                                                    <md-input-container class="md-block md-no-float includedsearch tdinput">
                                                        <input required name="BinNameUS1" ng-model="pallet.ShippingContainerID" required ng-enter="MapCustomPalletToDisposition(pallet)">
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="MapCustomPalletToDisposition(pallet)" ng-disabled="!pallet.ShippingContainerID">
                                                            <i class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                        </md-button>
                                                    </md-input-container>
                                                </td>
                                                <td class="text-center">{{pallet.AssetsCount}}</td>
                                                <td class="text-center">
                                                    <md-menu md-position-mode="target-right target">
                                                        <!-- Actions Menu Button -->
                                                        <md-button aria-label="Actions" ng-click="$mdMenu.open($event)" style="min-width: 80px; padding: 6px 12px;">
                                                            Actions <md-icon class="material-icons" style="font-size: 16px;">arrow_drop_down</md-icon>
                                                        </md-button>

                                                        <!-- Menu Content -->
                                                        <md-menu-content>
                                                            <!-- Create Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="CreateBin(pallet,PartsRecovery.SiteID,$event)" class="create-action">
                                                                    Create Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Move Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="MoveBinToStationLocationGroup(pallet,PartsRecovery.SiteID,$event)" class="move-action">
                                                                    Move Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Close Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="CloseBin(pallet,PartsRecovery.SiteID,$event)" class="close-action">
                                                                    Close Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Consolidate Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="ConsolidateContainer(pallet,PartsRecovery.SiteID,$event)" class="consolidate-action">
                                                                    Consolidate Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Nest to Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="NestToBin(pallet,PartsRecovery.SiteID,$event)" class="nest-action">
                                                                    Nest to Bin
                                                                </md-button>
                                                            </md-menu-item>                                                            
                                                        </md-menu-content>
                                                    </md-menu>
                                                </td>
                                            </tr>

                                            <tr><td colspan="4"></td></tr>

                                        </tbody>
                                        <tfoot>
                                        </tfoot>
                                    </table>
                                </md-table-container>
                        </div>
                        <!--Unserilaized Parts Close-->
                    </div>
                </md-card>

                <md-card class="no-margin-h">
                    <div class="col-md-12">
                        <!-- <div class="col-md-3" ng-show="PartsRecovery.RecoveryTypeName == 'Container'"> -->
                        <div class="col-md-3" ng-hide="PartsRecovery.RecoveryTypeName == 'Container' && PartsRecovery.BatchRecovery == '1' && PartsRecovery.BatchRecoveryCompleted == '0'">
                            <md-input-container class="md-block mb-20">
                              <!--ng-init="RecoveryCompleted='0'-->
                                <md-checkbox ng-model="RecoveryCompleted" ng-true-value="1" ng-false-value="0" >Recovery Completed</md-checkbox>
                            </md-input-container>
                        </div>

                        <div class="col-md-3"  ng-if="PartsRecovery.RecoveryTypeName == 'Assembly'">
                            <md-input-container class="md-block">
                                <label>Verification ID</label>                                        
                                <input type="text" name="VerificationID" id="VerificationID"  ng-model="PartsRecovery.VerificationID" ng-maxlength="30" ng-enter="SaveWIPRecoveryParts($event);" />
                                <!-- <div class="error-sapce">
                                    <div ng-messages="asset_form.VerificationID.$error" multiple ng-if='asset_form.VerificationID.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 200.</div>                           
                                    </div>
                                </div> -->
                            </md-input-container>
                        </div>

                        <div class="col-md-9 btns-row" ng-hide="PartsRecovery.RecoveryTypeName == 'Container' && PartsRecovery.BatchRecovery == '1' && PartsRecovery.BatchRecoveryCompleted == '0'">
                            <button class="md-button md-raised btn-w-md  md-default" ng-click="EnableReoveryType()">Cancel</button>
                            <md-button class="md-raised btn-w-md md-primary btn-w-md" ng-hide="PartsRecovery.RecoveryTypeName == 'Assembly' && PartsRecovery.MaterialType == 'Media Rack'"
                                data-ng-disabled="(SerialRecoveryParts.length == 0 && UnserializedPartTypes.length == 0) || PartsRecovery.busy || !PartsRecovery.idPallet" ng-click="SaveWIPRecoveryParts($event);" >
                                <span ng-show="!PartsRecovery.busy">Save</span>
                                <span ng-show="PartsRecovery.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                            </md-button>
                            <!-- <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="RecoveryCompleted == '0'" ng-click="RecoveryComplete();"> -->
                            <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="RecoveryCompleted == '0' || PartsRecovery.busy || !PartsRecovery.idPallet" ng-click="SaveWIPRecoveryParts($event,true);">
                                <span ng-show="!PartsRecovery.busy">Save & Close</span>
                                <span ng-show="PartsRecovery.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                            </md-button>
                            <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="!PartsRecovery.TopLevelAssetClosed || !PartsRecovery.ReopenAccess || PartsRecovery.busy" ng-click="ReopenPopup(PartsRecovery,$event);">
                                <span ng-show="!PartsRecovery.busy">Reopen</span>
                                <span ng-show="PartsRecovery.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                            </md-button>
                        </div>                        
                    </div>
                </md-card>



                <!--Recovered Serialized Parts starts-->
                <md-card class="no-margin-h">
                    <md-toolbar class="md-table-toolbar md-default" ng-init="RecoveredSerializedPannel = false">
                        <div class="md-toolbar-tools">
                            <i class="material-icons md-primary" ng-show="RecoveredSerializedPannel" style="cursor: pointer;" ng-click="RecoveredSerializedPannel = !RecoveredSerializedPannel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! RecoveredSerializedPannel" style="cursor: pointer;" ng-click="GetRecoveredSerialPartsByTopLeveAssetID();">keyboard_arrow_down</i>
                            <span>Recovered Serialized Parts</span>
                        </div>
                    </md-toolbar>

                    <div ng-show="RecoveredSerializedPannel">
                        <div class="col-md-12">
                            <md-table-container class="table-responsive" style="overflow: auto;">

                                <table class="table mb-10" md-row-select>

                                    <thead>

                                        <tr class="bg-grey">
                                            <th>
                                                   <div> Part Type</div>
                                            </th>
                                            <th>
                                                <div>Evaluation Result</div>
                                            </th>

                                            <th>
                                                <div>COO</div>
                                            </th>

                                            <th>
                                                <div> MPN</div>
                                            </th>


                                            <th>
                                                <div>Disposition</div>
                                            </th>

                                            <th>
                                                <div>Bin</div>
                                            </th>
                                            <th>
                                                <div>Serial No#</div>
                                            </th>

                                        </tr>
                                    </thead>
                                    <tbody>

                                      <tr ng-repeat="SerialPart in SavedSerialParts">
                                          <td>
                                              {{SerialPart.parttype}}
                                          </td>
                                          <td>
                                              {{SerialPart.input}}
                                          </td>
                                          <td>
                                              {{SerialPart.COO}}
                                          </td>
                                          <td>
                                              {{SerialPart.UniversalModelNumber}}
                                          </td>

                                          <td>
                                              {{SerialPart.disposition}}
                                          </td>
                                          <td>
                                              {{SerialPart.BinName}}
                                          </td>
                                          <td>
                                              {{SerialPart.SerialNumber}}
                                          </td>
                                      </tr>
                                </tbody>


                                </table>


                            </md-table-container>
                        </div>
                    </div>
                </md-card>
                <!--Recovered Serialized Parts ends-->

                <!--Recovered Unserialized Parts starts-->
                <md-card class="no-margin-h">
                    <md-toolbar class="md-table-toolbar md-default" ng-init="RecoveredUnserializedPannel = false">
                        <div class="md-toolbar-tools">
                            <i class="material-icons md-primary" ng-show="RecoveredUnserializedPannel" style="cursor: pointer;" ng-click="RecoveredUnserializedPannel = !RecoveredUnserializedPannel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! RecoveredUnserializedPannel" style="cursor: pointer;" ng-click="GetRecoveredUnserialPartsByTopLeveAssetID();">keyboard_arrow_down</i>
                            <span>Recovered Unserialized Parts</span>
                        </div>
                    </md-toolbar>

                    <div ng-show="RecoveredUnserializedPannel">
                        <div class="col-md-12">
                            <md-table-container class="table-responsive" style="overflow: auto;">

                                <table class="table mb-10">
                                    <thead>
                                        <tr class="bg-grey">
                                            <th><div>Part Type</div></th>
                                            <!--<th><div>COO</div></th>-->
                                            <th><div>Disposition</div></th>
                                            <th><div>Bin/Container</div></th>
                                            <!--<th><div>Quantity</div></th>-->
                                        </tr>
                                    </thead>
                                    <tbody>
                                      <tr ng-repeat="UnserialPart in SavedUnserialParts">
                                          <td>
                                              {{UnserialPart.parttype}}
                                          </td>
                                          <!--<td>
                                              {{UnserialPart.COO}}
                                          </td>-->

                                          <td>
                                              {{UnserialPart.disposition}}
                                          </td>
                                          <td ng-hide="UnserialPart.ShippingContainerID">
                                              {{UnserialPart.DispositionBin}}
                                          </td>
                                          <td ng-show="UnserialPart.ShippingContainerID">
                                              {{UnserialPart.ShippingContainerID}}
                                          </td>
                                          <!--<td>
                                              {{UnserialPart.Quantity}}
                                          </td>-->
                                      </tr>
                                    </tbody>


                                </table>
                            </md-table-container>
                    </div>
                    </div>
                </md-card>
                <!--Recovered Unserialized Parts ends-->




            </article>
        </div>
    </div>
</div>