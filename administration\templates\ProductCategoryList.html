<div class="row page  page-invoice" data-ng-controller="ProductCategoryList" >  
    <div class="col-md-12">
        <article class="article">
            
            <div class="row ui-section">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-body" style="padding-top:0px;">
                    
                            <md-toolbar class="md-table-toolbar md-default" ng-hide="options.rowSelection && selected.length">
                                <div class="md-toolbar-tools">
                                    <span>Product Category List</span>
                                    <div flex></div>
                                    <a href="#!/ProductCategory" class="md-button md-raised btn-w-md" style="display: flex;">
                                        <i class="material-icons">add</i> Create New
                                    </a>
                                </div>
                            </md-toolbar>
                            
                            <md-toolbar class="md-table-toolbar alternate" ng-show="options.rowSelection && selected.length">
                                <div class="md-toolbar-tools">
                                    <span>{{selected.length}} {{selected.length > 1 ? 'items' : 'item'}} selected</span>
                                </div>
                            </md-toolbar>
                            
                            <md-table-container>
                                <table md-table md-row-select="options.rowSelection" >
                                    <thead ng-if="!options.decapitate" md-head md-order="query.order" md-on-reorder="logOrder">
                                        <tr md-row>
                                            <th md-column><span>Edit</span></th> 
                                            <th md-column md-order-by="CategoryName"><span>Category Name</span></th>
                                            <th md-column md-order-by="JabilPN"><span>Part No</span></th>
                                            <th md-column md-order-by="CustomerPN"><span>Customer<br />Part No</span></th>
                                            <th md-column md-order-by="StandardCost"><span>Standard<br />Cost</span></th>
                                            <th md-column md-order-by="UnitPrice"><span>Buy Price /<br />Unit</span></th>
                                    <th md-column md-order-by="Commodity_Price"><span>Buy Price /<br />Weight</span></th>
                                    <th md-column md-order-by="DownstreamUnitPrice"><span>Sell Price /<br />Unit</span></th>
                                    <th md-column md-order-by="DownstreamLBPrice"><span>Sell Price /<br />Weight</span></th>
                                    <th md-column md-order-by="ProductClassName"><span>Class Name</span></th>
                                    
                                            <th md-column md-order-by="CategoryStatus"><span>Category<br />Status</span></th>                                
                                        </tr>
                                        <!-- <tr md-row class="search_filters">
                                            <th md-column></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="category_name" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="part_no" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="cus_partno" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="standard_cost" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="buy_price_unit" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="buy_price_weight" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="sell_price_unit" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="sell_price_weight" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="class_name" placeholder="Search" /></md-input-container></th>
                                            <th md-column><md-input-container md-no-float class="md-block"><input type="text" name="category" placeholder="Search" /></md-input-container></th>                                           

                                        </tr> -->
                                    </thead>
                                    <tbody md-body>
                                        <tr md-row ng-repeat="dessert in desserts.data | filter: filter.search | orderBy: query.order | limitTo: query.limit : (query.page -1) * query.limit">
                                            <td md-cell>
                                                <a href="#!/ProductCategory/{{dessert.ProductCatID}}"><md-icon class="text-danger">edit</md-icon></a>
                                            </td>
                                                                            
                                            <td md-cell>{{dessert.CategoryName}}</td>
                                            <td md-cell>{{dessert.JabilPN}}</td>
                                            <td md-cell>{{dessert.CustomerPN}}</td>
                                            <td md-cell>{{dessert.StandardCost}}</td>
                                            <td md-cell>
                                                <div class="row td_edit_input" ng-show="dessert.EditUnitPrice">
                                                    <!--{{dessert.DownstreamUnitPrice}}-->
                                                    <div class="col-md-8 p0">
                                                        <md-input-container md-no-float class="md-block"><input type="text" name="xyz" ng-model="dessert.UnitPrice" />
                                                    </div>
                                                    <div class="col-md-4 p0">
                                                        <md-icon class="pull-right text-success action_icon" style="font-size: 20px;" ng-click="SaveUnitPrice(dessert,'UnitPrice',dessert.UnitPrice,'EditUnitPrice',dessert.StandardCost)">check_circle</md-icon>
                                                    </div>
                                                </div>
                                                <div class="row" ng-show="! dessert.EditUnitPrice">
                                                    {{dessert.UnitPrice}} 
                                                    <md-icon class="pull-right text-danger action_icon" ng-click="dessert.EditUnitPrice = ! dessert.EditUnitPrice">edit</md-icon>
                                                </div>
                                            </td>
                                            <!-- <td md-cell>{{dessert.Commodity_Price}} <md-icon class="pull-right text-danger action_icon">edit</md-icon>
                                            </td> -->

                                            <td md-cell>
                                                <div class="row td_edit_input" ng-show="dessert.EditCommodity_Price">
                                                    <div class="col-md-8 p0">
                                                        <md-input-container md-no-float class="md-block"><input type="text" name="xyz" ng-model="dessert.Commodity_Price" />
                                                    </div>
                                                    <div class="col-md-4 p0">
                                                        <md-icon class="pull-right text-success action_icon" style="font-size: 20px;" ng-click="SaveCommodity_Price(dessert,'Commodity_Price',dessert.Commodity_Price,'EditCommodity_Price',dessert.StandardCost)">check_circle</md-icon>
                                                    </div>
                                                </div>
                                                <div class="row" ng-show="! dessert.EditCommodity_Price">
                                                    {{dessert.Commodity_Price}} 
                                                    <md-icon class="pull-right text-danger action_icon" ng-click="dessert.EditCommodity_Price = ! dessert.EditCommodity_Price">edit</md-icon>
                                                </div>
                                            </td>

                                            <!-- <td md-cell>{{dessert.DownstreamUnitPrice}} <md-icon class="pull-right text-danger action_icon">edit</md-icon></td> -->

                                            <td md-cell>
                                                <div class="row td_edit_input" ng-show="dessert.EditDownstreamUnitPrice">
                                                    <div class="col-md-8 p0">
                                                        <md-input-container md-no-float class="md-block"><input type="text" name="xyz" ng-model="dessert.DownstreamUnitPrice" />
                                                    </div>
                                                    <div class="col-md-4 p0">
                                                        <md-icon class="pull-right text-success action_icon" style="font-size: 20px;" ng-click="SaveDownstreamUnitPrice(dessert,'DownstreamUnitPrice',dessert.DownstreamUnitPrice,'EditDownstreamUnitPrice',dessert.StandardCost)">check_circle</md-icon>
                                                    </div>
                                                </div>
                                                <div class="row" ng-show="! dessert.EditDownstreamUnitPrice">
                                                    {{dessert.DownstreamUnitPrice}} 
                                                    <md-icon class="pull-right text-danger action_icon" ng-click="dessert.EditDownstreamUnitPrice = ! dessert.EditDownstreamUnitPrice">edit</md-icon>
                                                </div>
                                            </td>


                                           <!--  <td md-cell>{{dessert.DownstreamLBPrice}} <md-icon class="pull-right text-danger action_icon">edit</md-icon></td> -->

                                            <td md-cell>
                                                <div class="row td_edit_input" ng-show="dessert.EditDownstreamLBPrice">
                                                    <div class="col-md-8 p0">
                                                        <md-input-container md-no-float class="md-block"><input type="text" name="xyz" ng-model="dessert.DownstreamLBPrice" />
                                                    </div>
                                                    <div class="col-md-4 p0">
                                                        <md-icon class="pull-right text-success action_icon" style="font-size: 20px;" ng-click="SaveDownstreamLBPrice(dessert,'DownstreamLBPrice',dessert.DownstreamLBPrice,'EditDownstreamLBPrice',dessert.StandardCost)">check_circle</md-icon>
                                                    </div>
                                                </div>
                                                <div class="row" ng-show="! dessert.EditDownstreamLBPrice">
                                                    {{dessert.DownstreamLBPrice}} 
                                                    <md-icon class="pull-right text-danger action_icon" ng-click="dessert.EditDownstreamLBPrice = ! dessert.EditDownstreamLBPrice">edit</md-icon>
                                                </div>
                                            </td>
                                            
                                            <td md-cell>{{dessert.ProductClassName}}</td>
                                            <td md-cell>
                                                <span ng-show="dessert.CategoryStatus == '1'">Active</span>
                                                <span ng-show="dessert.CategoryStatus == '0'">Inactive</span>
                                            </td>                               
                                        </tr>
                                    </tbody>
                                </table>
                            </md-table-container>

                            <md-table-pagination md-limit="query.limit" md-limit-options="limitOptions" md-page="query.page" md-total="{{desserts.count}}" md-page-select="options.pageSelect" md-boundary-links="options.boundaryLinks" md-on-paginate="logPagination"></md-table-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </article>                            
    </div>
</div>