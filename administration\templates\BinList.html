<div ng-controller = "BinList" class="page">
    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">

                <style>
                    .icon_btn{padding: 0 !important;  margin: 0 !important;  line-height: 24px !important;  min-height: 24px !important; height: 24px !important;}
                    .table > thead > tr > th, .table > thead > tr > td, .table > tbody > tr > th, .table > tbody > tr > td, .table > tfoot > tr > th, .table > tfoot > tr > td{padding: 4px 8px 4px 0px !important;}
                </style>

                <script type="text/ng-template" id="closeBinModal.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Closing Bin ({{CurrentBin.BinName}})
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">
                                <form name="closeBinForm">
                                    <md-input-container class="md-block">
                                        <label>Controller</label>
                                        <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="100" type="text" ng-enter="FocusNextField('password','0')">
                                        <div ng-messages="closeBinForm.AuditController.$error" multiple ng-if='closeBinForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>
                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="FocusNextField('SealID','0')">
                                        <div ng-messages="closeBinForm.Password.$error" multiple ng-if='closeBinForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Seal ID</label>
                                        <input required name="SealID" id="SealID" ng-model="confirmDetails.NewSealID" ng-maxlength="100" type="text" ng-enter="FocusNextField('BinWeight','0')">
                                        <div ng-messages="closeBinForm.SealID.$error" multiple ng-if='closeBinForm.SealID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Weight</label>
                                        <input required name="BinWeight" id="BinWeight" ng-model="confirmDetails.BinWeight" ng-max="999999" ng-min="0" type="number" ng-enter="hide()">
                                        <div ng-messages="closeBinForm.BinWeight.$error" multiple ng-if='closeBinForm.BinWeight.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="max">Maximum value is 999999.</div>
                                            <div ng-message="min">Minimum value is 0.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="closeBinForm.$invalid">Continue</button>
                        </div>
                    </div>
                </script>

                <script type="text/ng-template" id="reopenBinModal.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Reopening Bin (Bin ID : {{CurrentBin.CustomPalletID}})
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">
                                <form name="reopenBinForm">
                                    <md-input-container class="md-block">
                                        <label>Controller</label>
                                        <input required name="AuditController" id="ReopenAuditController" ng-model="confirmDetails.AuditController" ng-maxlength="100" type="text" ng-enter="FocusNextField('reopenPassword','0')">
                                        <div ng-messages="reopenBinForm.AuditController.$error" multiple ng-if='reopenBinForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>
                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="reopenPassword" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="hide()">
                                        <div ng-messages="reopenBinForm.Password.$error" multiple ng-if='reopenBinForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="reopenBinForm.$invalid">Continue</button>
                        </div>
                    </div>
                </script>

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="BinList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">    
                                 <i ng-click="BinList = !BinList" class="material-icons md-primary" ng-show="BinList">keyboard_arrow_up</i>
                                <i ng-click="BinList = !BinList" class="material-icons md-primary" ng-show="! BinList">keyboard_arrow_down</i>
                                <span ng-click="BinList = !BinList">Bin List</span>
                                <div flex></div>

                                 <a href="#!/BinList" ng-click="BinListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>
                                <div class="upload-btn-wrapper text-center mt-5">
                                    <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                                    <input type="file" ng-file-select="onFileSelect($files)" id="BinFile">  
                                    <a href="../../sample_files/upload_Bin_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                                </div> 

                                <a href="#!/BinList" ng-click="BinListxls()" class="md-button md-raised md-default dis_open_v mr-5" style="display:none; min-width: 40px;">
                                    <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon>
                                </a>
                                 
                                <a href="#!/Bin" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Bin
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="BinList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>
                                    <div class="table-responsive" style="overflow: auto;">                                                                                                                                                                            
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 120px;">Actions</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('BinName')" ng-class="{'orderby' : OrderBy == 'BinName'}">
                                                        <div>
                                                            Bin Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'BinName'"></i>
                                                            <span ng-show="OrderBy == 'BinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('BinType')" ng-class="{'orderby' : OrderBy == 'BinType'}">
                                                        <div>
                                                            Bin Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'BinType'"></i>
                                                            <span ng-show="OrderBy == 'BinType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th> -->

                                                    <th style="cursor:pointer; min-width: 270px;" ng-click="MakeOrderBy('ParentBinName')" ng-class="{'orderby' : OrderBy == 'ParentBinName'}">
                                                        <div>
                                                            Parent Bin <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ParentBinName'"></i>
                                                            <span ng-show="OrderBy == 'ParentBinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LocationType')" ng-class="{'orderby' : OrderBy == 'LocationType'}">
                                                        <div>
                                                            Location Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationType'"></i>
                                                            <span ng-show="OrderBy == 'LocationType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer; min-width: 270px;" ng-click="MakeOrderBy('GroupName')" ng-class="{'orderby' : OrderBy == 'GroupName'}">
                                                        <div>
                                                            Location Group <i class="fa fa-sort pull-right" ng-show="OrderBy != 'GroupName'"></i>
                                                            <span ng-show="OrderBy == 'GroupName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
    
                                                    <th style="cursor:pointer; min-width: 270px;" ng-click="MakeOrderBy('LocationName')" ng-class="{'orderby' : OrderBy == 'LocationName'}">                           
                                                        <div>                               
                                                            Location <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationName'"></i>                                    
                                                            <span ng-show="OrderBy == 'LocationName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th>                           
                                                        <div>                                                            	
                                                            Qty x Part Type                                                                                                                                                       
                                                        </div>                                                                                  
                                                    </th>

                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('LocationName')" ng-class="{'orderby' : OrderBy == 'LocationName'}">                           
                                                        <div>                               
                                                            Location <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationName'"></i>                                    
                                                            <span ng-show="OrderBy == 'LocationName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->

                                                   <!--  <th style="cursor:pointer;" ng-click="MakeOrderBy('workflow')" ng-class="{'orderby' : OrderBy == 'workflow'}">                          
                                                        <div>                               
                                                            workflow <i class="fa fa-sort pull-right" ng-show="OrderBy != 'workflow'"></i>                                  
                                                            <span ng-show="OrderBy == 'workflow'">                                  
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->
                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('Room')" ng-class="{'orderby' : OrderBy == 'Room'}">                          
                                                        <div>                               
                                                            Room <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Room'"></i>                                    
                                                            <span ng-show="OrderBy == 'Room'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->

                                                    <th>                          
                                                        <div>                               
                                                            Mapped Stations                                                                                                                                                            
                                                        </div>                                                                                  
                                                    </th>
                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                        <div>
                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>
                                                            <span ng-show="OrderBy == 'disposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ContainerWeight')" ng-class="{'orderby' : OrderBy == 'ContainerWeight'}">
                                                        <div>
                                                            Weight <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ContainerWeight'"></i>
                                                            <span ng-show="OrderBy == 'ContainerWeight'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SealID')" ng-class="{'orderby' : OrderBy == 'SealID'}">
                                                        <div>
                                                            Seal ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'SealID'"></i>
                                                            <span ng-show="OrderBy == 'SealID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">
                                                        <div>
                                                            Notes <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>
                                                            <span ng-show="OrderBy == 'Description'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">
                                                        <div style="min-width: 80px;">
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                            <span ng-show="OrderBy == 'Status'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('MobilityName')" ng-class="{'orderby' : OrderBy == 'MobilityName'}">
                                                        <div>
                                                            Mobility Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'MobilityName'"></i>
                                                            <span ng-show="OrderBy == 'MobilityName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="BinName" ng-model="filter_text[0].BinName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ParentBinName" ng-model="filter_text[0].ParentBinName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationType" ng-model="filter_text[0].LocationType" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="BinType" ng-model="filter_text[0].BinType" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->

                                                    <td >
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="GroupName" ng-model="filter_text[0].GroupName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }'  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
    
                                                    <td >
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationName" ng-model="filter_text[0].LocationName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }'  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td></td>
                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationName" ng-model="filter_text[0].LocationName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="workflow" ng-model="filter_text[0].workflow" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Room" ng-model="filter_text[0].Room" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <td></td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ContainerWeight" ng-model="filter_text[0].ContainerWeight" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SealID" ng-model="filter_text[0].SealID" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Notes" ng-model="filter_text[0].Description" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" placeholder="Filter by Notes" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="MobilityName" ng-model="filter_text[0].MobilityName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td>
                                                        <md-menu md-position-mode="target-right target">
                                                            <!-- Actions Menu Button -->
                                                            <md-button aria-label="Actions" ng-click="$mdMenu.open($event)" style="min-width: 80px; padding: 6px 12px;">
                                                                Actions <md-icon class="material-icons" style="font-size: 16px;">arrow_drop_down</md-icon>
                                                            </md-button>

                                                            <!-- Menu Content -->
                                                            <md-menu-content>
                                                                <!-- Edit Action -->
                                                                <md-menu-item>
                                                                    <md-button href="#!/Bin/{{product.CustomPalletID}}" class="edit-action">
                                                                        Edit
                                                                    </md-button>
                                                                </md-menu-item>

                                                                <!-- Print Action -->
                                                                <md-menu-item>
                                                                    <md-button href="{{host}}label/master/examples/binlabel.php?id={{product.CustomPalletID}}" target="_blank" class="print-action">
                                                                        Print
                                                                    </md-button>
                                                                </md-menu-item>

                                                                <!-- Close/Reopen Action -->
                                                                <md-menu-item ng-if="product.StatusID == '1'">
                                                                    <md-button class="close-action" ng-click="CloseBin(product,$event)">
                                                                        Close
                                                                    </md-button>
                                                                </md-menu-item>
                                                                <md-menu-item ng-if="product.StatusID != '1' && product.StatusID != '5'">
                                                                    <md-button class="reopen-action" ng-click="ReopenBin(product,$event)">
                                                                        Reopen
                                                                    </md-button>
                                                                </md-menu-item>

                                                                <!-- Delete Action -->
                                                                <md-menu-item ng-if="product.StatusID != '5'">
                                                                    <md-button class="delete-action" ng-click="DeleteBin(product,$event)">
                                                                        Delete
                                                                    </md-button>
                                                                </md-menu-item>
                                                            </md-menu-content>
                                                        </md-menu>
                                                    </td>
                                                    <td>
                                                        <md-icon class="material-icons text-primary" style="cursor: pointer; font-size: 16px; margin-right: 8px; vertical-align: middle;"
                                                                ng-click="showBinTrackingHistory(product)"
                                                                title="View Bin History">
                                                            history
                                                        </md-icon>
                                                        {{product.BinName}}
                                                    </td>

                                                    <!-- <td>
                                                        {{product.BinType}}
                                                    </td> -->

                                                    <td>
                                                        <div class="autocomplete insideuse">
                                                            <md-autocomplete required style="width: 180px;"
                                                                ng-disabled="product.Received == 0"
                                                                md-no-cache="noCache"
                                                                md-search-text-change="ParentBinChange1(product.parentBin,product)"
                                                                md-search-text="product.parentBin"
                                                                md-items="item in queryParentBinSearch1(product.parentBin,product)"
                                                                md-item-text="item.BinName"
                                                                md-selected-item-change="selectedParentBinChange1(item,product)"
                                                                md-min-length="0"
                                                                ng-model-options='{ debounce: 1000 }'
                                                                placeholder="Search Parent Bin">
                                                                <md-item-template>
                                                                    <span md-highlight-text="product.parentBin" md-highlight-flags="^i">{{item.BinName}}</span>
                                                                </md-item-template>
                                                                <md-not-found>
                                                                    No Records matching "{{product.parentBin}}" were found.
                                                                </md-not-found>
                                                            </md-autocomplete>
                                                            <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-accent"  type="button" ng-disabled="! product.parentBin" ng-click="UpdateBinParent(product,$event)">
                                                                <!-- Update -->

                                                                <span ng-show="! product.busy">Update</span>
                                                                <span ng-show="product.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>

                                                            </button>
                                                        </div>
                                                    </td>

                                                    <td>
                                                        {{product.LocationType}}
                                                    </td>

                                                    <td>
                                                        <div class="autocomplete insideuse">
                                                            <md-autocomplete required style="width: 180px;"
                                                                ng-disabled="product.Received == 0"
                                                                md-no-cache="noCache"
                                                                md-search-text-change="ContainerLocationChange1(product.group,product)"
                                                                md-search-text="product.group"
                                                                md-items="item in queryContainerLocationSearch1(product.group,product)"
                                                                md-item-text="item.GroupName"
                                                                md-selected-item-change="selectedContainerLocationChange1(item,product)"
                                                                md-min-length="0"
                                                                ng-model-options='{ debounce: 1000 }'
                                                                placeholder="Search Location Group">
                                                                <md-item-template>
                                                                    <span md-highlight-text="product.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                                </md-item-template>
                                                                <md-not-found>
                                                                    No Records matching "{{product.group}}" were found.
                                                                </md-not-found>
                                                            </md-autocomplete>
                                                            <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-accent"  type="button" ng-disabled="! product.group" ng-click="UpdateBinLocationGroup(product,$event)">
                                                                <!-- Update -->

                                                                <span ng-show="! product.busy">Update</span>
                                                                <span ng-show="product.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular"></span>

                                                            </button>
                                                        </div>
                                                    </td>
    
                                                    <td>
                                                        {{product.LocationName}}                                                        
                                                    </td>

                                                    <td>
                                                        <!-- {{product.parttype}}                             -->
                                                        <span ng-click="UpdateBinPartTypeSummary(product)"><md-icon class="material-icons text-danger">refresh</md-icon></a></span>
                                                        {{product.PartTypeSummary}}
                                                    </td>                       
                                                    <!-- <td>
                                                        {{product.LocationName}}                            
                                                    </td>  -->
                                                    <!-- <td>
                                                        {{product.workflow}}
                                                    </td> -->
                                                    <!-- <td>
                                                        {{product.Room}}
                                                    </td> -->
                                                    <td>
                                                        {{product.MappedStations}}
                                                    </td>
                                                    <td>
                                                        {{product.disposition}}
                                                    </td>
                                                    <td>
                                                        {{product.ContainerWeight}}
                                                    </td>
                                                    <td>
                                                        {{product.SealID}}
                                                    </td>
                                                    <td>
                                                        {{product.Description}}
                                                    </td>
                                                    <td>
                                                        {{product.Status}}
                                                    </td>
                                                    <td>
                                                        {{product.MobilityName}}
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="14">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

    <!-- Bin Tracking History Dialog Template -->
    <script type="text/ng-template" id="binTrackingDialog.html">
        <md-dialog aria-label="Bin History" style="min-width: 800px; max-width: 90vw;">
            <md-toolbar>
                <div class="md-toolbar-tools">
                    <md-icon class="material-icons">history</md-icon>
                    <h2>Bin History: {{selectedBin.BinName}}</h2>
                    <span flex></span>
                    <md-button class="md-icon-button" ng-click="closeDialog()">
                        <md-icon class="material-icons">close</md-icon>
                    </md-button>
                </div>
            </md-toolbar>

            <md-dialog-content style="max-width: none; max-height: 70vh;">
                <div class="md-dialog-content" style="padding: 20px;">

                    <!-- Loading State -->
                    <div ng-show="trackingHistoryLoading" class="text-center" style="padding: 40px;">
                        <md-progress-circular md-mode="indeterminate" md-diameter="40"></md-progress-circular>
                        <p style="margin-top: 20px;">Loading bin history...</p>
                    </div>

                    <!-- Empty State -->
                    <div ng-show="!trackingHistoryLoading && trackingHistory.length === 0" class="text-center" style="padding: 40px;">
                        <md-icon class="material-icons" style="font-size: 48px; opacity: 0.5; color: #999;">history</md-icon>
                        <p style="margin-top: 20px; color: #666;">No history records found for this bin.</p>
                    </div>

                    <!-- History Table -->
                    <div ng-show="!trackingHistoryLoading && trackingHistory.length > 0">
                        <div style="margin-bottom: 20px;">
                            <small style="color: #666;">
                                <md-icon class="material-icons" style="font-size: 14px; vertical-align: middle;">info</md-icon>
                                Showing {{trackingHistory.length}} history record{{trackingHistory.length !== 1 ? 's' : ''}}
                            </small>
                        </div>

                        <div class="tracking-table-responsive">
                            <table class="tracking-table tracking-table-striped tracking-table-hover">
                                <thead style="background-color: #f5f5f5;">
                                    <tr>
                                        <th style="width: 150px;">Date & Time</th>
                                        <th style="width: 120px;">User</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="record in trackingHistory">
                                        <td style="vertical-align: middle;">
                                            <div style="font-size: 13px;">
                                                <div style="font-weight: 500;">{{record.CreatedDate}}</div>
                                                <!-- <div style="color: #666;">{{record.CreatedDate | date:'HH:mm:ss'}}</div> -->
                                            </div>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <div style="font-size: 13px; font-weight: 500;">
                                                {{record.CreatedByName || 'System'}}
                                            </div>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <div style="font-size: 13px; line-height: 1.4;">
                                                {{record.Action}}
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </md-dialog-content>

            <md-dialog-actions layout="row">
                <span flex></span>
                <md-button ng-click="closeDialog()" class="md-primary">
                    Close
                </md-button>
            </md-dialog-actions>
        </md-dialog>
    </script>

</div>

<style>
/* Bin History Table Styles - Scoped to tracking table only */
.tracking-table-responsive {
    border: 1px solid #ddd;
    border-radius: 4px;
}

.tracking-table th {
    border-top: none;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: #666;
    padding: 12px 8px;
}

.tracking-table td {
    padding: 12px 8px;
    border-top: 1px solid #eee;
}

.tracking-table-striped tbody tr:nth-of-type(odd) {
    background-color: #f9f9f9;
}

.tracking-table-hover tbody tr:hover {
    background-color: #f0f8ff;
}
</style>

