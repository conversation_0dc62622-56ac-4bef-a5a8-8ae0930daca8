
<div class="row page" data-ng-controller="Profile">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Profile</span>
                        <div flex></div>
                        <a href="#!/ProfileList" class="md-button md-raised btn-w-md" style="display: flex;">
                            <i class="material-icons">chevron_left</i> Back to List
                        </a>
                    </div>
                </md-toolbar>

                <div>

                    <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                        <fieldset>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Name</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->
                                    <input type="text" name="ProfileName"  ng-model="newProfile['ProfileName']"  required ng-maxlength="45" />
                                    <div ng-messages="material_signup_form.ProfileName.$error" multiple ng-if='material_signup_form.ProfileName.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>
                                    </div>
                                </md-input-container>
                            </div>

                            
                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.CustomerBased" aria-label="CustomerBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Customer Based </md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.LaborTrackingRequired" aria-label="LaborTrackingRequired" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Labor Tracking Required </md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.AssetReopenPermission" aria-label="AssetReopenPermission" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Asset Reopen Permission </md-checkbox>
                                </md-input-container>
                            </div>

                            <!-- <div class="col-md-3"><md-input-container class="md-block" >
                                <md-checkbox ng-model="newProfile.RefCustomerBased" aria-label="RefCustomerBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Reference Customer Based </md-checkbox>
                            </md-input-container></div>

                            <div class="col-md-3"><md-input-container class="md-block" >
                                <md-checkbox ng-model="newProfile.SalesBased" aria-label="SalesBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Sales Based </md-checkbox>
                            </md-input-container></div>

                            <div class="col-md-3"><md-input-container class="md-block" >
                                <md-checkbox ng-model="newProfile.VendorBased" aria-label="VendorBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Vendor Based </md-checkbox>
                            </md-input-container></div> -->

                            <div class="col-md-3">
                                    <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="ProfileStatus" ng-model="newProfile.ProfileStatus" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="0"> In active </md-option>
                                    </md-select>
                                    <div ng-messages="material_signup_form.ProfileStatus.$error" multiple ng-if='material_signup_form.ProfileStatus.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-12">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->
                                    <textarea name="ProfileDesc" ng-minlength="3"  ng-model="newProfile['ProfileDesc']"  required ng-maxlength="250" ></textarea>

                                    <div ng-messages="material_signup_form.ProfileDesc.$error" multiple ng-if='material_signup_form.ProfileDesc.$dirty'>
                                        <div ng-message="required">This is required.</div>

                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>
                                    </div>
                                </md-input-container>
                            </div>


                            <!-- <div class="divider"></div>
                            <md-button
                                class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="CreateProfile()">Save</md-button> -->

                            <div class="col-md-12 btns-row">
                                <!-- <button class="md-button md-raised btn-w-md">
                                    Cancel
                                </button> -->
                                <!-- <button class="md-button md-raised btn-w-md md-default custom-btn" ng-click="CreateProfile()" data-ng-disabled="material_signup_form.$invalid">
                                    Save
                                </button> -->
                                <md-button
                                class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || newProfile.busy" ng-click="CreateProfile()">
                                <span ng-show="! newProfile.busy">Save</span>
                             <span ng-show="newProfile.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>

                        </fieldset>
                    </form>


                </div>

            </md-card>
        </article>
    </div>

</div>
