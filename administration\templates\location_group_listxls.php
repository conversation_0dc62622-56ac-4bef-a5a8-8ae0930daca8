<?php
session_start();
include_once("../../config.php");
$data = $_SESSION['Location_Group_listxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "LocationGroupList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Location Group List');
$header = array('Group Name','Group Description','Facility','Total Locations','Unlocked Locations','Location Type','Status');

$sql = "select l.*,f.FacilityName,ss.StatusName from location_group l
			  left join facility f on l.FacilityID = f.FacilityID
			  left join statusses ss on l.LocationStatus = ss.StatusID where 1 ";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                   if ($key == 'GroupName') {
						$sql = $sql . " AND l.GroupName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
					}
					if ($key == 'GroupDescription') {
						$sql = $sql . " AND l.GroupDescription like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
					}
					if ($key == 'TotalLocations') {
						$sql = $sql . " AND l.TotalLocations like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
					}
					if ($key == 'UnlockedLocations') {
						$sql = $sql . " AND UnlockedLocations like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
					}
					if ($key == 'LocationType') {
						$sql = $sql . " AND l.LocationType like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
					}
					if ($key == 'StatusName') {
						$sql = $sql . " AND ss.StatusName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
					}
					if ($key == 'FacilityName') {
						$sql = $sql . " AND f.FacilityName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
					}
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

            if ($data['OrderBy'] == 'GroupName') {
					$sql = $sql . " order by l.GroupName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'GroupDescription') {
					$sql = $sql . " order by l.GroupDescription " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TotalLocations') {
					$sql = $sql . " order by l.TotalLocations " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'UnlockedLocations') {
					$sql = $sql . " order by UnlockedLocations " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LocationType') {
					$sql = $sql . " order by l.LocationType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StatusName') {
					$sql = $sql . " order by ss.StatusName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'FacilityName') {
					$sql = $sql . " order by f.FacilityName " . $order_by_type . " ";
				}           
        } else {
            $sql = $sql . " order by GroupName asc ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
            while($row = mysqli_fetch_assoc($query))
            {
            	//Start get unlocked locations count
				$query1 = "select count(*) from location where GroupID = '".$row['GroupID']."' and Locked = '2'";
				$q1 = mysqli_query($connectionlink1,$query1);
				
				if(mysqli_affected_rows($connectionlink1) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$row['UnlockedLocations'] = $row1['count(*)'];
				} else {
					$row['UnlockedLocations'] = 0;
				}
				//End get unlocked locations count
				$row2  = array($row['GroupName'],$row['GroupDescription'],$row['FacilityName'],$row['TotalLocations'],$row['UnlockedLocations'],$row['LocationType'],$row['StatusName']);
                $rows[] = $row2;
            }

$sheet_name = 'Location Group List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 