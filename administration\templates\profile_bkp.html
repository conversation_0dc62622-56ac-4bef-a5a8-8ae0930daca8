<div class="row page">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default" ng-hide="options.rowSelection && selected.length">
                    <div class="md-toolbar-tools">
                        <span>Create Account</span>
                        <div flex></div>
                        <a href="#!/administration/account" class="md-button md-raised btn-w-md" style="display: flex;">
                            <i class="material-icons">arrow_back</i> Back To Account List
                        </a>
                    </div>
                </md-toolbar>
                
                <div>
                    
                    <div class="col-md-4">
                        <md-input-container class="md-block">
                            <label>Account Name</label>
                            <input ng-model="account.name">
                        </md-input-container>
                    </div>

                    <div class="col-md-4">
                        <md-input-container class="md-block">
                            <label>Description</label>
                            <input ng-model="account.description">
                        </md-input-container>
                    </div>

                    <div class="col-md-4">
                        <md-checkbox aria-label="Checkbox 1" class="mt-10"> Active </md-checkbox>
                    </div>

                    <div class="col-md-12 text-center" style="margin-bottom: 20px;">
                        <button class="md-button md-raised btn-w-md md-default custom-btn">
                            Create Account
                        </button>
                    </div>

                </div>

            </md-card>
        </article>
    </div>
</div>



<div class="row page">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default" ng-hide="options.rowSelection && selected.length">
                    <div class="md-toolbar-tools">
                        <span>Create Hallmark Center</span>
                        <div flex></div>
                        <a href="#!/administration/hallmark-center" class="md-button md-raised btn-w-md" style="display: flex;">
                            <i class="material-icons">arrow_back</i> Back To Hallmark Center
                        </a>
                    </div>
                </md-toolbar>
                
                <div class="">

                    <div class="col-md-offset-2 col-md-8">    
                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>Account Name</label>
                                <md-select ng-model="accnameddl.accname" aria-label="select">
                                    <md-option ng-repeat="accname in accnameddl.accname" value="{{accname.abbrev}}"> {{accname.abbrev}} </md-option>
                                </md-select>
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>Center Name</label>
                                <input ng-model="hmcenter.centername">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>Address 1</label>
                                <input ng-model="hmcenter.addressone">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>Address 2</label>
                                <input ng-model="hmcenter.addresstwo">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>Address 3</label>
                                <input ng-model="hmcenter.addressthree">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>PIN Code</label>
                                <input ng-model="hmcenter.pincode">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>Phone Number</label>
                                <input ng-model="hmcenter.phonenumber">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>Cell Number</label>
                                <input ng-model="hmcenter.cellnumber">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>Email</label>
                                <input ng-model="hmcenter.email">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>BIS License Number</label>
                                <input ng-model="hmcenter.bis">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-input-container class="full-width">
                                <label>GSTIN</label>
                                <input ng-model="hmcenter.gstin">
                            </md-input-container>
                        </div>

                        <div class="col-md-6">
                            <md-checkbox aria-label="Checkbox 1" class="mt-10"> Active </md-checkbox>
                        </div>

                    </div>                            

                    <div class="col-md-12 btns-row">
                        <button class="md-button md-raised btn-w-md">
                            Cancel
                        </button>
                        <button class="md-button md-raised btn-w-md md-default custom-btn">
                            Create
                        </button>
                    </div>

                </div>

            </md-card>
        </article>
    </div>
</div>



<div class="row page  page-invoice" data-ng-controller="Profile" style="display: none;">  
    <!-- <div class="invoice-wrapper">
        <section class="invoice-container" id="invoice">
        Here we go
        </section>
    </div> -->

    <div class="row invoice-wrapper">    
        <h2 class="section-header">Profile</h2>       

        <div class="panel panel-default">
            <div class="panel-body " >
                <div class="row">
                    <div class="col-lg-4 col-lg-offset-4">

                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <fieldset>
                                <md-input-container class="md-block">
                                    <label>Name</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="ProfileName"  ng-model="newProfile['ProfileName']"  required />
                                    <div ng-messages="material_signup_form.ProfileName.$error" multiple ng-if='material_signup_form.ProfileName.$dirty'>                            
                                        <div ng-message="required">This is required.</div>                            
                                    </div>
                                </md-input-container>

                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <textarea name="ProfileDesc" ng-minlength="3"  ng-model="newProfile['ProfileDesc']"  required ng-maxlength="250" ></textarea>
                                    
                                    <div ng-messages="material_signup_form.ProfileDesc.$error" multiple ng-if='material_signup_form.ProfileDesc.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>
                                    </div>
                                </md-input-container>

                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.CustomerBased" aria-label="CustomerBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Customer Based </md-checkbox>
                                </md-input-container>
                                
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.RefCustomerBased" aria-label="RefCustomerBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Reference Customer Based </md-checkbox>
                                </md-input-container>
            
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.SalesBased" aria-label="SalesBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Sales Based </md-checkbox>
                                </md-input-container>
            
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.VendorBased" aria-label="VendorBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Vendor Based </md-checkbox>
                                </md-input-container>

                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="ProfileStatus" ng-model="newProfile.ProfileStatus" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="0"> In active </md-option>
                                    </md-select>   
                                    <div ng-messages="material_signup_form.ProfileStatus.$error" multiple ng-if='material_signup_form.ProfileStatus.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                                
                                <div class="divider"></div>
                                <md-button 
                                    class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="material_signup_form.$invalid" ng-click="CreateProfile()">Save</md-button>
                                
                            </fieldset>
                        </form>


                    </div>
                </div>
            </div>
        </div>        
    </div>    

    <div class="col-md-12" style="display: none;">
        <div class="panel panel-default">
            <div class="panel-body " >
                <div class="row">
                    <div class="col-lg-4 col-lg-offset-4">

                        <form name="profileform" class="form-validation">
                            <fieldset>
                                <md-input-container class="md-block">
                                    <label>Name</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="ProfileName" ng-minlength="3"  ng-model="newProfile['ProfileName']"  required ng-maxlength="50" />
                                    <div ng-messages="profileform.ProfileName.$error" multiple ng-if='profileform.ProfileName.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>
                                    </div>
                                </md-input-container>

                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <textarea name="ProfileDesc" ng-minlength="3"  ng-model="newProfile['ProfileDesc']"  required ng-maxlength="250" ></textarea>
                                    
                                    <div ng-messages="profileform.ProfileDesc.$error" multiple ng-if='profileform.ProfileDesc.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="pattern">Invalid pattern.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>
                                    </div>
                                </md-input-container>
                                
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.CustomerBased" aria-label="CustomerBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Customer Based </md-checkbox>
                                </md-input-container>
                                
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.RefCustomerBased" aria-label="RefCustomerBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Reference Customer Based </md-checkbox>
                                </md-input-container>

                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.SalesBased" aria-label="SalesBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Sales Based </md-checkbox>
                                </md-input-container>

                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="newProfile.VendorBased" aria-label="VendorBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Vendor Based </md-checkbox>
                                </md-input-container>

                                <!-- <div layout layout-sm="column">
                                    <md-input-container class="md-block" flex>
                                        <md-checkbox ng-model="newProfile.CustomerBased" aria-label="CustomerBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Customer Based </md-checkbox>
                                    </md-input-container>
                                    
                                    <md-input-container class="md-block" flex>
                                        <md-checkbox ng-model="newProfile.RefCustomerBased" aria-label="RefCustomerBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Reference Customer Based </md-checkbox>
                                    </md-input-container>
                                </div> -->
                                <!-- <div layout layout-sm="column">
                                    <md-input-container class="md-block" flex>
                                        <md-checkbox ng-model="newProfile.SalesBased" aria-label="SalesBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Sales Based </md-checkbox>
                                    </md-input-container>

                                    <md-input-container class="md-block" flex>
                                        <md-checkbox ng-model="newProfile.VendorBased" aria-label="VendorBased" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Vendor Based </md-checkbox>
                                    </md-input-container>
                                </div> -->

                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="ProfileStatus" ng-model="newProfile.ProfileStatus" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="0"> In active </md-option>
                                    </md-select>   
                                    <div ng-messages="profileform.ProfileStatus.$error" multiple ng-if='profileform.ProfileStatus.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                                
                                <div class="divider"></div>
                                <md-button 
                                            class="md-raised btn-w-md md-primary btn-w-md"
                                            data-ng-disabled="profileform.$invalid" ng-click="CreateProfile()">Save</md-button>
                                <!-- <md-button class="btn-w-md"
                                            data-ng-disabled="!canRevert()"
                                            data-ng-click="revert()">Revert</md-button> -->                                    
                            </fieldset>
                        </form>


                    </div>
                </div>
            </div>
        </div>        
    </div>
</div>