<div ng-controller = "WorkflowlabortrackingList" class="page">

    <div ng-show="loading" class="loading" style="text-align:center;"><img src="../images/loading2.gif" /> LOADING...</div>

    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="WorkflowlabortrackingList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">

                                <i ng-click="WorkflowlabortrackingList = !WorkflowlabortrackingList" class="material-icons md-primary" ng-show="WorkflowlabortrackingList">keyboard_arrow_up</i>
                                <i ng-click="WorkflowlabortrackingList = !WorkflowlabortrackingList" class="material-icons md-primary" ng-show="! WorkflowlabortrackingList">keyboard_arrow_down</i>
                                <span ng-click="WorkflowlabortrackingList = !WorkflowlabortrackingList">Labor Tracking Workflow </span>
                                <div flex></div>

                                <a href="#!/Workflowlabortracking" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create Labor Tracking Workflow
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                            <p>No  Labor Tracking Workflow available </p>
                        </div>

                        <div class="row"  ng-show="WorkflowlabortrackingList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">


                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div>

                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('workflowlabortrackingname')" ng-class="{'orderby' : OrderBy == 'workflowlabortrackingname'}">
                                                        <div>
                                                            Labor Tracking Workflow<i class="fa fa-sort pull-right" ng-show="OrderBy != 'workflowlabortrackingname'"></i>
                                                            <span ng-show="OrderBy == 'workflowlabortrackingname'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('workflow_labor_trackingdescription')" ng-class="{'orderby' : OrderBy == 'workflow_labor_trackingdescription'}">
                                                        <div>
                                                            Description<i class="fa fa-sort pull-right" ng-show="OrderBy != 'workflow_labor_trackingdescription'"></i>
                                                            <span ng-show="OrderBy == 'workflow_labor_trackingdescription'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">
                                                        <div style="min-width: 80px;">
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                            <span ng-show="OrderBy == 'Status'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                </tr>

                                                <tr class="errornone">
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="workflowlabortrackingname" ng-model="filter_text[0].workflowlabortrackingname" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="workflow_labor_trackingdescription" ng-model="filter_text[0].workflow_labor_trackingdescription" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>

                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/Workflowlabortracking/{{product.workflow_labor_tracking_id}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a></td>
                                                    <td>
                                                        {{product.workflowlabortrackingname}}
                                                    </td>
                                                    <td>
                                                        {{product.workflow_labor_trackingdescription}}
                                                    </td>

                                                   <td>
                                                        {{product.Status}}
                                                    </td>                  
                                                </tr>
                                            </tbody>

                                            <tfoot>
                                                <tr>
                                                    <td colspan="7">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>
