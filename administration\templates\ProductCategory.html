
<div class="row page" data-ng-controller="ProductCategory">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Category Creations</span>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Class Name</label>
                                    <md-select name="ProductClassID" ng-model="ProductCategory.ProductClassID" aria-label="select">
                                        <md-option value="{{cla.ProductClassID}}" ng-repeat="cla in ProductClasses"> {{cla.ProductClassName}} </md-option>                                        
                                    </md-select>  
                                    <div ng-messages="customer_form.ProductClassID.$error" multiple ng-if='customer_form.ProductClassID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                                      
                                    </div>    
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Category Name</label>                                    
                                    <input type="text" name="CategoryName"  ng-model="ProductCategory['CategoryName']"  required ng-maxlength="50" ng-minlength="3" autocomplete="off" />
                                    <div ng-messages="customer_form.CategoryName.$error" multiple ng-if='customer_form.CategoryName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                           
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Category Description</label>                                    
                                    <textarea name="CategoryDescription" ng-minlength="3"  ng-model="ProductCategory['CategoryDescription']"  required ng-maxlength="500" ></textarea>
                                    
                                    <div ng-messages="customer_form.CategoryDescription.$error" multiple ng-if='customer_form.CategoryDescription.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 500.</div>
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Part No</label>
                                    <input type="text" name="JabilPN"  ng-model="ProductCategory['JabilPN']"  required ng-maxlength="45" />
                                    <div ng-messages="material_signup_form.JabilPN.$error" multiple ng-if='material_signup_form.JabilPN.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Customer Part No</label>
                                    <input type="text" name="CustomerPN"  ng-model="ProductCategory['CustomerPN']"  required ng-maxlength="45" />
                                    <div ng-messages="material_signup_form.CustomerPN.$error" multiple ng-if='material_signup_form.CustomerPN.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Recovery Price</label>
                                    <input type="number" name="RecoveryPrice"  ng-model="ProductCategory['RecoveryPrice']"  />
                                    <div ng-messages="material_signup_form.RecoveryPrice.$error" multiple ng-if='material_signup_form.RecoveryPrice.$dirty' readonly="readonly">                                                      
                                    </div>

                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Standard Cost</label>
                                    <input type="number" name="StandardCost"  ng-model="ProductCategory['StandardCost']"  />
                                    <div ng-messages="material_signup_form.StandardCost.$error" multiple ng-if='material_signup_form.StandardCost.$dirty'>                                                      
                                    </div>

                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Buy Price / Unit</label>
                                    <input type="number" name="UnitPrice"  ng-model="ProductCategory['UnitPrice']"  />
                                    <div ng-messages="material_signup_form.UnitPrice.$error" multiple ng-if='material_signup_form.UnitPrice.$dirty'>                                                      
                                    </div>

                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Buy Price / Weight</label>
                                    <input type="number" name="Commodity_Price"  ng-model="ProductCategory['Commodity_Price']"  />
                                    <div ng-messages="material_signup_form.Commodity_Price.$error" multiple ng-if='material_signup_form.Commodity_Price.$dirty'>                                                      
                                    </div>

                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Sell Price / Unit</label>
                                    <input type="number" name="DownstreamUnitPrice"  ng-model="ProductCategory['DownstreamUnitPrice']"  />
                                    <div ng-messages="material_signup_form.DownstreamUnitPrice.$error" multiple ng-if='material_signup_form.DownstreamUnitPrice.$dirty'>                                                      
                                    </div>

                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Sell Price / Weight</label>
                                    <input type="number" name="DownstreamLBPrice"  ng-model="ProductCategory['DownstreamLBPrice']"  />
                                    <div ng-messages="material_signup_form.DownstreamLBPrice.$error" multiple ng-if='material_signup_form.DownstreamLBPrice.$dirty'>                                                      
                                    </div>

                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Charge Type</label>
                                    <md-select name="PaymentType" ng-model="ProductCategory.PaymentType" required aria-label="select">
                                        <md-option value="" ng-selected=true> Choose </md-option>
                                        <md-option value="Payable"> Payable </md-option>
                                        <md-option value="Chargeable"> Chargeable </md-option>
                                    </md-select>                                           
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>UOM</label>
                                    <md-select name="UOM" ng-model="ProductCategory.UOM" required aria-label="select">
                                        <md-option value="" ng-selected=true> Choose </md-option>
                                        <md-option value="LB"> LB </md-option>
                                        <md-option value="EA"> EA </md-option>
                                    </md-select>                                           
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Data Destruction Device</label>
                                    <md-select name="DataDestruction" ng-model="ProductCategory.DataDestruction" required aria-label="select">
                                        <md-option value="" ng-selected=true> Choose </md-option>
                                        <md-option value="Yes"> Yes </md-option>
                                        <md-option value="No"> No </md-option>
                                    </md-select>                                           
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="ProductCategory.CategoryStatus" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="0"> In active </md-option>
                                    </md-select>   
                                    <div ng-messages="material_signup_form.CategoryStatus.$error" multiple ng-if='material_signup_form.CategoryStatus.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Audit Result</label>
                                    <md-select name="AuditResultID" ng-model="ProductCategory.AuditResultID" aria-label="select">
                                        <md-option value="{{auditResult.AuditResultID}}" ng-repeat="auditResult in AuditResults"> {{auditResult.Result}} </md-option>                                        
                                    </md-select>  
                                    <div ng-messages="customer_form.AuditResultID.$error" multiple ng-if='customer_form.AuditResultID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                                      
                                    </div>    
                                </md-input-container>
                            </div>

                            <div class="col-md-12 btns-row">
                                <a href="#!/ProductCategoryList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="Category()">Save</md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>