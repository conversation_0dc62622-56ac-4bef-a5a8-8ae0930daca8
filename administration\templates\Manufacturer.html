
<div class="row page" data-ng-controller="Manufacturer">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Manufacturer Creation(s)</span>
                        <div flex></div>
                            <a href="#!/ManufacturerList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Manufacturer Name</label>
                                    <input type="text" name="ManufacturerName"  ng-model="Manufacturer['ManufacturerName']"  required ng-maxlength="45" />
                                    <div ng-messages="material_signup_form.ManufacturerName.$error" multiple ng-if='material_signup_form.ManufacturerName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="Description"  ng-model="Manufacturer['Description']"  required ng-maxlength="250" />
                                    <div ng-messages="material_signup_form.Description.$error" multiple ng-if='material_signup_form.Description.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>

                                </md-input-container>
                            </div>
                           
                            <div class="col-md-4">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="Manufacturer.Active" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="0"> In active </md-option>
                                    </md-select>   
                                    <div ng-messages="material_signup_form.Active.$error" multiple ng-if='material_signup_form.Active.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/ManufacturerList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="ManufacturerSave()">
                                <span ng-show="! Manufacturer.busy">Save</span>
                                <span ng-show="Manufacturer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>