<?php
session_start();
include_once("../../connection.php");
include_once("../../common_functions.php");
class AuditClass extends CommonClass {
	public $responseParameters;
	public $connectionlink;
	public function __construct(){
		$this->connectionlink = Connection::DBConnect();
	}

	public function GetInputResults ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
				return json_encode($json);
			}
			//$query = "select * from workflow_input where workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' order by input";
			$query = "select * from workflow_input where workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' and status = 'Active' order by input";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					if($row['default'] == '1') {
						$json['Default'] = $row['input_id'];
					}
					$dispositions[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Input Results Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetMPNFromSerial ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Serial')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Serial Page';
				return json_encode($json);
			}
			//$query = "select mpn_id,apn_id from asn_details where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$query = "select UniversalModelNumber,ID from asn_assets where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and isnull(AssetScanID)";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($row['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$row['UniversalModelNumber'] = $exact_mpn['MPN'];
				}
				//End get exact MPN

				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "Serial Number details not available in ASN";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function ApplyBusinessRule_bkp($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			$input = array();
			//Start get Input Information
			if($data['input_id'] > 0) {
				$query = "select input,input_type,input_id from workflow_input where input_id = '".mysqli_real_escape_string($this->connectionlink,$data['input_id'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					$input['Input'] = $row;
				}
			}
			//End get Input Information

			//Start get If serial is first time or not
			if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module
				$query1 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and AssetScanID != '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			} else {// If Asset is not created, generally in Receive Serial
				$query1 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			}
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) { // Not first time
					$input['Internal']['first_arrival'] = 'No';
				} else { //First Time
					$input['Internal']['first_arrival'] = 'Yes';
				}
			}
			//End get If serial is first time or not

			//Start get If No Fleet Risk available
			/*$query1 = "select count(*) from fleet_risk_serials where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and fleet_risk = 'Yes'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) {
					$input['Internal']['no_fleet_risk'] = 'No';
				} else {
					$input['Internal']['no_fleet_risk'] = 'Yes';
				}
			}*/
			//End get If No Fleet Risk available

			//Start get Serial attributes like Fleet Risk
			$query1 = "select * from fleet_risk_serials where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if(($row1['fleet_risk']  == 'Yes') || ($row1['fleet_risk']  == 'YES')) {
					//$input['Internal']['no_fleet_risk'] = 'No';
					$input['Internal']['fleet_risk'] = 'Yes';
				} else {
					//$input['Internal']['no_fleet_risk'] = 'Yes';
					$input['Internal']['fleet_risk'] = 'No';
				}

				if(($row1['recovery_project']  == 'Yes') || ($row1['recovery_project']  == 'YES')) {
					$input['Internal']['recovery_project'] = 'Yes';
				} else {
					$input['Internal']['recovery_project'] = 'No';
				}

				if(($row1['special_handling']  == 'Yes') || ($row1['special_handling']  == 'YES')) {
					$input['Internal']['special_handling'] = 'Yes';
				} else {
					$input['Internal']['special_handling'] = 'No';
				}

				if(($row1['internal_use']  == 'Yes') || ($row1['internal_use']  == 'YES')) {
					$input['Internal']['internal_use'] = 'Yes';
				} else {
					$input['Internal']['internal_use'] = 'No';
				}
			} else {
				//$input['Internal']['no_fleet_risk'] = 'Yes';
				$input['Internal']['fleet_risk'] = 'No';
				$input['Internal']['recovery_project'] = 'No';
				$input['Internal']['special_handling'] = 'No';
				$input['Internal']['internal_use'] = 'No';
			}
			//End get Serial attributes like Fleet Risk


			//Start get If Warranty available
			/*$query1 = "select count(*) from asset_warranty where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and in_warranty = 'Yes'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) {
					$input['Internal']['in_warranty'] = 'Yes';
				} else {
					$input['Internal']['in_warranty'] = 'No';
				}
			}*/
			//End get If Warranty available


			//Start get Warranty Details
			$query1 = "select *,DATEDIFF(warranty_expiration_date, NOW()) as warranty_days from asset_warranty where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' ";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['warranty_days'] >= 0) {
					$input['Internal']['in_warranty'] = 'Yes';
				} else {
					$input['Internal']['in_warranty'] = 'No';
				}
			} else {
				$input['Internal']['in_warranty'] = 'No';
			}
			//End get Warranty Details

			//Start get PalletID from Serial
			if($data['idPallet'] == '') {
				if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module
					$query121 = "select idPallet from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				} else {// If Asset is not created, generally in Receive Serial
					$query121 = "select idPallet from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
				}
				$q121 = mysqli_query($this->connectionlink,$query121);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row121 = mysqli_fetch_assoc($q121);
					$data['idPallet'] = $row121['idPallet'];
				} else {
					$data['idPallet'] = '';
				}
			}
			//End get PalletID from Serial



			//Start get Source ID from pallet
			//$query1 = "select c.CustomerShotCode from pallets p,loads l,customer c where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and p.LoadId = l.LoadId and l.idCustomer = c.CustomerID ";
			//$query1 = "select c.CustomerShotCode,p.WasteClassificationType from pallets p,customer c where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and p.idCustomer = c.CustomerID ";
			if($data['UnknownidCustomer'] > 0) {
				$query1 = "select c.CustomerShotCode from customer c where c.CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['UnknownidCustomer'])."' ";
			} else {
				$query1 = "select c.CustomerShotCode,p.WasteClassificationType,m.MaterialType,ct.Cumstomertype,a.Customer from pallets p
				left join customer c on p.idCustomer = c.CustomerID
				left join material_types m on p.MaterialTypeID = m.MaterialTypeID
				left join aws_customers a on p.AWSCustomerID = a.AWSCustomerID
				left join customertype ct on p.idCustomertype = ct.idCustomertype
				where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
			}
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($data['UnknownidCustomer'] > 0) {
					$input['Internal']['CustomerShotCode'] = $row1['CustomerShotCode'];
					$input['Internal']['WasteClassificationType'] = 'n/a';
					$input['Internal']['MaterialType'] = $data['MaterialType'];
					$input['Internal']['Cumstomertype'] = '';
					$input['Internal']['Customer'] = '';
				} else {
					$input['Internal']['CustomerShotCode'] = $row1['CustomerShotCode'];
					$input['Internal']['WasteClassificationType'] = $row1['WasteClassificationType'];
					$input['Internal']['MaterialType'] = $row1['MaterialType'];
					$input['Internal']['Cumstomertype'] = $row1['Cumstomertype'];
					$input['Internal']['Customer'] = $row1['Customer'];
				}
			} else {
				$input['Internal']['CustomerShotCode'] = '';
				$input['Internal']['WasteClassificationType'] = '';
				$input['Internal']['MaterialType'] = '';
				$input['Internal']['Cumstomertype'] = '';
				$input['Internal']['Customer'] = '';
			}
			//End get Source ID from Pallet

			//Start get Source Type from pallet
			/*$query1 = "select ct.Cumstomertype from pallets p
			left join loads l on p.LoadId = l.LoadId
			left join customer c on l.idCustomer =  c.CustomerID
			left join customertype ct on c.CustomerType = ct.idCustomertype
			where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'  ";*/

			// $query1 = "select ct.Cumstomertype from pallets p
			// left join customer c on p.idCustomer =  c.CustomerID
			// left join customertype ct on c.CustomerType = ct.idCustomertype
			// where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'  ";

			// $q1 = mysqli_query($this->connectionlink,$query1);
			// if(mysqli_error($this->connectionlink)) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = mysqli_error($this->connectionlink);
			// 	return json_encode($json);
			// }
			// if(mysqli_affected_rows($this->connectionlink) > 0) {
			// 	$row1 = mysqli_fetch_assoc($q1);
			// 	$input['Internal']['Cumstomertype'] = $row1['Cumstomertype'];
			// } else {
			// 	$input['Internal']['Cumstomertype'] = '';
			// }
			//End get Source Type from Pallet

			//Start get MPN Catalog Details
			//$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['mpn_id'])."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$data['apn_id'])."'";

			if($data['From'] == 'ReceiveSerial') {//Get Exact MPN
				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($data['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$data['UniversalModelNumber'] = $exact_mpn['MPN'];
					$json['ExactMPN'] = $exact_mpn['MPN'];
				}
				//End get exact MPN
			} else {
				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($data['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$data['UniversalModelNumber'] = $exact_mpn['MPN'];
					$json['ExactMPN'] = $exact_mpn['MPN'];
				}
				//End get exact MPN
			}

			//Start get APN
			if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module

				$query118 = "select apn_id from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
				$q118 = mysqli_query($this->connectionlink,$query118);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row118 = mysqli_fetch_assoc($q118);
					$APN = $row118['apn_id'];
				} else {
					$APN = '';
				}

			} else { // In Receive Serial Module
				if($data['ID'] > 0) {
					$query118 = "select apn_id from asn_assets where ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."' ";
					$q118 = mysqli_query($this->connectionlink,$query118);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row118 = mysqli_fetch_assoc($q118);
						$APN = $row118['apn_id'];
					} else {
						$APN = '';
					}
				} else {
					$query118 = "select apn_id from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
					$q118 = mysqli_query($this->connectionlink,$query118);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row118 = mysqli_fetch_assoc($q118);
						$APN = $row118['apn_id'];
					} else {
						$APN = '';
					}
				}
			}
			//End get APN

			//Start get daily extracted value
			$query1 = "select * from daily_received_summary where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."' and ReceivedDate = CURDATE()";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$input['Internal']['daily_extracted_quantity'] = $row1['ReceivedCount'];
			} else {
				$input['Internal']['daily_extracted_quantity'] = 0;
			}
			//End get daily extracted value
			if($APN == '' || $APN == NULL || $APN == 'n/a') {
				$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
			} else {
				//$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
				$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
			}
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If MPN Details available
				$row2 = mysqli_fetch_assoc($q2);
				$input['CatalogMPN'] = $row2;

				//Start get Sanitization type value file
				// if($row2['nd_sanitization_type'] != '') {
				// 	$query121 = "select file_url from business_rule_attribute_values where attribute_id = '7' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['nd_sanitization_type'])."'";
				// 	$q121 = mysqli_query($this->connectionlink,$query121);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row121 = mysqli_fetch_assoc($q121);
				// 		$row2['nd_sanitization_type_file_url'] = $row121['file_url'];
				// 	} else {
				// 		$row2['nd_sanitization_type_file_url'] = '';
				// 	}
				// } else {
				// 	$row2['nd_sanitization_type_file_url'] = '';
				// }
				//End get Sanitization type value file

				//Start get Sanitization type
				if($data['AssetScanID'] > 0) {
					$query1231 = "select a.*,b.rule_name as nd_sanitization_type from asset a
					left join business_rule b on a.RecentDispositionRuleID = b.rule_id
					where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
					$q1231 = mysqli_query($this->connectionlink,$query1231);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1231 = mysqli_fetch_assoc($q1231);
						$row2['nd_sanitization_type'] = $row1231['nd_sanitization_type'];
					} else {
						$row2['nd_sanitization_type'] = '';
						$row2['CSPLINK'] = '';
					}
				} else {
					$row2['nd_sanitization_type'] = '';
					$row2['CSPLINK'] = '';
				}
				//End get Sanitization type

				//Start get Sanitization type value from Procedure id column
				if($row2['procedure_id'] != '' && $row2['nd_sanitization_type'] != '') {
					$row2['CSPLINK'] = '';
					$procedure_id = $row2['procedure_id'];
					$procedure_array = explode('$', $procedure_id);
					$san_contains = false;
					for($k=0;$k<count($procedure_array);$k++) {
						if(strpos($procedure_array[$k], $row2['nd_sanitization_type']) !== false) {
							$links_array = explode('*', $procedure_array[$k]);
							if(count($links_array) > 0) {
								$san_contains = true;
								$row2['CSPLINK'] = $links_array[1];
							}
						}
					}
				} else {
					//$row2['nd_sanitization_type_file_url'] = '';
					//$row2['nd_sanitization_type'] = '';
					$row2['CSPLINK'] = '';
				}
				//End get Sanitization type value from Procedure id column

				//Start get Sanitization type value file
				// if($row2['nd_sanitization_type'] != '') {
				// 	$query121 = "select file_url from business_rule_attribute_values where attribute_id = '7' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['nd_sanitization_type'])."'";
				// 	$q121 = mysqli_query($this->connectionlink,$query121);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row121 = mysqli_fetch_assoc($q121);
				// 		$row2['nd_sanitization_type_file_url'] = $row121['file_url'];
				// 	} else {
				// 		$row2['nd_sanitization_type_file_url'] = '';
				// 	}
				// } else {
				// 	$row2['nd_sanitization_type_file_url'] = '';
				// }
				//End get Sanitization type value file

				//Start get Harvest type value file
				// if($row2['harvest_type'] != '') {
				// 	$query121 = "select file_url from business_rule_attribute_values where attribute_id = '9' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['harvest_type'])."'";
				// 	$q121 = mysqli_query($this->connectionlink,$query121);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row121 = mysqli_fetch_assoc($q121);
				// 		$row2['harvest_type_file_url'] = $row121['file_url'];
				// 	} else {
				// 		$row2['harvest_type_file_url'] = '';
				// 	}
				// } else {
				// 	$row2['harvest_type_file_url'] = '';
				// }
				//End get Harvest type value file

				//Start get Repair type value file
				if($row2['RepairType'] != '') {
					$query121 = "select file_url from business_rule_attribute_values where attribute_id = '22' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['RepairType'])."'";
					$q121 = mysqli_query($this->connectionlink,$query121);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row121 = mysqli_fetch_assoc($q121);
						$row2['RepairType_file_url'] = $row121['file_url'];
					} else {
						$row2['RepairType_file_url'] = '';
					}
				} else {
					$row2['RepairType_file_url'] = '';
				}
				//End get Repair type value file

				$json['MPN'] = $row2;
			} else { // MPN Details are missing
				$json['Success'] = false;
				$json['Result'] = 'MPN Details not available';
				return json_encode($json);
			}
			//End get MPN Catalog Details

			// $json['Success'] = false;
			// $json['Result'] = $input;
			// $json['Query'] = $query1;
			// return json_encode($json);

			//Start Get Rules
			//$query3 = "select r.*,d.disposition from business_rule r,disposition d where r.disposition_id = d.disposition_id and r.FacilityID = '".$_SESSION['user']['FacilityID']."' order by r.priority";
			$query3 = "select r.*,d.disposition,sd.disposition as sub_disposition from business_rule r
			left join disposition d on r.disposition_id = d.disposition_id
			left join disposition sd on r.sub_disposition_id = sd.disposition_id
			where r.FacilityID = '".$_SESSION['user']['FacilityID']."' and r.status = 'Active' order by r.priority";

			// $query3 = "select r.*,d.disposition,sd.disposition as sub_disposition,d.color_code,d.color,sd.color_code as sub_color_code,sd.color as sub_color from business_rule r
			// left join disposition d on r.disposition_id = d.disposition_id
			// left join disposition sd on r.sub_disposition_id = sd.disposition_id
			// left join business_rule_facilities rf  on r.rule_id = rf.rule_id
			// left join business_rule_versions rv on r.version_id = rv.version_id
			// where rf.FacilityID = '".$_SESSION['user']['FacilityID']."' and r.status = 'Active' and rv.current_version = '1' order by r.priority";

			$query3 = "select r.*,d.disposition,sd.disposition as sub_disposition,d.color_code,d.color,sd.color_code as sub_color_code,sd.color as sub_color from business_rule r
			left join disposition d on r.disposition_id = d.disposition_id
			left join disposition sd on r.sub_disposition_id = sd.disposition_id
			left join business_rule_versions rv on r.version_id = rv.version_id
			where r.status = 'Active' and rv.current_version = '1' order by r.priority";

			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If Rules Defined
				while($rule = mysqli_fetch_assoc($q3)) { // Loop through Rules
					//Start check If input_id matches
					$input_passed = true;
					$conditions_passed = true;

					// $json['Success'] = false;
					// $json['Result'] = $rule['input_id']."##".$input['Input']['input_id'];
					// return json_encode($json);

					if(($rule['input_id'] > 0) && ($rule['input_id'] != $input['Input']['input_id'])) {
						$input_passed = false;
						continue; //Look for new Rule
					}
					//End check If input_id matched

					//Start get Rule Conditions
					$query4 = "select c.*,a.attribute_id,a.attribute_name,a.attribute_type,a.field_name from business_rule_condition c,business_rule_attributes a where c.attribute_id = a.attribute_id and c.rule_id = '".mysqli_real_escape_string($this->connectionlink,$rule['rule_id'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) { //If Conditions Defined
						while($condition = mysqli_fetch_assoc($q4)) { // Loop through Rule Conditions
							if($condition['multiple_values'] == 0) {
								if($condition['value'] == 'BRE_sort_quantity') {
									$condition['value'] = $input['CatalogMPN']['BRE_sort_quantity'];
								}
								if($condition['operator'] == '==') {
									if($condition['attribute_id'] == '16') {//Part Type Attribute, ignore case sensitivity
										if(strcasecmp($condition['value'], $input[$condition['attribute_type']][$condition['field_name']]) != 0) {
											$conditions_passed = false;
										}
									} else if(! ($condition['value'] == $input[$condition['attribute_type']][$condition['field_name']])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '!=') {
									if(! ($condition['value'] != $input[$condition['attribute_type']][$condition['field_name']])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '>') {
									if(! ($input[$condition['attribute_type']][$condition['field_name']] > $condition['value'])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '<') {
									if(! ($input[$condition['attribute_type']][$condition['field_name']] < $condition['value'])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == 'contains') {
									if(! strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $condition['value'])) {
										$conditions_passed = false;
									}
								} else { // not_contains
									if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $condition['value'])) {
										$conditions_passed = false;
									}
								}
							} else {
								$multiple_values = explode('@#$', $condition['value']);
								if($condition['operator'] == '==') {
									if(! in_array($input[$condition['attribute_type']][$condition['field_name']], $multiple_values)){
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '!=') {
									if(in_array($input[$condition['attribute_type']][$condition['field_name']], $multiple_values)){
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '>') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(($input[$condition['attribute_type']][$condition['field_name']] > $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '<') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(($input[$condition['attribute_type']][$condition['field_name']] < $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == 'contains') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else { // not_contains
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == true) {
										$conditions_passed = false;
									}
								}
							}
						}
					}
					//End get Rule Conditions
					if($input_passed &&	$conditions_passed && $data['workflow_id'] == '20') { // Parts Sort




						// get workstation group id of the work station
						$getWorkstationGroupQ = "select GroupID from WorkstationConfiguration_mapping where SiteID='".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
						$getWorkstationGroupQEx = mysqli_query($this->connectionlink,$getWorkstationGroupQ);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						$GroupID = 0;
						if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
							$WorkstationGroup = mysqli_fetch_assoc($getWorkstationGroupQEx);
							$GroupID = $WorkstationGroup['GroupID'];
						} else {
							$json['Success'] = false;
							$json['Result'] = "No workstation Group available";
							return json_encode($json);
						}
						// get COO
						if(isset($data['COOID']) && $data['COOID'] != 'All'){
							$cooQuery = "select * from COO where COOID='".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."'";
							$cooQueryEx = mysqli_query($this->connectionlink,$cooQuery);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$CooRow = mysqli_fetch_assoc($cooQueryEx);
								$data['COO'] = $CooRow['COO'];
							} else {
								$json['Success'] = false;
								$json['Result'] = "No COO Data available";
								return json_encode($json);
							}
						}

						// get Bin for sort configuration based on sort validation key
						//Start get BIN Details for Parts Sort Page
						if($data['SortCriteriaID'] == '1') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 1";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$json['NoMapping'] = '1';
								$json['Success'] = true;
								$json['Result'] = "No Mapping Bin Available";
								return json_encode($json);
							}
						} else if($data['SortCriteriaID'] == '2') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and LOWER(m.mpn_id) = LOWER('".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."') and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 2";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 2";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '3') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 3";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 3";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '4') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 4";
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 4";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '5') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 5";
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 5";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 5";
									$q5 = mysqli_query($this->connectionlink,$query5);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
										$row5 = mysqli_fetch_assoc($q5);
										if($row5['AuditLocked'] == '1'){
											$json['Success'] = false;
											$json['Result'] = "BIN is locked for Audit";
											return json_encode($json);
										}
										$json['CustomPalletID'] = $row5['CustomPalletID'];
										$json['BinName'] = $row5['BinName'];
										$json['LocationID'] = $row5['LocationID'];
										$json['LocationName'] = $row5['LocationName'];
										$json['AssetsCount'] = $row5['AssetsCount'];
										$json['MaximumAssets'] = $row5['MaximumAssets'];
										$json['sortconfigurationid'] = $row5['sortconfigurationid'];
									}else{
										$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 5";
										$q5 = mysqli_query($this->connectionlink,$query5);
										if(mysqli_error($this->connectionlink)) {
											$json['Success'] = false;
											$json['Result'] = mysqli_error($this->connectionlink);
											return json_encode($json);
										}
										if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
											$row5 = mysqli_fetch_assoc($q5);
											if($row5['AuditLocked'] == '1'){
												$json['Success'] = false;
												$json['Result'] = "BIN is locked for Audit";
												return json_encode($json);
											}
											$json['CustomPalletID'] = $row5['CustomPalletID'];
											$json['BinName'] = $row5['BinName'];
											$json['LocationID'] = $row5['LocationID'];
											$json['LocationName'] = $row5['LocationName'];
											$json['AssetsCount'] = $row5['AssetsCount'];
											$json['MaximumAssets'] = $row5['MaximumAssets'];
											$json['sortconfigurationid'] = $row5['sortconfigurationid'];
										}else{
											$json['NoMapping'] = '1';
											$json['Success'] = true;
											$json['Result'] = "No Mapping Bin Available";
											return json_encode($json);
										}
									}
								}
							}
						} else if($data['SortCriteriaID'] == '6'){
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 6";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 6";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 6";
									$q5 = mysqli_query($this->connectionlink,$query5);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
										$row5 = mysqli_fetch_assoc($q5);
										if($row5['AuditLocked'] == '1'){
											$json['Success'] = false;
											$json['Result'] = "BIN is locked for Audit";
											return json_encode($json);
										}
										$json['CustomPalletID'] = $row5['CustomPalletID'];
										$json['BinName'] = $row5['BinName'];
										$json['LocationID'] = $row5['LocationID'];
										$json['LocationName'] = $row5['LocationName'];
										$json['AssetsCount'] = $row5['AssetsCount'];
										$json['MaximumAssets'] = $row5['MaximumAssets'];
										$json['sortconfigurationid'] = $row5['sortconfigurationid'];
									}else{
										$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 6";
										$q5 = mysqli_query($this->connectionlink,$query5);
										if(mysqli_error($this->connectionlink)) {
											$json['Success'] = false;
											$json['Result'] = mysqli_error($this->connectionlink);
											return json_encode($json);
										}
										if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
											$row5 = mysqli_fetch_assoc($q5);
											if($row5['AuditLocked'] == '1'){
												$json['Success'] = false;
												$json['Result'] = "BIN is locked for Audit";
												return json_encode($json);
											}
											$json['CustomPalletID'] = $row5['CustomPalletID'];
											$json['BinName'] = $row5['BinName'];
											$json['LocationID'] = $row5['LocationID'];
											$json['LocationName'] = $row5['LocationName'];
											$json['AssetsCount'] = $row5['AssetsCount'];
											$json['MaximumAssets'] = $row5['MaximumAssets'];
											$json['sortconfigurationid'] = $row5['sortconfigurationid'];
										}else{
											$json['NoMapping'] = '1';
											$json['Success'] = true;
											$json['Result'] = "No Mapping Bin Available";
											return json_encode($json);
										}
									}
								}
							}
						}
						//End get BIN Details for Parts Sort Page

						//End get Bin Details
						if($rule['sub_disposition'] == NULL) {
							$rule['sub_disposition'] = 'n/a';
						}
						$json['Success'] = true;
						$json['Result'] = $rule;
						return json_encode($json);

					} else if($input_passed &&	$conditions_passed ) {//Rule Satisfied
						//Start get Bin Details
						$query5 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m
						left join custompallet cp on m.CustomPalletID = cp.CustomPalletID
						where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."'";
						$q5 = mysqli_query($this->connectionlink,$query5);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
							$row5 = mysqli_fetch_assoc($q5);
							$json['CustomPalletID'] = $row5['CustomPalletID'];
							$json['BinName'] = $row5['BinName'];
						}
						//End get Bin Details
						if($rule['sub_disposition'] == NULL) {
							$rule['sub_disposition'] = 'n/a';
						}
						if($rule['sub_disposition_id'] > 0) {
							$query56 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m
							left join custompallet cp on m.CustomPalletID = cp.CustomPalletID
							where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['sub_disposition_id'])."'";
							$q56 = mysqli_query($this->connectionlink,$query56);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row56 = mysqli_fetch_assoc($q56);
								$json['Sub_CustomPalletID'] = $row56['CustomPalletID'];
								$json['Sub_BinName'] = $row56['BinName'];
							}
						}

						$json['Success'] = true;
						$json['Result'] = $rule;
						return json_encode($json);
					}
				}

				$json['Success'] = false;
				$json['Result'] = 'No Rules Satisfied';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Business Rules Defined';
				return json_encode($json);
			}
			//End get Rules

			$json['Success'] = false;
			$json['Result'] = $input;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ApplyBusinessRule($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			$input = array();

			//Start get COO ID
			if($data['COOID'] > 0) {
				$input['Internal']['COOID'] = $data['COOID'];
			} else {
				$input['Internal']['COOID'] = '';
			}
			//End get COO ID

			//Start get Region Information
			$query119 = "select r.RegionName from facility f left join region r on f.Region = r.Region where f.FacilityID = '".$_SESSION['user']['FacilityID']."'";
			$q119 = mysqli_query($this->connectionlink,$query119);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row119 = mysqli_fetch_assoc($q119);
				$input['Internal']['RegionName'] = $row119['RegionName'];
			} else {
				$input['Internal']['RegionName'] = '';
			}

			//End get Region Information

			//Start get If serial is first time or not
			if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module
				$query1 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and AssetScanID != '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			} else {// If Asset is not created, generally in Receive Serial
				$query1 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			}
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$count = (int)$row1['count(*)'];

				if($count == 0) { // First Time
					$input['Internal']['first_arrival'] = 'Yes';
					$input['Internal']['second_arrival'] = 'No';
					$input['Internal']['third_arrival'] = 'No';
				} else if($count == 1) { // Second Time
					$input['Internal']['first_arrival'] = 'No';
					$input['Internal']['second_arrival'] = 'Yes';
					$input['Internal']['third_arrival'] = 'No';
				} else { // Third Time or More (count >= 2)
					$input['Internal']['first_arrival'] = 'No';
					$input['Internal']['second_arrival'] = 'No';
					$input['Internal']['third_arrival'] = 'Yes';
				}
			}
			//End get If serial is first time or not

			//Start get Serial attributes like Fleet Risk
			$query1 = "select * from fleet_risk_serials where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if(($row1['fleet_risk']  == 'Yes') || ($row1['fleet_risk']  == 'YES')) {
					//$input['Internal']['no_fleet_risk'] = 'No';
					$input['Internal']['fleet_risk'] = 'Yes';
				} else {
					//$input['Internal']['no_fleet_risk'] = 'Yes';
					$input['Internal']['fleet_risk'] = 'No';
				}

				if(($row1['recovery_project']  == 'Yes') || ($row1['recovery_project']  == 'YES')) {
					$input['Internal']['recovery_project'] = 'Yes';
				} else {
					$input['Internal']['recovery_project'] = 'No';
				}

				if(($row1['special_handling']  == 'Yes') || ($row1['special_handling']  == 'YES')) {
					$input['Internal']['special_handling'] = 'Yes';
				} else {
					$input['Internal']['special_handling'] = 'No';
				}

				if(($row1['internal_use']  == 'Yes') || ($row1['internal_use']  == 'YES')) {
					$input['Internal']['internal_use'] = 'Yes';
				} else {
					$input['Internal']['internal_use'] = 'No';
				}
			} else {
				//$input['Internal']['no_fleet_risk'] = 'Yes';
				$input['Internal']['fleet_risk'] = 'No';
				$input['Internal']['recovery_project'] = 'No';
				$input['Internal']['special_handling'] = 'No';
				$input['Internal']['internal_use'] = 'No';
			}
			//End get Serial attributes like Fleet Risk

			//Start get Warranty Details
			$query1 = "select *,DATEDIFF(warranty_expiration_date, NOW()) as warranty_days from asset_warranty where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' ";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['warranty_days'] >= 0) {
					$input['Internal']['in_warranty'] = 'Yes';
				} else {
					$input['Internal']['in_warranty'] = 'No';
				}
			} else {
				$input['Internal']['in_warranty'] = 'No';
			}
			//End get Warranty Details

			//Start get PalletID from Serial
			if($data['idPallet'] == '') {
				if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module
					$query121 = "select idPallet from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				} else {// If Asset is not created, generally in Receive Serial
					$query121 = "select idPallet from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
				}
				$q121 = mysqli_query($this->connectionlink,$query121);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row121 = mysqli_fetch_assoc($q121);
					$data['idPallet'] = $row121['idPallet'];
				} else {
					$data['idPallet'] = '';
				}
			}
			//End get PalletID from Serial



			//Start get Source ID from pallet
			if($data['UnknownidCustomer'] > 0) {
				$query1 = "select c.CustomerShotCode from customer c where c.CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['UnknownidCustomer'])."' ";
			} else {
				$query1 = "select c.CustomerShotCode,p.WasteClassificationType,m.MaterialType,ct.Cumstomertype,a.Customer from pallets p
				left join customer c on p.idCustomer = c.CustomerID
				left join material_types m on p.MaterialTypeID = m.MaterialTypeID
				left join aws_customers a on p.AWSCustomerID = a.AWSCustomerID
				left join customertype ct on p.idCustomertype = ct.idCustomertype
				where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
			}
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($data['UnknownidCustomer'] > 0) {
					$input['Internal']['CustomerShotCode'] = $row1['CustomerShotCode'];
					$input['Internal']['WasteClassificationType'] = 'n/a';
					$input['Internal']['MaterialType'] = $data['MaterialType'];
					$input['Internal']['Cumstomertype'] = '';
					$input['Internal']['Customer'] = '';
				} else {
					$input['Internal']['CustomerShotCode'] = $row1['CustomerShotCode'];
					$input['Internal']['WasteClassificationType'] = $row1['WasteClassificationType'];
					$input['Internal']['MaterialType'] = $row1['MaterialType'];
					$input['Internal']['Cumstomertype'] = $row1['Cumstomertype'];
					$input['Internal']['Customer'] = $row1['Customer'];
				}
			} else {
				$input['Internal']['CustomerShotCode'] = '';
				$input['Internal']['WasteClassificationType'] = '';
				$input['Internal']['MaterialType'] = '';
				$input['Internal']['Cumstomertype'] = '';
				$input['Internal']['Customer'] = '';
			}
			//End get Source ID from Pallet

			if($data['From'] == 'ReceiveSerial') {//Get Exact MPN
				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($data['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$data['UniversalModelNumber'] = $exact_mpn['MPN'];
					$json['ExactMPN'] = $exact_mpn['MPN'];
				}
				//End get exact MPN
			} else {
				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($data['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$data['UniversalModelNumber'] = $exact_mpn['MPN'];
					$json['ExactMPN'] = $exact_mpn['MPN'];
				}
				//End get exact MPN
			}

			//Start get daily extracted value
			$query1 = "select * from daily_received_summary where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."' and ReceivedDate = CURDATE()";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$input['Internal']['daily_extracted_quantity'] = $row1['ReceivedCount'];
			} else {
				$input['Internal']['daily_extracted_quantity'] = 0;
			}
			//End get daily extracted value
			$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If MPN Details available
				$row2 = mysqli_fetch_assoc($q2);
				$input['CatalogMPN'] = $row2;

				//Start get Sanitization type
				if($data['AssetScanID'] > 0) {
					$query1231 = "select a.*,b.rule_name as nd_sanitization_type from asset a
					left join business_rule b on a.RecentDispositionRuleID = b.rule_id
					where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
					$q1231 = mysqli_query($this->connectionlink,$query1231);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1231 = mysqli_fetch_assoc($q1231);
						$row2['nd_sanitization_type'] = $row1231['nd_sanitization_type'];
					} else {
						$row2['nd_sanitization_type'] = '';
						$row2['CSPLINK'] = '';
					}
				} else {
					$row2['nd_sanitization_type'] = '';
					$row2['CSPLINK'] = '';
				}
				//End get Sanitization type

				//Start get Sanitization type value from Procedure id column
				if($row2['procedure_id'] != '' && $row2['nd_sanitization_type'] != '') {
					$row2['CSPLINK'] = '';
					$procedure_id = $row2['procedure_id'];
					$procedure_array = explode('$', $procedure_id);
					$san_contains = false;
					for($k=0;$k<count($procedure_array);$k++) {
						if(strpos($procedure_array[$k], $row2['nd_sanitization_type']) !== false) {
							$links_array = explode('*', $procedure_array[$k]);
							if(count($links_array) > 0) {
								$san_contains = true;
								$row2['CSPLINK'] = $links_array[1];
							}
						}
					}
				} else {
					//$row2['nd_sanitization_type_file_url'] = '';
					//$row2['nd_sanitization_type'] = '';
					$row2['CSPLINK'] = '';
				}
				//End get Sanitization type value from Procedure id column

				//Start get Repair type value file
				if($row2['RepairType'] != '') {
					$query121 = "select file_url from business_rule_attribute_values where attribute_id = '22' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['RepairType'])."'";
					$q121 = mysqli_query($this->connectionlink,$query121);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row121 = mysqli_fetch_assoc($q121);
						$row2['RepairType_file_url'] = $row121['file_url'];
					} else {
						$row2['RepairType_file_url'] = '';
					}
				} else {
					$row2['RepairType_file_url'] = '';
				}
				//End get Repair type value file

				$json['MPN'] = $row2;
			} else { // MPN Details are missing
				$json['Success'] = false;
				$json['Result'] = 'MPN Details not available';
				return json_encode($json);
			}
			//End get MPN Catalog Details
			if($data['UnknownidCustomer'] > 0) {//Unknown Recovery
				//Forunknown Recovery the 3 fields are sent as input
			} else if($data['idPallet'] != '') {//Start get AWSCustomerID, Source Type, Material Type
				$query118 = "select MaterialType,AWSCustomerID,idCustomertype from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
				$q118 = mysqli_query($this->connectionlink,$query118);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row118 = mysqli_fetch_assoc($q118);
					$data['AWSCustomerID'] = $row118['AWSCustomerID'];
					$data['MaterialType'] = $row118['MaterialType'];
					$data['idCustomertype'] = $row118['idCustomertype'];
				} else {
					$data['AWSCustomerID'] = '';
					$data['MaterialType'] = '';
					$data['idCustomertype'] = '';
				}
			} else {
				$data['AWSCustomerID'] = '';
				$data['MaterialType'] = '';
				$data['idCustomertype'] = '';
			}

			// $json['Success'] = false;
			// $json['Result'] = $data;
			// $json['Query'] = $query1;
			// return json_encode($json);

			// $json['Success'] = false;
			// $json['Result'] = $input;
			// $json['Query'] = $query1;
			// return json_encode($json);

			//Start Get Rules
			$where_conditions = array();
			$where_conditions[] = "r.status = 'Active'";
			$where_conditions[] = "rv.current_version = '1'";
			//$where_conditions[] = "rv.version_id = '34'";

			// Add input_id filtering
			if(isset($data['input_id']) && $data['input_id'] > 0) {
				$where_conditions[] = "(r.input_id = '' OR r.input_id = '0' OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $data['input_id'])."', r.input_id) > 0)";
			}

			// Add Customer ID filtering - Updated to handle comma-separated Customer IDs in database
			if(isset($data['AWSCustomerID']) && $data['AWSCustomerID'] > 0) {
				// Input AWSCustomerID is a single integer, but database rules may have comma-separated values
				$customer_condition = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All'";
				$customer_condition .= " OR r.AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink, $data['AWSCustomerID'])."'";
				$customer_condition .= " OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $data['AWSCustomerID'])."', r.AWSCustomerID) > 0";
				$customer_condition .= ")";
				$where_conditions[] = $customer_condition;
			} else {
				// If AWSCustomerID is 0, empty, or not set - only check for All rules
				$where_conditions[] = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";
			}

			// Add Facility filtering
			if(isset($_SESSION['user']['FacilityID'])) {
				$where_conditions[] = "(r.FacilityID = 'all' OR r.FacilityID = 'All' OR r.FacilityID = '".mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID'])."')";
			}

			// Add Workflow filtering
			if(isset($data['workflow_id'])) {
				$where_conditions[] = "(r.workflow_id = 'all' OR r.workflow_id = 'All' OR r.workflow_id = '".mysqli_real_escape_string($this->connectionlink, $data['workflow_id'])."')";
			}

			// Add Part Type filtering using parttypeid
			$part_type_name = '';
			if(isset($data['parttypeid']) && $data['parttypeid'] > 0) {
				$parttype_query = "SELECT parttype FROM parttype WHERE parttypeid = '".mysqli_real_escape_string($this->connectionlink, $data['parttypeid'])."'";
				$parttype_result = mysqli_query($this->connectionlink, $parttype_query);
				if($parttype_result && mysqli_num_rows($parttype_result) > 0) {
					$parttype_row = mysqli_fetch_assoc($parttype_result);
					$part_type_name = $parttype_row['parttype'];
					$where_conditions[] = "(r.part_types = 'all' OR r.part_types = 'All' OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $part_type_name)."', r.part_types) > 0)";
				}
			}

			// Add Source Type filtering
			if($data['idCustomertype'] > 0) {
				$where_conditions[] = "(r.idCustomertype = 'all' OR r.idCustomertype = 'All' OR r.idCustomertype = '".mysqli_real_escape_string($this->connectionlink, $data['idCustomertype'])."')";
			} else {
				$where_conditions[] = "(r.idCustomertype = 'all' OR r.idCustomertype = 'All')";
			}

			// Add Material Type filtering
			if(isset($data['MaterialType'])) {
				$where_conditions[] = "(r.MaterialType = 'all' OR r.MaterialType = 'All' OR r.MaterialType = '".mysqli_real_escape_string($this->connectionlink, $data['MaterialType'])."')";
			}

			// Get filtered business rules ordered by priority
			$rules_query = "SELECT r.*, d.disposition, d.color_code, d.color
							FROM business_rule r
							LEFT JOIN disposition d ON r.disposition_id = d.disposition_id
							LEFT JOIN business_rule_versions rv ON r.version_id = rv.version_id
							WHERE " . implode(' AND ', $where_conditions) . "
							ORDER BY r.priority ASC";

			$rules_result = mysqli_query($this->connectionlink, $rules_query);

			// $json['Result'] = $rules_query;
			// $json['Success'] = false;
			// $json['Result'] = $rules_query;
			// return json_encode($json);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If Rules Defined
				while($rule = mysqli_fetch_assoc($rules_result)) { // Loop through Rules
					//Start check If input_id matches
					$input_passed = true;
					$conditions_passed = true;

					//Start get Rule Conditions
					$query4 = "select c.*,a.attribute_id,a.attribute_name,a.attribute_type,a.field_name from business_rule_condition c,business_rule_attributes a where c.attribute_id = a.attribute_id and c.rule_id = '".mysqli_real_escape_string($this->connectionlink,$rule['rule_id'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) { //If Conditions Defined
						while($condition = mysqli_fetch_assoc($q4)) { // Loop through Rule Conditions
							if($condition['multiple_values'] == 0) {
								if($condition['value'] == 'BRE_sort_quantity') {
									$condition['value'] = $input['CatalogMPN']['BRE_sort_quantity'];
								}
								if($condition['operator'] == '==') {
									if($condition['attribute_id'] == '16') {//Part Type Attribute, ignore case sensitivity
										if(strcasecmp($condition['value'], $input[$condition['attribute_type']][$condition['field_name']]) != 0) {
											$conditions_passed = false;
										}
									} else if(! ($condition['value'] == $input[$condition['attribute_type']][$condition['field_name']])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '!=') {
									if(! ($condition['value'] != $input[$condition['attribute_type']][$condition['field_name']])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '>') {
									if(! ($input[$condition['attribute_type']][$condition['field_name']] > $condition['value'])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '<') {
									if(! ($input[$condition['attribute_type']][$condition['field_name']] < $condition['value'])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == 'contains') {
									if(! strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $condition['value'])) {
										$conditions_passed = false;
									}
								} else { // not_contains
									if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $condition['value'])) {
										$conditions_passed = false;
									}
								}
							} else {
								$multiple_values = explode('@#$', $condition['value']);
								if($condition['operator'] == '==') {
									if(! in_array($input[$condition['attribute_type']][$condition['field_name']], $multiple_values)){
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '!=') {
									if(in_array($input[$condition['attribute_type']][$condition['field_name']], $multiple_values)){
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '>') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(($input[$condition['attribute_type']][$condition['field_name']] > $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '<') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(($input[$condition['attribute_type']][$condition['field_name']] < $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == 'contains') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else { // not_contains
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == true) {
										$conditions_passed = false;
									}
								}
							}
						}
					}
					//End get Rule Conditions
					if($input_passed &&	$conditions_passed && $data['workflow_id'] == '20') { // Parts Sort




						// get workstation group id of the work station
						$getWorkstationGroupQ = "select GroupID from WorkstationConfiguration_mapping where SiteID='".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
						$getWorkstationGroupQEx = mysqli_query($this->connectionlink,$getWorkstationGroupQ);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						$GroupID = 0;
						if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
							$WorkstationGroup = mysqli_fetch_assoc($getWorkstationGroupQEx);
							$GroupID = $WorkstationGroup['GroupID'];
						} else {
							$json['Success'] = false;
							$json['Result'] = "No workstation Group available";
							return json_encode($json);
						}
						// get COO
						if(isset($data['COOID']) && $data['COOID'] != 'All'){
							$cooQuery = "select * from COO where COOID='".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."'";
							$cooQueryEx = mysqli_query($this->connectionlink,$cooQuery);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$CooRow = mysqli_fetch_assoc($cooQueryEx);
								$data['COO'] = $CooRow['COO'];
							} else {
								$json['Success'] = false;
								$json['Result'] = "No COO Data available";
								return json_encode($json);
							}
						}

						// get Bin for sort configuration based on sort validation key
						//Start get BIN Details for Parts Sort Page
						if($data['SortCriteriaID'] == '1') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 1";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$json['NoMapping'] = '1';
								$json['Success'] = true;
								$json['Result'] = "No Mapping Bin Available";
								return json_encode($json);
							}
						} else if($data['SortCriteriaID'] == '2') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and LOWER(m.mpn_id) = LOWER('".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."') and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 2";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 2";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '3') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 3";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 3";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '4') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 4";
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 4";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '5') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 5";
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 5";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 5";
									$q5 = mysqli_query($this->connectionlink,$query5);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
										$row5 = mysqli_fetch_assoc($q5);
										if($row5['AuditLocked'] == '1'){
											$json['Success'] = false;
											$json['Result'] = "BIN is locked for Audit";
											return json_encode($json);
										}
										$json['CustomPalletID'] = $row5['CustomPalletID'];
										$json['BinName'] = $row5['BinName'];
										$json['LocationID'] = $row5['LocationID'];
										$json['LocationName'] = $row5['LocationName'];
										$json['AssetsCount'] = $row5['AssetsCount'];
										$json['MaximumAssets'] = $row5['MaximumAssets'];
										$json['sortconfigurationid'] = $row5['sortconfigurationid'];
									}else{
										$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 5";
										$q5 = mysqli_query($this->connectionlink,$query5);
										if(mysqli_error($this->connectionlink)) {
											$json['Success'] = false;
											$json['Result'] = mysqli_error($this->connectionlink);
											return json_encode($json);
										}
										if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
											$row5 = mysqli_fetch_assoc($q5);
											if($row5['AuditLocked'] == '1'){
												$json['Success'] = false;
												$json['Result'] = "BIN is locked for Audit";
												return json_encode($json);
											}
											$json['CustomPalletID'] = $row5['CustomPalletID'];
											$json['BinName'] = $row5['BinName'];
											$json['LocationID'] = $row5['LocationID'];
											$json['LocationName'] = $row5['LocationName'];
											$json['AssetsCount'] = $row5['AssetsCount'];
											$json['MaximumAssets'] = $row5['MaximumAssets'];
											$json['sortconfigurationid'] = $row5['sortconfigurationid'];
										}else{
											$json['NoMapping'] = '1';
											$json['Success'] = true;
											$json['Result'] = "No Mapping Bin Available";
											return json_encode($json);
										}
									}
								}
							}
						} else if($data['SortCriteriaID'] == '6'){
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 6";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 6";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 6";
									$q5 = mysqli_query($this->connectionlink,$query5);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
										$row5 = mysqli_fetch_assoc($q5);
										if($row5['AuditLocked'] == '1'){
											$json['Success'] = false;
											$json['Result'] = "BIN is locked for Audit";
											return json_encode($json);
										}
										$json['CustomPalletID'] = $row5['CustomPalletID'];
										$json['BinName'] = $row5['BinName'];
										$json['LocationID'] = $row5['LocationID'];
										$json['LocationName'] = $row5['LocationName'];
										$json['AssetsCount'] = $row5['AssetsCount'];
										$json['MaximumAssets'] = $row5['MaximumAssets'];
										$json['sortconfigurationid'] = $row5['sortconfigurationid'];
									}else{
										$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 6";
										$q5 = mysqli_query($this->connectionlink,$query5);
										if(mysqli_error($this->connectionlink)) {
											$json['Success'] = false;
											$json['Result'] = mysqli_error($this->connectionlink);
											return json_encode($json);
										}
										if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
											$row5 = mysqli_fetch_assoc($q5);
											if($row5['AuditLocked'] == '1'){
												$json['Success'] = false;
												$json['Result'] = "BIN is locked for Audit";
												return json_encode($json);
											}
											$json['CustomPalletID'] = $row5['CustomPalletID'];
											$json['BinName'] = $row5['BinName'];
											$json['LocationID'] = $row5['LocationID'];
											$json['LocationName'] = $row5['LocationName'];
											$json['AssetsCount'] = $row5['AssetsCount'];
											$json['MaximumAssets'] = $row5['MaximumAssets'];
											$json['sortconfigurationid'] = $row5['sortconfigurationid'];
										}else{
											$json['NoMapping'] = '1';
											$json['Success'] = true;
											$json['Result'] = "No Mapping Bin Available";
											return json_encode($json);
										}
									}
								}
							}
						}
						//End get BIN Details for Parts Sort Page

						//End get Bin Details
						if($rule['sub_disposition'] == NULL) {
							$rule['sub_disposition'] = 'n/a';
						}
						$json['Success'] = true;
						$json['Result'] = $rule;
						return json_encode($json);

					} else if($input_passed &&	$conditions_passed ) {//Rule Satisfied
						//Start get Bin Details
						$query5 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m
						left join custompallet cp on m.CustomPalletID = cp.CustomPalletID
						where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."'";
						$q5 = mysqli_query($this->connectionlink,$query5);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
							$row5 = mysqli_fetch_assoc($q5);
							$json['CustomPalletID'] = $row5['CustomPalletID'];
							$json['BinName'] = $row5['BinName'];
						}
						//End get Bin Details
						if($rule['sub_disposition'] == NULL) {
							$rule['sub_disposition'] = 'n/a';
						}
						if($rule['sub_disposition_id'] > 0) {
							$query56 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m
							left join custompallet cp on m.CustomPalletID = cp.CustomPalletID
							where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['sub_disposition_id'])."'";
							$q56 = mysqli_query($this->connectionlink,$query56);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row56 = mysqli_fetch_assoc($q56);
								$json['Sub_CustomPalletID'] = $row56['CustomPalletID'];
								$json['Sub_BinName'] = $row56['BinName'];
							}
						}

						$json['Success'] = true;
						$json['Result'] = $rule;
						return json_encode($json);
					}
				}

				$json['Success'] = false;
				$json['Result'] = 'No Rules Satisfied';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Matching Rules Available';
				//$json['Result'] = $rules_query;
				return json_encode($json);
			}
			//End get Rules

			$json['Success'] = false;
			$json['Result'] = $input;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ApplyBusinessRule_AI($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		try {
			// Build optimized WHERE conditions to filter rules at database level
			$where_conditions = array();
			$where_conditions[] = "r.status = 'Active'";
			$where_conditions[] = "rv.current_version = '1'";

			// Add input_id filtering
			if(isset($data['input_id']) && $data['input_id'] > 0) {
				$where_conditions[] = "(r.input_id = '' OR r.input_id = '0' OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $data['input_id'])."', r.input_id) > 0)";
			}

			// Add Customer ID filtering - Updated to handle comma-separated Customer IDs in database
			if(isset($data['AWSCustomerID']) && $data['AWSCustomerID'] > 0) {
				// Input AWSCustomerID is a single integer, but database rules may have comma-separated values
				$customer_condition = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All'";
				$customer_condition .= " OR r.AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink, $data['AWSCustomerID'])."'";
				$customer_condition .= " OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $data['AWSCustomerID'])."', r.AWSCustomerID) > 0";
				$customer_condition .= ")";
				$where_conditions[] = $customer_condition;
			} else {
				// If AWSCustomerID is 0, empty, or not set - only check for All rules
				$where_conditions[] = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";
			}

			// Add Facility filtering
			if(isset($_SESSION['user']['FacilityID'])) {
				$where_conditions[] = "(r.FacilityID = 'all' OR r.FacilityID = 'All' OR r.FacilityID = '".mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID'])."')";
			}

			// Add Workflow filtering
			if(isset($data['workflow_id'])) {
				$where_conditions[] = "(r.workflow_id = 'all' OR r.workflow_id = 'All' OR r.workflow_id = '".mysqli_real_escape_string($this->connectionlink, $data['workflow_id'])."')";
			}

			// Add Part Type filtering using parttypeid
			$part_type_name = '';
			if(isset($data['parttypeid']) && $data['parttypeid'] > 0) {
				$parttype_query = "SELECT parttype FROM parttype WHERE parttypeid = '".mysqli_real_escape_string($this->connectionlink, $data['parttypeid'])."'";
				$parttype_result = mysqli_query($this->connectionlink, $parttype_query);
				if($parttype_result && mysqli_num_rows($parttype_result) > 0) {
					$parttype_row = mysqli_fetch_assoc($parttype_result);
					$part_type_name = $parttype_row['parttype'];
					$where_conditions[] = "(r.part_types = 'all' OR r.part_types = 'All' OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $part_type_name)."', r.part_types) > 0)";
				}
			}

			// Add Source Type filtering
			if(isset($data['idCustomertype'])) {
				$where_conditions[] = "(r.idCustomertype = 'all' OR r.idCustomertype = 'All' OR r.idCustomertype = '".mysqli_real_escape_string($this->connectionlink, $data['idCustomertype'])."')";
			}

			// Add Material Type filtering
			if(isset($data['MaterialType'])) {
				$where_conditions[] = "(r.MaterialType = 'all' OR r.MaterialType = 'All' OR r.MaterialType = '".mysqli_real_escape_string($this->connectionlink, $data['MaterialType'])."')";
			}

			// Get filtered business rules ordered by priority
			$rules_query = "SELECT r.*, d.disposition, d.color_code, d.color
							FROM business_rule r
							LEFT JOIN disposition d ON r.disposition_id = d.disposition_id
							LEFT JOIN business_rule_versions rv ON r.version_id = rv.version_id
							WHERE " . implode(' AND ', $where_conditions) . "
							ORDER BY r.priority ASC";

			$rules_result = mysqli_query($this->connectionlink, $rules_query);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$rule_count = 0;

			// Loop through each pre-filtered business rule until we find the first satisfied one
			while($rule = mysqli_fetch_assoc($rules_result)) {
				$rule_count++;
				$conditions_passed = true;

				// Check Rule Conditions from business_rule_condition table
				$conditions_query = "SELECT c.*, a.attribute_id, a.attribute_name, a.attribute_type, a.field_name
									FROM business_rule_condition c, business_rule_attributes a
									WHERE c.attribute_id = a.attribute_id AND c.rule_id = '".mysqli_real_escape_string($this->connectionlink, $rule['rule_id'])."'";

				$conditions_result = mysqli_query($this->connectionlink, $conditions_query);

				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = 'Database error in conditions: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				// If conditions are defined, check them
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($condition = mysqli_fetch_assoc($conditions_result)) {
						// Get the value from input data based on attribute type and field name
						$input_value = '';
						if(isset($data[$condition['field_name']])) {
							$input_value = $data[$condition['field_name']];
						}

						// Check condition based on operator
						if($condition['multiple_values'] == 0) {
							// Single value condition
							if($condition['operator'] == '==') {
								if($condition['value'] != $input_value) {
									$conditions_passed = false;
									break;
								}
							} else if($condition['operator'] == '!=') {
								if($condition['value'] == $input_value) {
									$conditions_passed = false;
									break;
								}
							} else if($condition['operator'] == '>') {
								if(!($input_value > $condition['value'])) {
									$conditions_passed = false;
									break;
								}
							} else if($condition['operator'] == '<') {
								if(!($input_value < $condition['value'])) {
									$conditions_passed = false;
									break;
								}
							} else if($condition['operator'] == 'contains') {
								if(!strpos("123".$input_value, $condition['value'])) {
									$conditions_passed = false;
									break;
								}
							} else if($condition['operator'] == 'not_contains') {
								if(strpos("123".$input_value, $condition['value'])) {
									$conditions_passed = false;
									break;
								}
							}
						} else {
							// Multiple values condition
							$multiple_values = explode('@#$', $condition['value']);

							if($condition['operator'] == '==') {
								if(!in_array($input_value, $multiple_values)) {
									$conditions_passed = false;
									break;
								}
							} else if($condition['operator'] == '!=') {
								if(in_array($input_value, $multiple_values)) {
									$conditions_passed = false;
									break;
								}
							}
							// Add other operators as needed
						}
					}
				}

				// If all conditions passed, return this rule
				if($conditions_passed) {
					$json['Success'] = true;
					$json['Result'] = 'Business rule satisfied - applying disposition';
					$json['RuleApplied'] = array(
						'rule_id' => $rule['rule_id'],
						'rule_id_text' => $rule['rule_id_text'],
						'rule_name' => $rule['rule_name'],
						'rule_description' => $rule['rule_description'],
						'priority' => $rule['priority'],
						'disposition_id' => $rule['disposition_id'],
						'disposition_name' => $rule['disposition'],
						'rule_summary' => $rule['rule_summary'],
						'color_code' => $rule['color_code'],
						'color' => $rule['color']
					);
					$json['RulesChecked'] = $rule_count;
					return json_encode($json);
				}
			}

			// If we reach here, no rules were satisfied
			$json['Success'] = false;
			$json['Result'] = 'No Rules Satisfied';
			$json['RuleApplied'] = null;
			$json['RulesChecked'] = $rule_count;

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = 'Error: ' . $e->getMessage();
		}

		return json_encode($json);
	}

	public function GetPendingLoads($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Shipments')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Shipments Page';
				return json_encode($json);
			}
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['UserId']
			);
			$query = "select P.idPallet,CU.CustomerName,L.LocationName,U.FirstName,U.LastName,P.ReceivedDate
			from pallets P,location L,users U,loads LO,customer CU
			where
			U.UserId = P.CreatedBy
			AND L.LocationID = P.WarehouseLocationId
			AND CU.CustomerID = LO.idCustomer
			AND P.LoadId = LO.LoadId
			AND LO.FacilityID = '".$_SESSION['user']['FacilityID']."' AND (P.status = 1 or P.status = 7)";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'idPallet') {
							$query = $query . " AND P.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'CustomerName') {
							$query = $query . " AND CU.CustomerName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'LocationName') {
							$query = $query . " AND L.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'UserName') {
							$query = $query . " AND (U.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR U.LastName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%') ";
						}
						if($key == 'ReceivedDate') {
							$query = $query . " AND P.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
				  if($data['OrderBy'] == 'idPallet') {
					$query = $query . " order by P.idPallet ".$order_by_type." ";
				} else if($data['OrderBy'] == 'CustomerName') {
					$query = $query . " order by CU.CustomerName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'LocationName') {
					$query = $query . " order by L.LocationName ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'UserName') {
					$query = $query . " order by U.FirstName ".$order_by_type." ";
				} elseif($data['OrderBy'] == 'ReceivedDate') {
					$query = $query . " order by P.ReceivedDate ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by P.idPallet desc ";
			}
			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink))
			{
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0)
			{
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$parttype = '';
					/*$sqlpalitems = "Select SUM(PI.quantity) as palquantity, CC.part_type from pallet_items PI, catlog_creation CC
					WHERE
					CC.mpn_id = PI.UniversalModelNumber
					AND PI.palletId = '".$row['idPallet']."'
					GROUP BY PI.UniversalModelNumber";*/

					// $sqlpalitems = "Select SUM(PI.quantity) as palquantity, CC.part_type from pallet_items PI, catlog_creation CC
					// WHERE
					// CC.mpn_id = PI.UniversalModelNumber
					// AND PI.palletId = '".$row['idPallet']."'
					// GROUP BY CC.part_type";

					$sqlpalitems = "Select count(*) as palquantity, CC.part_type from asn_assets PI, catlog_creation CC
					WHERE
					CC.mpn_id = PI.UniversalModelNumber
					AND PI.idPallet = '".$row['idPallet']."'
					GROUP BY CC.part_type";

					$querypalitems = mysqli_query($this->connectionlink,$sqlpalitems);
					while($rowpalitems = mysqli_fetch_assoc($querypalitems)) {
						$parttype = $parttype." ".$rowpalitems['palquantity']."-".$rowpalitems['part_type']."\n";
					}
					$row['parttype'] = $parttype;
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Pallets Available";
			}
			$query1 = "select count(*) from pallets P,location L,users U,loads LO,customer CU
			where
			U.UserId = P.CreatedBy
			AND L.LocationID = P.WarehouseLocationId
			AND CU.CustomerID = LO.idCustomer
			AND P.LoadId = LO.LoadId
			AND LO.FacilityID = '".$_SESSION['user']['FacilityID']."' AND (P.status = 1 or P.status = 7)";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'idPallet') {
							$query1 = $query1 . " AND P.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'CustomerName') {
							$query1 = $query1 . " AND CU.CustomerName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'LocationName') {
							$query1 = $query1 . " AND L.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'UserName') {
							$query1 = $query1 . " AND (U.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' OR U.LastName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%') ";
						}
						if($key == 'ReceivedDate') {
							$query1 = $query1 . " AND P.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$count = $row1['count(*)'];
			}
			$json['total'] = $count;
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


	public function GetFacilityStations ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Serial')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Receive Serial Page';
			// 	return json_encode($json);
			// }
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
				return json_encode($json);
			}
			//$query = "select SiteID,SiteName from site where Status = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' order by SiteName";
			$query = "select SiteID,SiteName from site where Status = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' and (Locked = '0' or LockedForUser = '".$_SESSION['user']['UserId']."') and workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' order by SiteName";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$stations = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$stations[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $stations;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Stations Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetStationCustomPallets ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
				return json_encode($json);
			}

			$query = "select d.disposition_id,d.disposition,cp.CustomPalletID,cp.BinName,cp.AssetsCount from disposition d
			left join station_custompallet_mapping m on d.disposition_id = m.disposition_id and m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'
			left join custompallet cp on m.CustomPalletID = cp.CustomPalletID and cp.StatusID = '1'
			where d.Status = 'Active' order by d.disposition ";

			$query = "select d.disposition_id,d.disposition,cp.CustomPalletID,cp.BinName,cp.AssetsCount,l.LocationID,l.LocationName,l.LocationType,ll.lable_name from station_disposition_mapping sm
			left join disposition d on sm.disposition_id = d.disposition_id
			left join station_custompallet_mapping m on d.disposition_id = m.disposition_id and m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'
			left join custompallet cp on m.CustomPalletID = cp.CustomPalletID and cp.StatusID = '1'
			left join location l on cp.LocationID = l.LocationID
			left join lables ll on l.lable_name = ll.id
			where d.Status = 'Active' and sm.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' order by d.disposition ";

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$stations = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					if(($data['CustomPalletID'] > 0) && ($row['CustomPalletID'] == $data['CustomPalletID'])) {
						$json['Success'] = false;
						$json['Result'] = 'Source BIN is defined as Output BIN for Disposition '.$row['disposition'];
						return json_encode($json);
					}
					$stations[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $stations;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Data Available";
			}

			//Start Unlock user stations
			$this->UnlockUserStations();
			//End Unlock user stations

			//Start check if Station is locked or not
			$query1 = "select * from site where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['Locked'] == '1') {
					$json['Success'] = false;
					$json['Result'] = 'Station ('.$row1['SiteName'].') is Locked';
					return json_encode($json);
				}
				$query2 = "update site set Locked = '1', LockedForUser = '".$_SESSION['user']['UserId']."' where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Station';
				return json_encode($json);
			}
			//End check if Station is locked or not

			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetStationCustomPallets1 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
				return json_encode($json);
			}

			$query = "select d.disposition_id,d.disposition,cp.CustomPalletID,cp.BinName,cp.AssetsCount from disposition d
			left join station_custompallet_mapping m on d.disposition_id = m.disposition_id and m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'
			left join custompallet cp on m.CustomPalletID = cp.CustomPalletID and cp.StatusID = '1'
			where d.Status = 'Active' order by d.disposition ";

			$query = "select d.disposition_id,d.disposition,d.serialized,d.switch_disposition,cp.CustomPalletID,cp.BinName,cp.AssetsCount,l.LocationID,l.LocationName,l.LocationType,ll.lable_name from station_disposition_mapping sm
			left join disposition d on sm.disposition_id = d.disposition_id
			left join station_custompallet_mapping m on d.disposition_id = m.disposition_id and m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'
			left join custompallet cp on m.CustomPalletID = cp.CustomPalletID and cp.StatusID = '1'
			left join location l on cp.LocationID = l.LocationID
			left join lables ll on l.lable_name = ll.id
			where d.Status = 'Active' and sm.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' order by d.disposition ";

			$query = "select d.disposition_id,d.disposition,d.serialized,d.switch_disposition,cp.CustomPalletID,cp.BinName,cp.AssetsCount,l.LocationID,l.LocationName,l.LocationType,ll.lable_name,scm.ShippingContainerID from station_disposition_mapping sm
			left join disposition d on sm.disposition_id = d.disposition_id
			left join station_custompallet_mapping m on d.disposition_id = m.disposition_id and m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'
			left join station_container_mapping scm on d.disposition_id = scm.disposition_id and scm.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'
			left join custompallet cp on m.CustomPalletID = cp.CustomPalletID and cp.StatusID = '1'
			left join location l on cp.LocationID = l.LocationID
			left join lables ll on l.lable_name = ll.id
			where d.Status = 'Active' and sm.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' order by d.disposition ";

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$stations = array();
			$serialized = array();
			$unserialized = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					if(($data['CustomPalletID'] > 0) && ($row['CustomPalletID'] == $data['CustomPalletID'])) {
						$json['Success'] = false;
						$json['Result'] = 'Source BIN is defined as Output BIN for Disposition '.$row['disposition'];
						return json_encode($json);
					}
					if($row['serialized'] == 'Yes') {
						$serialized[] = $row;
					} else {
						//Start get AssetsCount from Shipping Container
						if($row['ShippingContainerID'] != '') {
							$query11 = "select sum(Quantity) as AssetsCount from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$row['ShippingContainerID'])."'";
							$q11 = mysqli_query($this->connectionlink,$query11);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row11 = mysqli_fetch_assoc($q11);
								$row['AssetsCount'] = $row11['AssetsCount'];
							} else {
								$row['AssetsCount'] = 0;
							}
						} else {
							$row['AssetsCount'] = 0;
						}
						//End get AssetsCount from Shipping Container
						$unserialized[] = $row;
					}
					// $stations[$i] = $row;
					// $i = $i + 1;

					$stations['Serialized'] = $serialized;
					$stations['Unserialized'] = $unserialized;
					$json['Success'] = true;
					$json['Result'] = $stations;
				}
				$json['Success'] = true;
				$json['Result'] = $stations;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Data Available";
			}

			//Start Unlock user stations
			$this->UnlockUserStations();
			//End Unlock user stations

			//Start check if Station is locked or not
			$query1 = "select * from site where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['Locked'] == '1') {
					$json['Success'] = false;
					$json['Result'] = 'Station ('.$row1['SiteName'].') is Locked';
					return json_encode($json);
				}
				$query2 = "update site set Locked = '1', LockedForUser = '".$_SESSION['user']['UserId']."' where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Station';
				return json_encode($json);
			}
			//End check if Station is locked or not

			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	// public function MapCustomPalletToDisposition ($data) {
	// 	if(!isset($_SESSION['user'])) {
	// 		$json['Success'] = false;
	// 		$json['Result'] = 'Login to continue';
	// 		return json_encode($json);
	// 	}
	// 	$json = array(
	// 		'Success' => false,
	// 		'Result' => $data
	// 	);
	// 	try {
	// 		if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
	// 			$json['Success'] = false;
	// 			$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
	// 			return json_encode($json);
	// 		}
	// 		//Start get Custom Pallet Details
	// 		$query = "select cp.* from custompallet cp where cp.StatusID = '1' and cp.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' and cp.FacilityID = '".$_SESSION['user']['FacilityID']."' and cp.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
	// 		$q = mysqli_query($this->connectionlink,$query);
	// 		if(mysqli_error($this->connectionlink)) {
	// 			$json['Success'] = false;
	// 			$json['Result'] = mysqli_error($this->connectionlink);
	// 			return json_encode($json);
	// 		}
	// 		if(mysqli_affected_rows($this->connectionlink) > 0) {
	// 			$row = mysqli_fetch_assoc($q);
	// 			if($row['SiteID'] > 0 && $row['SiteID'] == $data['SiteID']) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = 'BIN already assigned';
	// 				return json_encode($json);
	// 			} else if($row['SiteID'] > 0){
	// 				$json['Success'] = false;
	// 				$json['Result'] = 'BIN Assigned to different Station';
	// 				return json_encode($json);
	// 			}
	// 			$CustomPalletID = $row['CustomPalletID'];
	// 		} else {
	// 			$json['Success'] = false;
	// 			$json['Result'] = 'Invalid BIN';
	// 			return json_encode($json);
	// 		}
	// 		//End get Custom Pallet Details

	// 		$query1 = "select * from station_custompallet_mapping where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
	// 		$q1 = mysqli_query($this->connectionlink,$query1);
	// 		if(mysqli_error($this->connectionlink)) {
	// 			$json['Success'] = false;
	// 			$json['Result'] = mysqli_error($this->connectionlink);
	// 			return json_encode($json);
	// 		}
	// 		if(mysqli_affected_rows($this->connectionlink) > 0) {// Updating existing mapping
	// 			$row1 = mysqli_fetch_assoc($q1);

	// 			//Start update Custom Pallet
	// 			$query20 = "update custompallet set SiteID = NULL where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row1['CustomPalletID'])."'";
	// 			$q20 = mysqli_query($this->connectionlink,$query20);
	// 			if(mysqli_error($this->connectionlink)) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = mysqli_error($this->connectionlink);
	// 				return json_encode($json);
	// 			}
	// 			//End update Custom Pallet

	// 			//Start Admin Tracking
	// 			$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$row1['CustomPalletID'])."','BIN removed from Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
	// 			$q10 = mysqli_query($this->connectionlink,$query10);
	// 			if(mysqli_error($this->connectionlink)) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = mysqli_error($this->connectionlink);
	// 				return json_encode($json);
	// 			}
	// 			//End Admin Tracking

	// 			$query2 = "update station_custompallet_mapping set CustomPalletID = '".$CustomPalletID."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where map_id = '".$row1['map_id']."'";
	// 			$q2 = mysqli_query($this->connectionlink,$query2);
	// 			if(mysqli_error($this->connectionlink)) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = mysqli_error($this->connectionlink);
	// 				return json_encode($json);
	// 			}

	// 			//Start Admin Tracking
	// 			$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."','BIN assigned to Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
	// 			$q10 = mysqli_query($this->connectionlink,$query10);
	// 			if(mysqli_error($this->connectionlink)) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = mysqli_error($this->connectionlink);
	// 				return json_encode($json);
	// 			}
	// 			//End Admin Tracking

	// 			//Start update Custom Pallet
	// 			$query20 = "update custompallet set SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."'";
	// 			$q20 = mysqli_query($this->connectionlink,$query20);
	// 			if(mysqli_error($this->connectionlink)) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = mysqli_error($this->connectionlink);
	// 				return json_encode($json);
	// 			}
	// 			//End update Custom Pallet

	// 		} else {//Creating new mapping
	// 			$query3 = "insert into station_custompallet_mapping (disposition_id,SiteID,CustomPalletID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."',NOW(),'".$_SESSION['user']['UserId']."')";
	// 			$q3 = mysqli_query($this->connectionlink,$query3);
	// 			if(mysqli_error($this->connectionlink)) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = mysqli_error($this->connectionlink);
	// 				return json_encode($json);
	// 			}

	// 			//Start Admin Tracking
	// 			$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."','BIN assigned to Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
	// 			$q10 = mysqli_query($this->connectionlink,$query10);
	// 			if(mysqli_error($this->connectionlink)) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = mysqli_error($this->connectionlink);
	// 				return json_encode($json);
	// 			}
	// 			//End Admin Tracking

	// 			//Start update Custom Pallet
	// 			$query20 = "update custompallet set SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."'";
	// 			$q20 = mysqli_query($this->connectionlink,$query20);
	// 			if(mysqli_error($this->connectionlink)) {
	// 				$json['Success'] = false;
	// 				$json['Result'] = mysqli_error($this->connectionlink);
	// 				return json_encode($json);
	// 			}
	// 			//End update Custom Pallet
	// 		}
	// 		$json['Success'] = true;
	// 		$json['Result'] = 'Success';
	// 		$json['CustomPalletID'] = $row['CustomPalletID'];
	// 		$json['AssetsCount'] = $row['AssetsCount'];
	// 		return json_encode($json);

	// 	} catch (Exception $e) {
	// 		$json['Success'] = false;
	// 		$json['Result'] = $e->getMessage();
	// 		return json_encode($json);
	// 	}
	// }

	public function MapCustomPalletToDisposition1 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
				return json_encode($json);
			}

			//Start get Site Details
			$query6 = "select * from site where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' ";
			$q6 = mysqli_query($this->connectionlink,$query6);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$site = mysqli_fetch_assoc($q6);
				if($site['GroupID'] > 0) {

					//Start get free location from group selected
					$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$site['GroupID'])."'";
					$q112 = mysqli_query($this->connectionlink,$query112);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row112 = mysqli_fetch_assoc($q112);
						$data['NewLocationID'] = $row112['LocationID'];
						$newLocationName = $row112['LocationName'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'No locations available, in Stations Location Group';
						return json_encode($json);
					}
					//End get free location from group selected


				} else {
					$json['Success'] = false;
					$json['Result'] = 'Location Group is not mapped to Station';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Station';
				return json_encode($json);
			}
			//End get Site Details

			//Start get Custom Pallet Details
			//$query = "select cp.* from custompallet cp where cp.StatusID = '1' and cp.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' and cp.FacilityID = '".$_SESSION['user']['FacilityID']."' and cp.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
			$query = "select cp.*,s.Status,l.LocationID,l.LocationName,l.LocationType,ll.lable_name from custompallet cp
			left join custompallet_status s on cp.StatusID = s.StatusID
			left join location l on cp.LocationID = l.LocationID
			left join lables ll on l.lable_name = ll.id
			where cp.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' and cp.FacilityID = '".$_SESSION['user']['FacilityID']."' and cp.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is '.$row['Status'];
					return json_encode($json);
				}

				if($row['SiteID'] > 0 && $row['SiteID'] == $data['SiteID']) {
					// $json['Success'] = false;
					// $json['Result'] = 'BIN already assigned';
					// return json_encode($json);
				} else if($row['SiteID'] > 0){
					// $json['Success'] = false;
					// $json['Result'] = 'BIN Assigned to different Station';
					// return json_encode($json);
				}
				$CustomPalletID = $row['CustomPalletID'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid BIN';
				return json_encode($json);
			}
			//End get Custom Pallet Details

			//Start check IF BIN is used as any of the source BIN
			$query5 = "select count(*) from source_bin_user_mapping where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."' ";
			$q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row5 = mysqli_fetch_assoc($q5);
				if($row5['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = "Bin is selected as Source BIN,Source BIN can't be defined as Output BIN";
					return json_encode($json);
				}
			}
			//End check IF BIN is used as any of the source BIN

			if(($data['SourceCustomPalletID'] > 0) && ($data['SourceCustomPalletID'] == $CustomPalletID)) {
				$json['Success'] = false;
				$json['Result'] = "Bin is selected as Source BIN,Source BIN can't be defined as Output BIN";
				return json_encode($json);
			}

			$query1 = "select * from station_custompallet_mapping where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {// Updating existing mapping
				$row1 = mysqli_fetch_assoc($q1);

				//Start update Custom Pallet
				// $query20 = "update custompallet set SiteID = NULL where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row1['CustomPalletID'])."'";
				// $q20 = mysqli_query($this->connectionlink,$query20);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				//End update Custom Pallet

				//Start Admin Tracking
				// $query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$row1['CustomPalletID'])."','BIN removed from Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
				// $q10 = mysqli_query($this->connectionlink,$query10);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				//End Admin Tracking

				$query2 = "update station_custompallet_mapping set CustomPalletID = '".$CustomPalletID."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where map_id = '".$row1['map_id']."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				//Start Admin Tracking
				$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."','BIN assigned to Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Admin Tracking

				//Start update Custom Pallet
				// $query20 = "update custompallet set SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."'";
				// $q20 = mysqli_query($this->connectionlink,$query20);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				//End update Custom Pallet

			} else {//Creating new mapping
				$query3 = "insert into station_custompallet_mapping (disposition_id,SiteID,CustomPalletID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."',NOW(),'".$_SESSION['user']['UserId']."')";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				//Start Admin Tracking
				$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."','BIN assigned to Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Admin Tracking

				//Start update Custom Pallet
				// $query20 = "update custompallet set SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."'";
				// $q20 = mysqli_query($this->connectionlink,$query20);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				//End update Custom Pallet
			}

			//Start moving Bin To Sites Location Group

			//Start check if valid Group
			$query10 = "select GroupID,LocationType,BinTypeID from location_group where GroupID = '".mysqli_real_escape_string($this->connectionlink,$site['GroupID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row10 = mysqli_fetch_assoc($q10);
				// if($row10['LocationType'] != 'WIP') {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Only WIP Location groups are allowed';
				// 	return json_encode($json);
				// }
				// if($row10['BinTypeID'] != $row['BinTypeID']) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Bin Type not matching with Location Group Bin Type';
				// 	return json_encode($json);
				// }
				$data['GroupID'] = $row10['GroupID'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Location Group';
				return json_encode($json);
			}
			//End check if valid Group

			//Start get free location from group selected
			$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['GroupID'])."'";
			$q112 = mysqli_query($this->connectionlink,$query112);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row112 = mysqli_fetch_assoc($q112);
				$data['NewLocationID'] = $row112['LocationID'];
				$newLocationName = $row112['LocationName'];
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No locations available, in selected group';
				return json_encode($json);
			}
			//End get free location from group selected


			$query2 = "update custompallet set LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$row['LocationID']."'";
			$querylocold = mysqli_query($this->connectionlink,$sqllocold);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				//return json_encode($json);
			}

			$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Bin',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
			$queryloc = mysqli_query($this->connectionlink,$sqlloc);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				//return json_encode($json);
			}
			//End moving Bin To Sites Location Group

			$json['Success'] = true;
			$json['Result'] = 'Success';
			$json['CustomPalletID'] = $row['CustomPalletID'];
			$json['AssetsCount'] = $row['AssetsCount'];

			$json['LocationType'] = $row['LocationType'];
			$json['lable_name'] = $row['lable_name'];
			$json['LocationName'] = $row['LocationName'];
			$json['LocationID'] = $row['LocationID'];
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function MapCustomPalletToDisposition ($data) {
		//echo "<pre>";print_r($data);exit;
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
				return json_encode($json);
			}



			//Start get Site Details
			$query6 = "select * from site where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' ";
			$q6 = mysqli_query($this->connectionlink,$query6);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$site = mysqli_fetch_assoc($q6);
				if($site['GroupID'] <= 0) {
					$json['Success'] = false;
					$json['Result'] = 'Location Group is not mapped to Station';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Station';
				return json_encode($json);
			}
			//End get Site Details




			// Get disposition details
			$getDipositionQuery = "select * from disposition where disposition_id='".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
			$getDipositionQueryEx = mysqli_query($this->connectionlink,$getDipositionQuery);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$dispositionDetails = array();
			if(mysqli_affected_rows($this->connectionlink) > 0){
				$dispositionDetails = mysqli_fetch_assoc($getDipositionQueryEx);
			}else{
				$json['Success'] = false;
				$json['Result'] = "Invalid Disposition";
				return json_encode($json);
			}
			//Start get Custom Pallet Details
			$query = "select cp.*, l.GroupID as CurrentGroupID, cp.ParentCustomPalletID from custompallet cp
					  left join location l on cp.LocationID = l.LocationID
					  where cp.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' and cp.FacilityID = '".$_SESSION['user']['FacilityID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$binDetails = mysqli_fetch_assoc($q);

				// Check if bin is a child bin (has a parent)
				if(!empty($binDetails['ParentCustomPalletID']) && $binDetails['ParentCustomPalletID'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Child bins cannot be moved to Location Group.';
					return json_encode($json);
				}

				// Check if bin needs to be moved to different location group
				$needsLocationMove = true;
				if(!empty($binDetails['CurrentGroupID']) && $binDetails['CurrentGroupID'] == $site['GroupID']) {
					// Bin is already in the same location group, no need to move
					$needsLocationMove = false;
				}

				// Only find new location if bin needs to be moved to different group
				if($needsLocationMove && $site['GroupID'] > 0) {
					//Start get free location from group selected
					$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$site['GroupID'])."'";
					$q112 = mysqli_query($this->connectionlink,$query112);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row112 = mysqli_fetch_assoc($q112);
						$data['NewLocationID'] = $row112['LocationID'];
						$newLocationName = $row112['LocationName'];
					} else {
						$json['Success'] = false;
						$json['Result'] = 'No locations available, in Stations Location Group';
						return json_encode($json);
					}
					//End get free location from group selected
				}

				if($dispositionDetails['serialized'] == 'No'){
					$json['Success'] = false;
					//$json['Result'] = "Bin can't be assigned to unserialized disposition (part type)";
					$json['Result'] = "Invalid Container";
					return json_encode($json);
				}
				// First check for bin with same disposition
				$query = "select cp.* from custompallet cp where cp.StatusID = '1' and cp.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' and cp.FacilityID = '".$_SESSION['user']['FacilityID']."' and cp.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$binWithSameDisposition = false;
				$binWithoutDisposition = false;
				$binRow = null;

				if(mysqli_affected_rows($this->connectionlink) > 0) {
					// Bin found with same disposition
					$binWithSameDisposition = true;
					$binRow = mysqli_fetch_assoc($q);
				} else {
					// Check for bin without disposition (disposition_id is NULL or 0)
					$queryNoDisp = "select cp.* from custompallet cp where cp.StatusID = '1' and cp.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' and cp.FacilityID = '".$_SESSION['user']['FacilityID']."' and (cp.disposition_id IS NULL OR cp.disposition_id = '0' OR cp.disposition_id = '')";
					$qNoDisp = mysqli_query($this->connectionlink,$queryNoDisp);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						// Bin found without disposition
						$binWithoutDisposition = true;
						$binRow = mysqli_fetch_assoc($qNoDisp);
					}
				}

				// Process if we found a valid bin (either with same disposition or without disposition)
				if($binWithSameDisposition || $binWithoutDisposition) {
					$row = $binRow;
					if($row['SiteID'] > 0 && $row['SiteID'] == $data['SiteID']) {
							// $json['Success'] = false;
							// $json['Result'] = 'BIN already assigned';
							// return json_encode($json);
						} else if($row['SiteID'] > 0){
							// $json['Success'] = false;
							// $json['Result'] = 'BIN Assigned to different Station';
							// return json_encode($json);
						}
						$CustomPalletID = $row['CustomPalletID'];

					//End get Custom Pallet Details

					//Start check IF BIN is used as any of the source BIN
					$query5 = "select count(*) from source_bin_user_mapping where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."' ";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row5 = mysqli_fetch_assoc($q5);
						if($row5['count(*)'] > 0) {
							$json['Success'] = false;
							$json['Result'] = "Bin is selected as Source BIN,Source BIN can't be defined as Output BIN";
							return json_encode($json);
						}
					}
					//End check IF BIN is used as any of the source BIN

					if(($data['SourceCustomPalletID'] > 0) && ($data['SourceCustomPalletID'] == $CustomPalletID)) {
						$json['Success'] = false;
						$json['Result'] = "Bin is selected as Source BIN,Source BIN can't be defined as Output BIN";
						return json_encode($json);
					}

					$query1 = "select * from station_custompallet_mapping where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {// Updating existing mapping
						$row1 = mysqli_fetch_assoc($q1);

						//Start update Custom Pallet
						// $query20 = "update custompallet set SiteID = NULL where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row1['CustomPalletID'])."'";
						// $q20 = mysqli_query($this->connectionlink,$query20);
						// if(mysqli_error($this->connectionlink)) {
						// 	$json['Success'] = false;
						// 	$json['Result'] = mysqli_error($this->connectionlink);
						// 	return json_encode($json);
						// }
						//End update Custom Pallet

						//Start Admin Tracking
						// $query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$row1['CustomPalletID'])."','BIN removed from Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
						// $q10 = mysqli_query($this->connectionlink,$query10);
						// if(mysqli_error($this->connectionlink)) {
						// 	$json['Success'] = false;
						// 	$json['Result'] = mysqli_error($this->connectionlink);
						// 	return json_encode($json);
						// }
						//End Admin Tracking

						$query2 = "update station_custompallet_mapping set CustomPalletID = '".$CustomPalletID."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where map_id = '".$row1['map_id']."'";					
						$q2 = mysqli_query($this->connectionlink,$query2);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						//Start Admin Tracking
						$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."','BIN assigned to Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
						$q10 = mysqli_query($this->connectionlink,$query10);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						//End Admin Tracking

						//Start update Custom Pallet
						// $query20 = "update custompallet set SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."'";
						// $q20 = mysqli_query($this->connectionlink,$query20);
						// if(mysqli_error($this->connectionlink)) {
						// 	$json['Success'] = false;
						// 	$json['Result'] = mysqli_error($this->connectionlink);
						// 	return json_encode($json);
						// }
						//End update Custom Pallet

					} else {//Creating new mapping
						$query3 = "insert into station_custompallet_mapping (disposition_id,SiteID,CustomPalletID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."',NOW(),'".$_SESSION['user']['UserId']."')";
						$q3 = mysqli_query($this->connectionlink,$query3);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						//Start Admin Tracking
						$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."','BIN assigned to Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
						$q10 = mysqli_query($this->connectionlink,$query10);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						//End Admin Tracking

						//Start update Custom Pallet
						// $query20 = "update custompallet set SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."'";
						// $q20 = mysqli_query($this->connectionlink,$query20);
						// if(mysqli_error($this->connectionlink)) {
						// 	$json['Success'] = false;
						// 	$json['Result'] = mysqli_error($this->connectionlink);
						// 	return json_encode($json);
						// }
						//End update Custom Pallet
					}



					//Start moving Bin To Sites Location Group (only if NewLocationID is set)
					if(isset($data['NewLocationID']) && !empty($data['NewLocationID'])) {
						$query2 = "update custompallet set LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
						$q2 = mysqli_query($this->connectionlink,$query2);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						// Unlock old location if bin had one
						if(!empty($row['LocationID'])) {
							$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$row['LocationID']."'";
							$querylocold = mysqli_query($this->connectionlink,$sqllocold);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								//return json_encode($json);
							}
						}

						// Lock new location
						$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Bin',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
						$queryloc = mysqli_query($this->connectionlink,$sqlloc);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							//return json_encode($json);
						}
					}
					//End moving Bin To Sites Location Group

					// If bin was without disposition, update it to match station's disposition
					if($binWithoutDisposition) {
						$updateDispositionQuery = "UPDATE custompallet SET disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."', LastModifiedDate = NOW(), LastModifiedBy = '".$_SESSION['user']['UserId']."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
						$updateDispositionResult = mysqli_query($this->connectionlink,$updateDispositionQuery);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = 'Failed to update bin disposition: ' . mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						// Add tracking record for disposition update
						$trackingAction = "Bin disposition updated to match station disposition during mapping";
						$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy) VALUES ('".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."', '$trackingAction', NOW(), '".$_SESSION['user']['UserId']."')";
						mysqli_query($this->connectionlink,$trackingQuery);
					}


				} else {

					$json['Success'] = false;
					$json['Result'] = 'Please select a bin from the same Disposition.';
					return json_encode($json);
				}
				$json['Success'] = true;
				$json['Result'] = 'Success';
				$json['CustomPalletID'] = $row['CustomPalletID'];
				$json['AssetsCount'] = $row['AssetsCount'];
				return json_encode($json);
			} else {
				if($dispositionDetails['serialized'] == 'Yes'){
					$json['Success'] = false;
					//$json['Result'] = "Container can't be assigned to serialized disposition (part type)";
					$json['Result'] = "Invalid Bin";
					return json_encode($json);
				}
				if(!empty($data['ShippingContainerID'])){
					$getContainers = "select sc.* from shipping_containers sc where sc.ShippingContainerID='".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and sc.FacilityID = '".$_SESSION['user']['FacilityID']."'";
				}else{
					$getContainers = "select sc.* from shipping_containers sc where sc.ShippingContainerID='".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' and sc.FacilityID = '".$_SESSION['user']['FacilityID']."'";
				}
				//echo $getContainers;exit;
				$getContainersEx = mysqli_query($this->connectionlink,$getContainers);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink)>0){
					if(!empty($data['ShippingContainerID'])){
						$getContainers = "select sc.* from shipping_containers sc where sc.ShippingContainerID='".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and sc.StatusID='1' and sc.FacilityID = '".$_SESSION['user']['FacilityID']."'";
					}else{
						$getContainers = "select sc.* from shipping_containers sc where sc.ShippingContainerID='".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' and sc.StatusID='1' and sc.FacilityID = '".$_SESSION['user']['FacilityID']."'";
					}
					$getContainersEx = mysqli_query($this->connectionlink,$getContainers);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink)>0){
						$row = mysqli_fetch_assoc($getContainersEx);
						if($row['disposition_id'] != $data['disposition_id']){
							$json['Success'] = false;
							$json['Result'] = "Container Disposition is not matched with the selected Disposition";
							return json_encode($json);
						}
						$ShippingContainerID = $row['ShippingContainerID'];
						$query1 = "select * from station_container_mapping where SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
						$q1 = mysqli_query($this->connectionlink,$query1);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {// Updating existing mapping
							$row1 = mysqli_fetch_assoc($q1);
							$query2 = "update station_container_mapping set ShippingContainerID = '".$ShippingContainerID."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where sc_map_id = '".$row1['sc_map_id']."'";
							$q2 = mysqli_query($this->connectionlink,$query2);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}

							//Start Admin Tracking
							$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('Container','Transaction','".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."','Container assigned to Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
							$q10 = mysqli_query($this->connectionlink,$query10);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							//End Admin Tracking

						}else{
							$query3 = "insert into station_container_mapping (disposition_id,SiteID,ShippingContainerID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."',NOW(),'".$_SESSION['user']['UserId']."')";
							$q3 = mysqli_query($this->connectionlink,$query3);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}

							//Start Admin Tracking
							$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('Container','Transaction','".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."','Container assigned to Station',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
							$q10 = mysqli_query($this->connectionlink,$query10);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
						}

						//Start get AssetsCount from Shipping Container
						if($row['ShippingContainerID'] != '') {
							$query11 = "select sum(Quantity) as AssetsCount from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$row['ShippingContainerID'])."'";
							$q11 = mysqli_query($this->connectionlink,$query11);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row11 = mysqli_fetch_assoc($q11);
								if($row11['AssetsCount'] >= 0) {
									if($row11['AssetsCount'] == NULL || $row11['AssetsCount'] == '') {
										$json['AssetsCount'] = 0;
									} else {
										$json['AssetsCount'] = $row11['AssetsCount'];
									}
								} else {
									$json['AssetsCount'] = 0;
								}
							} else {
								$json['AssetsCount'] = 0;
							}
						} else {
							$json['AssetsCount'] = 0;
						}
						//End get AssetsCount from Shipping Container

						$json['Success'] = true;
						$json['Result'] = 'Success';
						$json['CustomPalletID'] = '';
						$json['ShippingContainerID'] = $row['ShippingContainerID'];
						//$json['AssetsCount'] = 0;
						return json_encode($json);
					}else{
						$json['Success'] = false;
						$json['Result'] = 'Invalid Container';
						return json_encode($json);
					}
				}else{
					$json['Success'] = false;
					$json['Result'] = 'Invalid Bin/Container';
					return json_encode($json);
				}


			}


		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidatePallet ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Serial')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Serial Page';
				return json_encode($json);
			}
			$query = "select * from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['PalletFacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'Container Facility is Different from User Facility';
					return json_encode($json);
				}
				if($row['status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'Container Status is not Active';
					return json_encode($json);
				}
				if($row['Received'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'Container not Received';
					return json_encode($json);
				}
				$json['Success'] = true;
				$json['LoadId'] = $row['LoadId'];
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Container";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function CreateAsset ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Serial')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Serial Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Receive Serial')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Receive Serial Page';
				return json_encode($json);
			}

			//Start validate MPN
			$mpn_validate= $this->ValidateMPN($data['UniversalModelNumber']);
			if($mpn_validate['Success']) {
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid MPN';
				return json_encode($json);
			}
			//End validate MPN

			//Start get Pallet Details
			$query20 = "select * from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$pallet = mysqli_fetch_assoc($q20);
				if($pallet['status'] != 1) {
					$json['Success'] = false;
					$json['Result'] = 'Container Status is not Active';
					return json_encode($json);
				}
				if($pallet['Verified'] == '1') {
					$valid_seal_flag = 'Yes';
				} else {
					$valid_seal_flag = 'No';
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Container';
				return json_encode($json);
			}
			//End get Pallet Details


			//Start check If Serial Number exists for current Pallet
			//$query14 = "select count(*) from asset where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$query14 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and StatusID != '6'";
			$q14 = mysqli_query($this->connectionlink,$query14);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row14 = mysqli_fetch_assoc($q14);
				if($row14['count(*)'] > 0) {
					$json['Success'] = false;
					//$json['Result'] = 'Serial Number already exists in current Container';
					$json['Result'] = 'Serial Number already exists ';
					return json_encode($json);
				}
			}
			//End check If Serial Number exists for current Pallet

			//Start validate Custom pallet
			$query = "Select MaximumAssets,AssetsCount,StatusID,BinName from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid BIN';
				return json_encode($json);
			}
			//End validate Custom pallet
			$AssetScanID = $this->GetRandom();

			if($AssetScanID > 0) {
				//Start get APN from MPN

				//if($data['ID'] > 0) {//IF ASN Asset
				if($data['SerialNumber'] != '') {
					//$query192 = "select apn_id,part_type,UniversalModelNumber,idPallet,LoadId from asn_assets where ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."'";
					$query192 = "select apn_id,part_type,idManufacturer,UniversalModelNumber,idPallet,LoadId from asn_assets where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
					$q192 = mysqli_query($this->connectionlink,$query192);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row192 = mysqli_fetch_assoc($q192);
						$APN = $row192['apn_id'];
						$PART_TYPE = $row192['part_type'];
						$ID_MANU = $row192['idManufacturer'];
					} else {
						$APN = '';
						$PART_TYPE = '';
						$ID_MANU = '';
					}

					if($row192['UniversalModelNumber'] == $data['UniversalModelNumber']) {
						$valid_identity_flag = 'Yes';
					} else {
						$valid_identity_flag = 'No';
					}

					if($row192['idPallet'] == $data['idPallet']) {
						$valid_container_flag = 'Yes';
					} else {
						$valid_container_flag = 'No';
					}

					if($row192['LoadId'] == $data['LoadId']) {
						$valid_ticket_flag  = 'Yes';
					} else {
						$valid_ticket_flag  = 'No';
					}


					//Start get APN,PartType,Manufacturer
					$query191 = "select part_type,idManufacturer,apn_id from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
					$q191 = mysqli_query($this->connectionlink,$query191);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row191 = mysqli_fetch_assoc($q191);
						$PART_TYPE = $row191['part_type'];
						$ID_MANU = $row191['idManufacturer'];
						$APN = $row191['apn_id'];
					} else {
						$PART_TYPE = '';
						$ID_MANU = '';
						$APN = '';
					}
					//End get APN,PartType,Manufacturer


				} else {//Not from ASN
					$APN = '';
					$valid_identity_flag = 'No';
					$valid_container_flag = 'No';
					$valid_ticket_flag = 'No';
				}

				// if($APN == '' || $APN == NULL) {
				// 	//$query191 = "select apn_id from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
				// 	$query191 = "select apn_id from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
				// 	$q191 = mysqli_query($this->connectionlink,$query191);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row191 = mysqli_fetch_assoc($q191);
				// 		$APN = $row191['apn_id'];
				// 	} else {
				// 		$APN = '';
				// 	}
				// }

				// if($PART_TYPE == '' || $PART_TYPE == NULL) {
				// 	$query191 = "select part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
				// 	$q191 = mysqli_query($this->connectionlink,$query191);
				// 	if(mysqli_error($this->connectionlink)) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row191 = mysqli_fetch_assoc($q191);
				// 		$PART_TYPE = $row191['part_type'];
				// 		$ID_MANU = $row191['idManufacturer'];
				// 	} else {
				// 		$PART_TYPE = '';
				// 		$ID_MANU = '';
				// 	}
				// }
				//End get APN from MPN

				if($valid_identity_flag == 'Yes' && $valid_container_flag == 'Yes' && $valid_ticket_flag == 'Yes' && $valid_seal_flag == 'Yes') {
					$clean_receive_flag  = 'Yes';
				} else {
					$clean_receive_flag  = 'No';
				}
				$data['OriSerialNumber'] = $data['SerialNumber'];
				$data['SerialNumber'] = preg_replace('/[^A-Za-z0-9_-]/', '', $data['SerialNumber']);
				$query1 = "insert into asset (AssetScanID,idPallet,SerialNumber,Quantity,FacilityID,DateCreated,CreatedBy,StatusID,AccountID,CustomPalletID,UniversalModelNumber,SiteID,input_id,disposition_id,custom_id,receive_notes,rule_id,apn_id,part_type,idManufacturer,FirstReceivedCustomPalletID,FirstReceivedDateTime,FirstReceivedBy,valid_identity_flag,valid_container_flag,valid_ticket_flag,valid_seal_flag,clean_receive_flag,ActualSerialNumber,RecentWorkflowID,RecentWorkflowDate,RecentWorkflowBy,RecentDispositionDate,RecentDispositionRuleID,RecentDispositionBy,RecentDispositionComments) values ('".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','1','".$_SESSION['user']['FacilityID']."',NOW(),'".$_SESSION['user']['UserId']."','1','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$data['input_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['custom_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['receive_notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."','".mysqli_real_escape_string($this->connectionlink,$APN)."','".mysqli_real_escape_string($this->connectionlink,$PART_TYPE)."','".mysqli_real_escape_string($this->connectionlink,$ID_MANU)."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$valid_identity_flag)."','".mysqli_real_escape_string($this->connectionlink,$valid_container_flag)."','".mysqli_real_escape_string($this->connectionlink,$valid_ticket_flag)."','".mysqli_real_escape_string($this->connectionlink,$valid_seal_flag)."','".mysqli_real_escape_string($this->connectionlink,$clean_receive_flag)."','".$data['OriSerialNumber']."','1',NOW(),'".$_SESSION['user']['UserId']."',NOW(),'".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."','".$_SESSION['user']['UserId']."','Created in Receive Serial')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//Insert into Asset Tracking
				$desc = "Asset Created, (BIN ID : ".$row['BinName'].")";
				$query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Inserting into Asset Tracking

				//Start inserting into custompallet
				$query6 = "insert into custompallet_items (CustomPalletID,AssetScanID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."',NOW(),'1')";
				$q6 = mysqli_query($this->connectionlink,$query6);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$query9 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q9 = mysqli_query($this->connectionlink,$query9);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End inserting into Custompallet

				//Start Update ASN Record with Asset Scan ID
				//if($data['ID'] > 0) {
				if($data['SerialNumber'] != '') {
					//$query11 = "update asn_assets set AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."',AssetCreatedDate = NOW(),AssetCreatedBy = '".$_SESSION['user']['UserId']."' where ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."'";
					$query11 = "update asn_assets set AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."',AssetCreatedDate = NOW(),AssetCreatedBy = '".$_SESSION['user']['UserId']."' where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
					$q11 = mysqli_query($this->connectionlink,$query11);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				}
				//End Update ASN Record with Asset Scan ID

				//Start insert mpn apn facility summary
				$query116 = "select ReceiveID from daily_received_summary where FacilityID = '".$_SESSION['user']['FacilityID']."' and mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."' and ReceivedDate = CURDATE()";
				$q116 = mysqli_query($this->connectionlink,$query116);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row116 = mysqli_fetch_assoc($q116);
					$query117 = "update daily_received_summary set ReceivedCount = ReceivedCount + 1 where ReceiveID = '".$row116['ReceiveID']."' ";
					$q117 = mysqli_query($this->connectionlink,$query117);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				} else {
					$query117 = "insert into daily_received_summary (mpn_id,apn_id,FacilityID,ReceivedCount,ReceivedDate) values ('".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$APN)."','".$_SESSION['user']['FacilityID']."','1',CURDATE())";
					$q117 = mysqli_query($this->connectionlink,$query117);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				}
				//End insert mpn apn facility summary


				//Start get Asset Details
				$query10 = "select a.*,cp.CustomPalletID,cp.BinName,cp.AssetsCount,d.disposition,wi.input,wi.input_type from asset a
				left join custompallet cp on a.CustomPalletID = cp.CustomPalletID
				left join disposition d on a.disposition_id = d.disposition_id
				left join workflow_input wi on a.input_id = wi.input_id
				where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."'";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row10 = mysqli_fetch_assoc($q10);
					$json['Asset'] = $row10;
				}
				//End get Asset Details


				//Start check If MPN is changed
				$query = "select UniversalModelNumber,ID from asn_assets where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['UniversalModelNumber'] != $data['UniversalModelNumber']) {
						//Start get apn_id and part_type from new MPN

						$query191 = "select part_type,apn_id,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
						$q191 = mysqli_query($this->connectionlink,$query191);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row191 = mysqli_fetch_assoc($q191);
							$PART_TYPE = $row191['part_type'];
							$APN = $row191['apn_id'];
							$ID_MANU = $row191['idManufacturer'];
						} else {
							$PART_TYPE = '';
							$APN = '';
							$ID_MANU = '';
						}

						//End get apn_id and part_type from new MPN
						$query1 = "update asset set OriginalUniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."',apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."',part_type = '".mysqli_real_escape_string($this->connectionlink,$PART_TYPE)."',idManufacturer = '".mysqli_real_escape_string($this->connectionlink,$ID_MANU)."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."'";
						$q1 = mysqli_query($this->connectionlink,$query1);
					}

					$desc = "Asset MPN Changed from '".$row['UniversalModelNumber']."'(ASN) to '".$data['UniversalModelNumber']."' while Receiving";
					$query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
					$q2 = mysqli_query($this->connectionlink,$query2);
				}
				//End check IF MPN is changed

				//Start Close Container
				if($data['ColsePallet'] == '1') {
					$query16 = "update pallets set status = 3,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',WarehouseLocationId = NULL where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
					$q16 = mysqli_query($this->connectionlink,$query16);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					//Start Unlock Pallet Location
					$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$pallet['WarehouseLocationId']."'";
					$querylocold = mysqli_query($this->connectionlink,$sqllocold);
					$record_location = $this->RecordLocationHistory('Container',$data['idPallet'],$pallet['WarehouseLocationId'],'0','Container Closed');
					//End Unlock Pallet Location

					$query17 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','Container Closed','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";
					$q17 = mysqli_query($this->connectionlink,$query17);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = $query2;
						return json_encode($json);
					}
					$json['PalletClosed'] = '1';
				}
				//End Close Container

				$json['Success'] = true;
				$json['Result'] = 'Asset Created';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Error';
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPalletAssets ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Receive Serial')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Receive Serial Page';
				return json_encode($json);
			}
			$query = "select a.*,cp.CustomPalletID,cp.BinName,cp.AssetsCount,d.disposition,wi.input,wi.input_type from asset a
			left join custompallet cp on a.CustomPalletID = cp.CustomPalletID
			left join disposition d on a.disposition_id = d.disposition_id
			left join workflow_input wi on a.input_id = wi.input_id
			where a.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' order by CreatedDate desc";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$assets = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$assets[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $assets;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Assets Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetRandom() {
		while(1) {
			// generate unique random number
			$randomNumber = rand(10000000000, 99999999999);
			//$randomNumber = PRE.$randomNumber;
			$randomNumber = $randomNumber;
			// check if it exists in database
			$query = "SELECT * FROM `asset` WHERE AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$randomNumber)."'";
			$res = mysqli_query($this->connectionlink,$query);
			$rowCount = mysqli_num_rows($res);
			/*if(mysqli_affected_rows($this->connectionlink) > 0){
				$row = mysqli_fetch_assoc($res);
				$rowCount = $row['count(*)'];
			} else {
				$rowCount = 1;
			}*/
			if($rowCount < 1) {
				break;
			}
		}
		return $randomNumber;
	}

	public function RecordLocationHistory($itemtype,$itemid,$FromLocationID,$ToLocationID,$description) {
		$query = "insert into location_history (ItemType,ItemID,FromLocationID,ToLocationID,CreatedDate,CreatedBy,Description) values ('".mysqli_real_escape_string($this->connectionlink,$itemtype)."','".mysqli_real_escape_string($this->connectionlink,$itemid)."','".mysqli_real_escape_string($this->connectionlink,$FromLocationID)."','".mysqli_real_escape_string($this->connectionlink,$ToLocationID)."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$description)."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			return mysqli_error($this->connectionlink);
		} else {
			return 1;
		}
	}

	public function GenerateSubComponentSerialNumber ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
				return json_encode($json);
			}
			$SerialNumber = $this->GetRandomSerial();
			$json['Success'] = true;
			$json['Result'] = $SerialNumber;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetRandomSerial() {
		while(1) {

			$str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
			$randomNumber = substr(str_shuffle($str_result),0, 8);
			// check if it exists in database
			$query = "SELECT * FROM `inventory` WHERE SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$randomNumber)."'";
			$res = mysqli_query($this->connectionlink,$query);
			$rowCount = mysqli_num_rows($res);
			/*if(mysqli_affected_rows($this->connectionlink) > 0){
				$row = mysqli_fetch_assoc($res);
				$rowCount = $row['count(*)'];
			} else {
				$rowCount = 1;
			}*/
			if($rowCount < 1) {
				break;
			}
		}
		return $randomNumber;
	}


	public function ValidateSanitizationController ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Sanitization')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Sanitization Page';
				return json_encode($json);
			}
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['UserName'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['SanitizationController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Sanitization Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Valid';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Sanitization Controller or Password";
				return json_encode($json);
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function EmptyCustomPallet($FromCustomPalletID,$ToCustomPalletName) {
		//Start valiate ToCustomPalletName
		$query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$ToCustomPalletName)."' ";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$to_cp = mysqli_fetch_assoc($q);
			if($to_cp['AcceptAllDisposition'] != '1') {
				$json['Success'] = false;
				$json['Error'] = 'Close BIN is not All Disposition BIN';
				return $json;
			}
			if($to_cp['StatusID'] != '1') {
				$json['Success'] = false;
				$json['Error'] = 'All Disposition BIN Status is not Active';
				return $json;
			}


			//Insert into Asset Tracking
			$desc = "Asset Moved to BIN, (BIN ID : ".$to_cp['BinName'].") using Empty BIN";
            $query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy)
            select AssetScanID,'".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."' from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$FromCustomPalletID)."' ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Error'] = mysqli_error($this->connectionlink);
				return $json;
			}
			//End Inserting into Asset Tracking

			//Start update Asset
			$query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$FromCustomPalletID)."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Error'] = mysqli_error($this->connectionlink);
				return $json;
			}
			//End update Asset


			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$FromCustomPalletID)."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Error'] = mysqli_error($this->connectionlink);
				return $json;
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
            $query33 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' ";
            $q33 = mysqli_query($this->connectionlink,$query33);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Error'] = mysqli_error($this->connectionlink);
				return $json;
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row33 = mysqli_fetch_assoc($q33);
                $query3 = "UPDATE `custompallet` SET `AssetsCount`= '".mysqli_real_escape_string($this->connectionlink,$row33['count(*)'])."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
                $q3 = mysqli_query($this->connectionlink,$query3);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
					$json['Error'] = mysqli_error($this->connectionlink);
					return $json;
                }
            }

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$FromCustomPalletID)."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Error'] = mysqli_error($this->connectionlink);
				return $json;
			}
			//End update Custom Pallet Counts

			$json['Success'] = true;
			$json['Error'] = 'Empty BIN Success';
			return $json;


		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid All Disposition BIN';
			return $json;
		}

		//End validate ToCustomPalletName
	}


	public function RefreshSession ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => true,
			'Result' => true
		);
		try {
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = true;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function MoveBinToNewLocationGroup ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {

			//Start get Bin Details
			$query = "select * from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "Bin Status is not active";
					return json_encode($json);
				}
				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = "Bin Facility is different from User Facility";
					return json_encode($json);
				}

				//Start check if valid Group
				$query10 = "select GroupID,LocationType,BinTypeID,GroupName from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row10 = mysqli_fetch_assoc($q10);
					if($row10['LocationType'] != 'WIP') {
						$json['Success'] = false;
						$json['Result'] = 'Only WIP Location groups are allowed';
						return json_encode($json);
					}
					// if($row10['BinTypeID'] != $row['BinTypeID']) {
					// 	$json['Success'] = false;
					// 	$json['Result'] = 'Bin Type not matching with Location Group Bin Type';
					// 	return json_encode($json);
					// }
					$data['GroupID'] = $row10['GroupID'];
					$GroupName = $row10['GroupName'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location Group';
					return json_encode($json);
				}
				//End check if valid Group

				//Start get free location from group selected
				$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['GroupID'])."'";
				$q112 = mysqli_query($this->connectionlink,$query112);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row112 = mysqli_fetch_assoc($q112);
					$data['NewLocationID'] = $row112['LocationID'];
					$newLocationName = $row112['LocationName'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'No locations available, in selected group';
					return json_encode($json);
				}
				//End get free location from group selected


				$query2 = "update custompallet set LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$row['LocationID']."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);
				}

				$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Bin',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
				$queryloc = mysqli_query($this->connectionlink,$sqlloc);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					//return json_encode($json);
				}

				//Start Delete Bin from Station mapping
				$query2 = "DELETE from station_custompallet_mapping where  CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' and SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' ";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Delete Bin from Station mapping

				//Start Admin Tracking
				$action = "BIN removed from Station and moved to Location Group (".$data['NewLocationGroup'].")";
				$query10 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','site','SiteID','SiteName')";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Admin Tracking



				$json['newLocationName'] = $newLocationName;
				$json['GroupName'] = $GroupName;
				$json['Success'] = true;
				$json['Result'] = 'Location Group Updated';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Bin";
				return json_encode($json);
			}
			//End get Bin Details

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function SearchAIHelp ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => true,
			'Result' => true
		);
		try {

			$Search = $this->QueryAIHelp($data['Input']);
			// if($SNS_Message_cus['Success'] != true) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'Custody SNS Message Failed, Holding on Processing Media';
			// 	return json_encode($json);
			// }
			$json['Success'] = true;
			$json['Result'] = $Search['Message'];
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = true;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetPartTypesAndCOO ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {

			$query = "select parttypeid,parttype,serialized from parttype where FacilityID = '".$_SESSION['user']['FacilityID']."' and Status = '1' and serialized = 'Yes'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$PartTypes = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$PartTypes[$i] = $row;
					$i = $i + 1;
				}
				$json['PartTypes'] = $PartTypes;
			}

			$query = "select COOID,COO from COO where Status = '1' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$COOList = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$COOList[$i] = $row;
					$i = $i + 1;
				}
				$json['COOList'] = $COOList;
			}
			$json['Success'] = true;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function NLSearch ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => true,
			'Result' => true
		);
		try {

			$response = $this->NLToSqlQuery($data['Input']);
			// if($SNS_Message_cus['Success'] != true) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'Custody SNS Message Failed, Holding on Processing Media';
			// 	return json_encode($json);
			// }

			// Step 1: Decode the outer JSON
			$responseArray = json_decode($response['Message'], true);

			$responseFinalArray = json_decode($responseArray['body'], true);
			$query = $responseFinalArray['sql_query'];


			$forbiddenSQL = '/\b(INSERT|UPDATE|DELETE|TRUNCATE|DROP|ALTER|CREATE|REPLACE|MERGE|EXEC|CALL|GRANT|REVOKE|LOCK|UNLOCK)\b/i';

			// Check if the query contains any restricted SQL command
			if (preg_match($forbiddenSQL, $query, $matches)) {

				$json['Success'] = false;
				$json['Result'] = "❌ Error: Forbidden SQL command detected - " . strtoupper($matches[1]);
				return json_encode($json);
			} else {
				//echo "✅ Query is allowed.";
			}

			if (stripos(ltrim($query), "SELECT") === 0) {
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Permission to Update or Delete';
				return json_encode($json);
			}


			if($query != '') {
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink).$query;
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$result = array();
					$i = 0;
					while($row = mysqli_fetch_assoc($q)){
						$result[$i] = $row;
						$i = $i + 1;
					}

					$json['Success'] = true;
					$json['Result'] = $result;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'No Results Available';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Input';
				return json_encode($json);
			}



			$json['Success'] = true;
			$json['Result'] = $query;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = true;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function SaveRecoveryConfiguration($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		if($data['ConfigurationID'] > 0) {//Update existing configuration

			$query = "select count(*) from recover_configuration_sub_component where FacilityID = '".$_SESSION['user']['FacilityID']."' and FromDispositionID = '".mysqli_real_escape_string($this->connectionlink,$data['FromDispositionID'])."' and workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' and parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."' and input_id =  '".mysqli_real_escape_string($this->connectionlink,$data['input_id'])."' and ConfigurationID != '".mysqli_real_escape_string($this->connectionlink,$data['ConfigurationID'])."'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Combination already exists';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid';
				return json_encode($json);
			}

			$query = "update recover_configuration_sub_component set FromDispositionID = '".mysqli_real_escape_string($this->connectionlink,$data['FromDispositionID'])."',workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."',parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."',input_id = '".mysqli_real_escape_string($this->connectionlink,$data['input_id'])."',ToDispositionID = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',FacilityID = '".$_SESSION['user']['FacilityID']."',Status = '".mysqli_real_escape_string($this->connectionlink,$data['Status'])."' where ConfigurationID = '".mysqli_real_escape_string($this->connectionlink,$data['ConfigurationID'])."'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Configuration Modified';
			return json_encode($json);

		} else {//Create new configuration
			$query = "select count(*) from recover_configuration_sub_component where FacilityID = '".$_SESSION['user']['FacilityID']."' and FromDispositionID = '".mysqli_real_escape_string($this->connectionlink,$data['FromDispositionID'])."' and workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' and parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."' and input_id =  '".mysqli_real_escape_string($this->connectionlink,$data['input_id'])."' ";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Combination already exists';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid';
				return json_encode($json);
			}

			$query = "insert into recover_configuration_sub_component (FromDispositionID,workflow_id,parttypeid,input_id,ToDispositionID,CreatedDate,CreatedBy,FacilityID,Status) values ('".mysqli_real_escape_string($this->connectionlink,$data['FromDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."','".mysqli_real_escape_string($this->connectionlink,$data['input_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$data['Status'])."')";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$json['ConfigurationID'] = mysqli_insert_id($this->connectionlink);

			$json['Success'] = true;
			$json['Result'] = 'New Configuration Created';
			return json_encode($json);
		}
	}

	public function GetRecoveryConfigurationDetails($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Assets Recovered Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Assets Recovered Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Assets Recovered Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Assets Recovered Configuration Page';
			return json_encode($json);
		}

		//return json_encode($json);
		$query = "select * from recover_configuration_sub_component where ConfigurationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ConfigurationID']) . "' ";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Configuration ID";
		}
		return json_encode($json);
	}

	public function GetRecoverConfigurationList($data) {
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['ConfigurationID']
			);

			if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Assets Recovered Configuration')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Assets Recovered Configuration Page';
				return json_encode($json);
			}

			$query = "select rc.*,dt.disposition as dispositionto,d.disposition,w.workflow,p.parttype,er.input,f.FacilityName from recover_configuration_sub_component rc left join disposition d on d.disposition_id = rc.FromDispositionID left join workflow w on w.workflow_id=rc.workflow_id left join parttype p on p.parttypeid=rc.parttypeid left join workflow_input er on er.input_id=rc.input_id left join facility f on f.FacilityID=rc.FacilityID left join disposition dt on dt.disposition_id=rc.ToDispositionID where rc.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'disposition') {
							$query = $query . " AND d.disposition like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'workflow') {
							$query = $query . " AND w.workflow like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'parttype') {
							$query = $query . " AND p.parttype like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'input') {
							$query = $query . " AND er.input like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'dispositionto') {
							$query = $query . " AND dt.disposition like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

						if ($key == 'Status') {
							$query = $query . " AND rc.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'workflow') {
					$query = $query . " order by w.workflow " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'parttype') {
					$query = $query . " order by p.parttype " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'input') {
					$query = $query . " order by er.input " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'dispositionto') {
					$query = $query . " order by dt.disposition " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Status') {
					$query = $query . " order by rc.Status " . $order_by_type . " ";
				}
			} else {
				$query = $query . " order by ConfigurationID desc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Asset Configuration Available";
			}

			if ($data['skip'] == 0) {

				$query1 = "select count(*) from recover_configuration_sub_component rc left join disposition d on d.disposition_id = rc.FromDispositionID left join workflow w on w.workflow_id=rc.workflow_id left join parttype p on p.parttypeid=rc.parttypeid left join workflow_input er on er.input_id=rc.input_id left join facility f on f.FacilityID=rc.FacilityID left join disposition dt on dt.disposition_id=rc.ToDispositionID where rc.FacilityID = '".$_SESSION['user']['FacilityID']."' ";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'disposition') {
								$query1 = $query1 . " AND d.disposition like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'workflow') {
								$query1 = $query1 . " AND w.workflow like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'parttype') {
								$query1 = $query1 . " AND p.parttype like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'input') {
								$query1 = $query1 . " AND er.input like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'FacilityName') {
								$query1 = $query1 . " AND f.FacilityName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'dispositionto') {
								$query1 = $query1 . " AND dt.disposition like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

							if ($key == 'Status') {
								$query1 = $query1 . " AND rc.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function MakeDefaultInputResult($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Assets Recovered Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Assets Recovered Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Assets Recovered Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Assets Recovered Configuration Page';
			return json_encode($json);
		}

		if($data['DefaultValue'] == '0') {
			$query = "update recover_configuration_sub_component set DefaultValue = '".mysqli_real_escape_string($this->connectionlink,$data['DefaultValue'])."' where ConfigurationID = '".mysqli_real_escape_string($this->connectionlink,$data['ConfigurationID'])."'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$json['Success'] = true;
			$json['Result'] = 'Default Removed';
			return json_encode($json);
		} else {
			//$query = "update recover_configuration set DefaultValue = '0' where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and Recoverytypeid = '".mysqli_real_escape_string($this->connectionlink,$data['Recoverytypeid'])."' and parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."' ";
			$query = "update recover_configuration_sub_component set DefaultValue = '0' where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' and FromDispositionID = '".mysqli_real_escape_string($this->connectionlink,$data['FromDispositionID'])."' ";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$query = "update recover_configuration_sub_component set DefaultValue = '1' where ConfigurationID = '".mysqli_real_escape_string($this->connectionlink,$data['ConfigurationID'])."'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Default Evaluation Result added for Facility,Workflow and Part Type combination';
			return json_encode($json);
		}

		$query = "select * from recover_configuration_sub_component where FromDispositionID = '".mysqli_real_escape_string($this->connectionlink,$data['TopLevelDispositionID'])."' and workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' and parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['TopLevelparttypeid'])."' and input_id = '".mysqli_real_escape_string($this->connectionlink,$data['TopLevelInputID'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);

			//Start get disposition bin for the disposition
			$query1 = "select d.disposition_id,d.disposition,d.serialized,cp.CustomPalletID,cp.BinName,cp.AssetsCount from station_disposition_mapping sm
			left join disposition d on sm.disposition_id = d.disposition_id
			left join station_custompallet_mapping m on d.disposition_id = m.disposition_id and m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'
			left join custompallet cp on m.CustomPalletID = cp.CustomPalletID and cp.StatusID = '1'
			where d.Status = 'Active' and sm.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and sm.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$row['ToDispositionID'])."' order by d.disposition ";

			$q1 = mysqli_query($this->connectionlink, $query1);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$json['Success'] = true;
				$json['Result'] = $row1;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "To disposition not mapped to work station";
				return json_encode($json);
			}
			//End get disposition bin for the disposition
		} else {
			$json['Success'] = false;
			$json['Result'] = "Configuration not defined";
			return json_encode($json);
		}
		return json_encode($json);
	}

	public function DeleteRecoverConfiguration($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Assets Recovered Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Assets Recovered Configuration Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Assets Recovered Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Assets Recovered Configuration Page';
			return json_encode($json);
		}

		if($data['ConfigurationID']) {
			$query = "delete from recover_configuration_sub_component where ConfigurationID = '".mysqli_real_escape_string($this->connectionlink,$data['ConfigurationID'])."'";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Configuration Deleted';
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'Invalid';
			return json_encode($json);
		}
	}



	public function GetSubComponentDisposition($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		//Start get disposition from the asset
		$query = "select disposition_id from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}


		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$from_disposition = $row['disposition_id'];
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Serial";
			return json_encode($json);
		}

		//Start get to disposition
		$query1 = "select c.ToDispositionID,d.disposition,d.color_code from
		recover_configuration_sub_component c
		left join disposition d on c.ToDispositionID = d.disposition_id
		where  c.Status = 'Active' and FromDispositionID = '".mysqli_real_escape_string($this->connectionlink,$from_disposition)."' and workflow_id = '".mysqli_real_escape_string($this->connectionlink,$data['workflow_id'])."' and parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['media_parttypeid'])."' and input_id = '".mysqli_real_escape_string($this->connectionlink,$data['media_input_id'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
		$q1 = mysqli_query($this->connectionlink, $query1);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row1 = mysqli_fetch_assoc($q1);
			$disposition_id = $row1['ToDispositionID'];
			$json['sub_disposition_id'] = $row1['disposition'];
			$json['sub_disposition'] = $disposition_id;
			$json['sub_color_code'] = $row1['color_code'];

			//Start get disposition bin for the station

			$query56 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m
			left join custompallet cp on m.CustomPalletID = cp.CustomPalletID
			where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$disposition_id)."'";
			$q56 = mysqli_query($this->connectionlink,$query56);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
				$row56 = mysqli_fetch_assoc($q56);
				$json['Sub_CustomPalletID'] = $row56['CustomPalletID'];
				$json['Sub_BinName'] = $row56['BinName'];
			} else {
				$json['Sub_CustomPalletID'] = '';
				$json['Sub_BinName'] = '';
			}

			$json['Success'] = true;
			$json['Result'] = 'OK';
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = "Configuration not defined";
			return json_encode($json);
		}
	}


}
?>