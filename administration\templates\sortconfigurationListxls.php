<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['sortconfigurationListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "SortConfigurationList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
//$obj1 =  new Connection();
//$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect();
$datatoday = array('Generated Date',$today);
$datahead = array('Sort Configuration List');
$header = array('Facility','Work Station Group','Disposition','MPN','SPEC','COO','Bin','Location','Quantity');

$sql = "select sc.*,F.FacilityName,wc.GroupName,d.disposition,c.BinName,c.LocationID,c.MaximumAssets,l.LocationName,sc.part_spec_id,sc.COO,sc.SortCriteriaID from sortconfiguration sc
left join facility F on sc.FacilityID = F.FacilityID
left join WorkstationConfiguration wc on wc.GroupID = sc.GroupID
left join disposition d on sc.disposition_id = d.disposition_id
left join custompallet c on sc.CustomPalletID = c.CustomPalletID
left join location l on c.LocationID = l.LocationID
where sc.AccountID = '" . $_SESSION['user']['AccountID'] . "' AND sc.FacilityID = '" . $_SESSION['user']['FacilityID'] . "' and sc.Status='".$data['Status']."'";
if(count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                        if ($key == 'FacilityName') {
                            $sql = $sql . " AND F.FacilityName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'GroupName') {
                            $sql = $sql . " AND wc.GroupName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'disposition') {
                            $sql = $sql . " AND d.disposition like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'MPNRequired') {
                            $sql = $sql . " AND sc.MPNRequired like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'mpn_id') {
                            $sql = $sql . " AND sc.mpn_id like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'part_spec_id') {
                            $sql = $sql . " AND sc.part_spec_id like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'COO') {
                            $sql = $sql . " AND sc.COO like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'LocationName') {
                            $sql = $sql . " AND l.LocationName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'BinName') {
                            $sql = $sql . " AND c.BinName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }
                        if ($key == 'MaximumAssets') {
                            $sql = $sql . " AND c.MaximumAssets like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                        }


                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }
            if ($data['OrderBy'] == 'FacilityName') {
                $sql = $sql . " order by F.FacilityName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'GroupName') {
                $sql = $sql . " order by wc.GroupName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'disposition') {
                $sql = $sql . " order by d.disposition " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'MPNRequired') {
                $sql = $sql . " order by sc.MPNRequired " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'mpn_id') {
                $sql = $sql . " order by sc.mpn_id " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'part_spec_id') {
                $sql = $sql . " order by sc.part_spec_id " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'COO') {
                $sql = $sql . " order by sc.COO " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'BinName') {
                $sql = $sql . " order by c.BinName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'LocationName') {
                $sql = $sql . " order by l.LocationName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'MaximumAssets') {
                $sql = $sql . " order by c.MaximumAssets " . $order_by_type . " ";
            }
        } else {
            $sql = $sql . " order by sc.sortconfigurationid desc ";
        }
    //echo $sql;exit;
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);
}
            while($row = mysqli_fetch_assoc($query))
            {
                $row2  = array($row['FacilityName'],$row['GroupName'],$row['disposition'],$row['mpn_id'],$row['part_spec_id'],$row['COO'],$row['BinName'],$row['LocationName'],$row['MaximumAssets']);
                $rows[] = $row2;
            }

$sheet_name = 'Sort Configuration List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?>
