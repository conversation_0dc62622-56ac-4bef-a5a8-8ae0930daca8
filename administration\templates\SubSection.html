<div class="row page" data-ng-controller="SubSection">
    <div class="col-md-12">
        <article class="article">
            <form name="material_signup_form" class="form-validation" >
                <md-card class="no-margin-h">
                    
                    <md-toolbar class="md-table-toolbar md-default">
                        <div class="md-toolbar-tools">
                            <span>Sub Section</span>
                            <div flex></div>
                            <a href="#!/SubSection" class="md-button md-raised btn-w-md" style="display: flex;">
                                New Sub Section
                            </a>
                        </div>
                    </md-toolbar>
                                    
                    <div class="col-md-4 col-md-offset-4">
                        <fieldset>                        
                            <md-input-container class="md-block">                                          
                                <label>Jabil Site</label>
                                <md-select name="CustomerID" ng-model="benefit.CustomerID" required aria-label="select" ng-disabled="benefit.SubSectionID" ng-change="GetCustomerApplications()">
                                    <md-option ng-repeat="cus in Customer" value="{{cus.CustomerID}}"> {{cus.CustomerName}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.CustomerID.$error" multiple ng-if='material_signup_form.CustomerID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Application</label>
                                <md-select name="ApplicationID" ng-model="benefit.ApplicationID" required aria-label="select" ng-disabled="benefit.SubSectionID" ng-change="GetApplicationModules()">
                                    <md-option ng-repeat="app in Applications" value="{{app.ApplicationID}}"> {{app.ApplicationName}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.ApplicationID.$error" multiple ng-if='material_signup_form.ApplicationID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">                                          
                                <label>Module</label>
                                <md-select name="ModuleID" ng-model="benefit.ModuleID" required aria-label="select" ng-disabled="benefit.SubSectionID" ng-change="GetModuleSections()">
                                    <md-option ng-repeat="mod in Modules" value="{{mod.ModuleID}}"> {{mod.ModuleName}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.ModuleID.$error" multiple ng-if='material_signup_form.ModuleID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">                                          
                                <label>Section</label>
                                <md-select name="SectionID" ng-model="benefit.SectionID" required aria-label="select" ng-disabled="benefit.SubSectionID" >
                                    <md-option ng-repeat="sec in Sections" value="{{sec.SectionID}}"> {{sec.SectionName}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.SectionID.$error" multiple ng-if='material_signup_form.SectionID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Sub Section Name</label>
                                <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                <input type="text" name="SubSectionName"  ng-model="benefit['SubSectionName']"  required ng-maxlength="100" />
                                <div ng-messages="material_signup_form.SubSectionName.$error" multiple ng-if='material_signup_form.SubSectionName.$dirty'>                            
                                    <div ng-message="required">This is required.</div> 
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 100.</div>                           
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Description</label>
                                <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                <textarea name="SubSectionDescription" ng-minlength="3"  ng-model="benefit['SubSectionDescription']"  required ng-maxlength="1000" ></textarea>
                                
                                <div ng-messages="material_signup_form.SubSectionDescription.$error" multiple ng-if='material_signup_form.SubSectionDescription.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 1000.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">                                            
                                <label>Status</label>
                                <md-select name="Status" ng-model="benefit.Status" required aria-label="select">
                                    <md-option value="Active"> Active </md-option>
                                    <md-option value="In active"> In active </md-option>
                                </md-select>   
                                <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                    <div ng-message="required">This is required.</div>                                           
                                </div>                                             
                            </md-input-container>
                                                        
                            <div class="col-md-12 btns-row">
                                <md-button 
                                class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || benefit.busy" ng-click="CreateSubSection('Submit')">
                                    <span ng-show="! benefit.busy">Submit</span>
                                    <span ng-show="benefit.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>
                            
                        </fieldset>
                    </div>
                </md-card>                
            </form>            
        </article>                            
    </div>

</div>