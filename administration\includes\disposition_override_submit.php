<?php
	session_start();
	include_once("../database/disposition_override.class.php");
	$obj = new OverrideClass();
	
	if($_POST['ajax'] == "GetAllDispositions") {
		$result = $obj->GetAllDispositions($_POST);
		echo $result;
	} 
    
    if($_POST['ajax'] == "UpdateSerialDisposition") {
		$result = $obj->UpdateSerialDisposition($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateBulkDisposition") {
		$result = $obj->UpdateBulkDisposition($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBusinessRuleNames") {
		$result = $obj->GetBusinessRuleNames($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetLoggedinUserDetails") {
		$result = $obj->GetLoggedinUserDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CheckDispositionOverrideEligibility") {
		$result = $obj->CheckDispositionOverrideEligibility($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateBinDisposition") {
		$result = $obj->ValidateBinDisposition($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetDispositionOverrideReasons") {
		$result = $obj->GetDispositionOverrideReasons($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateSerial") {
		$result = $obj->ValidateSerial($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateDispositionOverride") {
		$result = $obj->CreateDispositionOverride($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteOverrideSerial") {
		$result = $obj->DeleteOverrideSerial($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CompleteDispositionOverrideAssets") {
		$result = $obj->CompleteDispositionOverrideAssets($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateFromBinDisposition") {
		$result = $obj->ValidateFromBinDisposition($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CompleteDispositionOverrideBulk") {
		$result = $obj->CompleteDispositionOverrideBulk($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateShippingContainerDisposition") {
		$result = $obj->ValidateShippingContainerDisposition($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CompleteDispositionOverrideContainer") {
		$result = $obj->CompleteDispositionOverrideContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateContainerSeal") {
		$result = $obj->ValidateContainerSeal($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllUserInputs") {
		$result = $obj->GetAllUserInputs($_POST);
		echo $result;
	}
?>