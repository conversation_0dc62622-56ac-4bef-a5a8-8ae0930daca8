<style>
  /* Additional styling to ensure the search input is usable */
   .md-select-header input.md-input {
       width: 100%;
       padding: 8px;
       margin: 0;
       box-sizing: border-box;
   }
   /*
   .selectdemoSelectHeader {
      Please note: All these selectors are only applied to children of elements with the 'selectdemoSelectHeader' class
   }*/
   .selectdemoSelectHeader .demo-header-searchbox {
     border: none;
     outline: none;
     height: 100%;
     width: 100%;
     padding: 0;
   }
   .selectdemoSelectHeader .demo-select-header {
     box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(0, 0, 0, 0.14), 0 0 0 0 rgba(0, 0, 0, 0.12);
     padding-left: 16px;
     height: 40px;
     cursor: pointer;
     position: relative;
     display: flex;
     width: auto;
   }
   .selectdemoSelectHeader md-content._md {
     max-height: 240px;
   }
   .selectdemoSelectHeader md-input-container {
     min-width: 112px;
   }
</style>
<div class="row page" data-ng-controller="SortConfiguration">
    <div class="col-md-12">
        <article class="article">
            <style>
                md-tabs.md-default-theme.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]).md-active, md-tabs.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]).md-active, md-tabs.md-default-theme.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]).md-active md-icon, md-tabs.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]).md-active md-icon, md-tabs.md-default-theme.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]).md-focused, md-tabs.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]).md-focused, md-tabs.md-default-theme.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]).md-focused md-icon, md-tabs.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]).md-focused md-icon
                {
                    background-color: #76c444;
                    border-radius: 14px 14px 0px 0px;
                }
                .tabs_section md-pagination-wrapper{
                    left: 16px;
                    top: 4px;
                }

                .tabs_section .md-tab {
                    padding: 9px 24px;
                }
                .tabs_section md-tabs.md-primary>md-tabs-wrapper {
                    background-color: #f4f4f4;
                }
                .tabs_section md-tabs[md-border-bottom] md-tabs-wrapper {
                    border-color: #6fc546;
                }
                md-tabs.md-default-theme.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]), md-tabs.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]), md-tabs.md-default-theme.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]) md-icon, md-tabs.md-primary>md-tabs-wrapper>md-tabs-canvas>md-pagination-wrapper>md-tab-item:not([disabled]) md-icon
                {
                    color:#222b2f;
                }
            </style>

            <script type="text/ng-template" id="progressDialog.tmpl.html">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <div class="form-horizontal verification-form">
                          <h2 class="md-title">Processing...</h2>
                          <p>Time remaining: {{timeRemaining}} seconds</p>
                          <md-progress-linear md-mode="determinate" value="{{progress}}"></md-progress-linear>
                        </div>
                    </div>
                </div>
              </script>

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Sort Configuration</span>
                        <div flex></div>
                        <!--  <div class="upload-btn-wrapper text-center mt-5">
                            <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                            <input type="file" ng-file-select="onFileSelect($files)" id="SortConfigurationFile">  
                            <a href="../../sample_files/upload_SortConfiguration_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                        </div>  -->
                            <!-- <a href="#!/ManufacturerList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a> -->
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="sortconfiguration.FacilityID" required ng-disabled="true">
                                        <md-option ng-repeat="facility in Facility | filter:{ FacilityStatus : '!InActive'}" value="{{facility.FacilityID}}"> {{facility.FacilityName}} </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Work Station Group</label>
                                    <md-select name="GroupID" ng-model="sortconfiguration.GroupID" required aria-label="select" ng-change="GetSortDisposition()">
                                        <md-option ng-repeat="group in Groups" value="{{group.GroupID}}"> {{group.GroupName}} </md-option>

                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="material_signup_form.GroupID.$error" multiple ng-if='material_signup_form.GroupID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Disposition</label>
                                    <md-select name="disposition_id" ng-model="sortconfiguration.disposition_id" required aria-label="select" ng-change="SelectSortBin(true)">
                                        <md-option ng-repeat="disposition in dispositions" value="{{disposition.disposition_id}}"> {{disposition.disposition}} </md-option>
                                    </md-select>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Sort Validation Key</label>
                                    <md-select name="SortCriteriaID" ng-model="sortconfiguration.SortCriteriaID" ng-change="CriteriaChange()" required >
                                        <md-option ng-repeat="criteria in SortCriteria" value="{{criteria.SortCriteriaID}}"> {{criteria.CriteriaName}} </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.SortCriteriaID.$error" multiple ng-if='material_signup_form.SortCriteriaID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!--<div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>MPN Required</label>
                                    <md-select name="MPNRequired" ng-model="sortconfiguration.MPNRequired" required aria-label="select">
                                        <md-option value="Yes"> Yes </md-option>
                                        <md-option value="No"> No </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="material_signup_form.MPNRequired.$error" multiple ng-if='material_signup_form.MPNRequired.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>-->

                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>MPN</label>
                                    <input type="text" name="mpn_id"  ng-model="sortconfiguration['mpn_id']"  required ng-maxlength="100" />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.mpn_id.$error" multiple ng-if='material_signup_form.mpn_id.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div> -->
                            <div class="col-md-3">
                                <md-input-container class="md-block mb-20" ng-init="mpnCheckboxDisabled=false">
                                    <md-checkbox ng-model="sortconfiguration.Allmpn_id" ng-true-value="1" ng-false-value="0" style="margin-bottom: 6px;" ng-change="CPMPNChanged()" ng-disabled="sortconfiguration.mpnCheckboxDisabled">All MPN</md-checkbox>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block" ng-init="mpnInputDisabled=false">
                                    <label>MPN</label>
                                    <input type="text" name="mpn_id" ng-model="sortconfiguration['mpn_id']" ng-disabled="sortconfiguration.mpn_id == 'All' || mpnInputDisabled" ng-change="sortconfiguration['mpn_id'] = (sortconfiguration['mpn_id'] | uppercase);changeMPN();" ng-blur="GetPartTypeByMPN();" required>
                                    <div class="error-sapce">
                                        <!-- <p>Enter MPNs seperated with coma (,)</p> -->
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block mb-20">
                                    <md-checkbox ng-model="sortconfiguration.Allspec_id" ng-true-value="1" ng-false-value="0" style="margin-bottom: 6px;" ng-change="SpecChanged()" ng-disabled="sortconfiguration.SPECCheckboxDisabled">All SPEC</md-checkbox>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>SPEC</label>
                                    <md-select name="part_spec_id" ng-model="sortconfiguration.part_spec_id" required ng-disabled="sortconfiguration.part_spec_id == 'All'" ng-change="changeSPEC();">
                                        <md-option value="All"> All </md-option>
                                        <md-option ng-repeat="spec in SPECList" value="{{spec.part_spec_id}}"> {{spec.part_spec_id}} </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.part_spec_id.$error" multiple ng-if='material_signup_form.part_spec_id.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div style="clear: both;"></div>

                            <div class="col-md-3">
                                <md-input-container class="md-block mb-20">
                                    <md-checkbox ng-model="sortconfiguration.Allcoo_id" ng-true-value="1" ng-false-value="0" style="margin-bottom: 6px;" ng-change="COOChanged()" ng-disabled="sortconfiguration.COOCheckboxDisabled">All COO</md-checkbox>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>COO</label>
                                    <md-select name="COO" ng-model="sortconfiguration.COO" required ng-disabled="sortconfiguration.COO == 'All'">
                                        <md-option value="All"> All </md-option>
                                        <md-option ng-repeat="coo in COOList" value="{{coo.COO}}"> {{coo.COO}} </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.COO.$error" multiple ng-if='material_signup_form.COO.$dirty'>
                                            <div ng-message="required"> This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Part Type</label>
                                    <md-select name="parttypeid" ng-model="sortconfiguration.parttypeid" ng-change="ValidateParttypeMPN();">
                                        <md-option ng-repeat="parttype in PartTypelist" value="{{parttype.parttypeid}}"> {{parttype.parttype}} </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.parttypeid.$error" multiple ng-if='material_signup_form.parttypeid.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="MPNValidation">The MPN, Part Type are Invalid </div>
                                            <div ng-message="MPNMandate">Please enter MPN</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Bin</label>
                                    <md-select name="CustomPalletID" ng-model="sortconfiguration.CustomPalletID"
                                                 md-on-close="clearBinSearchTerm()"
                                                 md-container-class="selectdemoSelectHeader"
                                                 ng-change="GetSortLocation()"
                                                 required>
                                          <md-select-header class="demo-select-header">
                                            <input ng-model="binSearchTerm" aria-label="bin filter"
                                                   type="search" placeholder="Search for an item..."
                                                   class="demo-header-searchbox md-text" ng-keydown="$event.stopPropagation()">
                                             <md-button class="md-icon-button clear-button" ng-click="deselectBin()" aria-label="Clear Filter">
                                                 <md-icon>Clear</md-icon>
                                             </md-button>
                                          </md-select-header>
                                          <md-optgroup label="bin">
                                            <md-option ng-value="{{CustomPallet.CustomPalletID}}" ng-repeat="CustomPallet in CustomPallets | filter:binSearchTerm">
                                              {{CustomPallet.BinName}}
                                            </md-option>
                                          </md-optgroup>
                                      </md-select>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div> -->
                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Location</label>
                                    <input type="text" name="LocationName" ng-model="sortconfiguration.LocationName" ng-min="1" ng-max="100" disabled required/>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.LocationName.$error" multiple ng-if='material_signup_form.LocationName.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="min">Minimum 1.</div>
                                            <div ng-message="max">Maximul 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div> -->


                           <!--  <div style="clear: both;"></div> -->

                            <!-- <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Maximum Assets</label>
                                    <input type="text" name="MaximumAssets" ng-model="sortconfiguration.MaximumAssets" ng-min="1" ng-max="100" disabled />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.MaximumAssets.$error" multiple ng-if='material_signup_form.MaximumAssets.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="min">Minimum 1.</div>
                                            <div ng-message="max">Maximul 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div> -->

                            <div class="col-md-3" >
                                <div class="autocomplete insideuse">
                                     <md-input-container class="md-block">
                                         <md-autocomplete flex  required style="margin-bottom:0px !important; margin-top:0px !important; padding-top: 0px !important;"
                                             md-input-name="group"
                                             md-input-maxlength="100"
                                             md-no-cache="noCache"
                                             md-search-text-change="LocationChange(sortconfiguration.group)"
                                             md-search-text="sortconfiguration.group"
                                             md-items="item in queryLocationSearch(sortconfiguration.group)"
                                             md-item-text="item.GroupName"
                                             md-selected-item-change="selectedLocationChange(item)"
                                             md-min-length="0"
                                             ng-model-options='{ debounce: 1000 }'
                                             placeholder="Location Group">
                                             <md-item-template>
                                                 <span md-highlight-text="sortconfiguration.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                             </md-item-template>
                                             <md-not-found>
                                                 No Records matching "{{sortconfiguration.group}}" were found.
                                             </md-not-found>
                                             <div ng-messages="material_signup_form.group.$error" ng-if="material_signup_form.group.$touched">
                                                 <div ng-message="required">No Records matching.</div>
                                             </div>
                                         </md-autocomplete>
                                     </md-input-container>

                                 </div>
                             </div>
                             <div class="col-md-5">
                                <md-input-container class="md-block">
                                    <label>Bin</label>
                                   <input type="text" name="BinName" ng-model="sortconfiguration.BinName" ng-min="1" ng-max="100" ng-change="SelectSortManualBin()" required/>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.BinName.$error" multiple ng-if='material_signup_form.BinName.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="min">Minimum 1.</div>
                                            <div ng-message="max">Maximul 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-1" ng-show="sortconfiguration.sortconfigurationid">
                                <i style="font-size: 25px; margin-top: 18px;" class="material-icons text-success" ng-click="showAdvancedDiForm($event,sortconfiguration)">swap_horiz</i>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Maximum Assets</label>
                                    <input type="text" name="MaximumAssets" ng-model="sortconfiguration.MaximumAssets" ng-min="1" ng-max="100" disabled />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.MaximumAssets.$error" multiple ng-if='material_signup_form.MaximumAssets.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="min">Minimum 1.</div>
                                            <div ng-message="max">Maximul 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>


                            <div class="col-md-12 btns-row">
                                <md-button class="md-button md-raised btn-w-md md-default"
                                ng-click="CancelConfiguration()">
                                        Cancel
                                    </md-button>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="SortConfigurationSave()">
                                <span ng-show="! sortconfiguration.busy">Save</span>
                                <span ng-show="sortconfiguration.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>

            <md-card class="no-margin-h pt-0">
                <md-toolbar class="md-table-toolbar md-default" ng-init="sortconfigurationList = true;">
                    <div class="md-toolbar-tools" style="cursor: pointer;">

                        <i ng-click="sortconfigurationList = !sortconfigurationList" class="material-icons md-primary" ng-show="sortconfigurationList">keyboard_arrow_up</i>
                        <i ng-click="sortconfigurationList = !sortconfigurationList" class="material-icons md-primary" ng-show="! sortconfigurationList">keyboard_arrow_down</i>
                        <span ng-click="sortconfigurationList = !sortconfigurationList">Sort Configuration List</span>
                        <div flex></div>
                        <!--<md-button class="md-fab md-raised md-mini md-primary" aria-label="Refresh">
                            <i class="material-icons" style="padding-top: 2px;">refresh</i>
                        </md-button>-->
                    </div>
                </md-toolbar>

                <div class="callout callout-info" ng-show="!busy && pagedItems1.length == 0">
                    <p>No Sort Configuration Available </p>
                </div>

                <div class="row"  ng-show="sortconfigurationList">

                    <div class="col-md-12 tabs_section" style="margin-top: 10px;">
                        <div>
                            <md-content>
                            <md-tabs md-dynamic-height md-border-bottom class="md-primary">

                                <md-tab label="Active list">
                                    <md-content class="md-padding">
                                        <!--<h5><strong>Active list</strong></h5>-->
                                        <p>
                                        <!--Active List Start-->
                                            <div class="row">
                                                <div class="col-md-12">

                                                    <a href="#!/SortConfiguration" ng-click="sortconfigurationListxls1()" class="md-button md-raised btn-w-md md-default pull-right" style="display: flex; margin-top: -10px;">
                                                        <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                                    </a>
                                                    <div ng-show="pagedItems1" class="pull-right mr-5">
                                                            <small>
                                                            Showing Results <span style="font-weight:bold;">{{(currentPage1 * itemsPerPage1) + 1}}</span>
                                                            to <span style="font-weight:bold;" ng-show="total1 >= (currentPage1 * itemsPerPage1) + itemsPerPage1">{{(currentPage1 * itemsPerPage1) + itemsPerPage1}}</span>
                                                                <span style="font-weight:bold;" ng-show="total1 < (currentPage1 * itemsPerPage1) + itemsPerPage1">{{total1}}</span>
                                                            of <span style="font-weight:bold;">{{total1}}</span>
                                                            </small>
                                                    </div>

                                                    <div style="clear:both;"></div>
                                                    <div class="table-responsive" style="overflow: auto;">
                                                        <table class="table table-striped mb-0">
                                                            <thead>

                                                                <tr class="th_sorting">
                                                                    <th style="min-width: 40px;">Edit</th>

                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy1('FacilityName')" ng-class="{'orderby' : OrderBy1 == 'FacilityName'}">
                                                                        <div style="min-width: 80px;">
                                                                            Facility<i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'FacilityName'"></i>
                                                                            <span ng-show="OrderBy1 == 'FacilityName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>

                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy1('GroupName')" ng-class="{'orderby' : OrderBy1 == 'GroupName'}">
                                                                        <div style="min-width: 130px;">
                                                                            Work Station<i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'GroupName'"></i>
                                                                            <span ng-show="OrderBy1 == 'GroupName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>

                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy1('disposition')" ng-class="{'orderby' : OrderBy1 == 'disposition'}">
                                                                        <div>
                                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'disposition'"></i>
                                                                            <span ng-show="OrderBy1 == 'disposition'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CriteriaName')" ng-class="{'orderby' : OrderBy == 'CriteriaName'}">
                                                                        <div>
                                                                            Sort Key <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CriteriaName'"></i>
                                                                            <span ng-show="OrderBy == 'CriteriaName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy1('mpn_id')" ng-class="{'orderby' : OrderBy1 == 'mpn_id'}">
                                                                        <div>
                                                                            MPN <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'mpn_id'"></i>
                                                                            <span ng-show="OrderBy1 == 'mpn_id'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy1('part_spec_id')" ng-class="{'orderby' : OrderBy1 == 'part_spec_id'}">
                                                                        <div>
                                                                            SPEC <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'part_spec_id'"></i>
                                                                            <span ng-show="OrderBy1 == 'part_spec_id'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy1('COO')" ng-class="{'orderby' : OrderBy1 == 'COO'}">
                                                                        <div>
                                                                            COO <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'COO'"></i>
                                                                            <span ng-show="OrderBy1 == 'COO'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy1('LocationName')" ng-class="{'orderby' : OrderBy1 == 'LocationName'}">
                                                                        <div style="min-width: 100px;">
                                                                            Location <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'LocationName'"></i>
                                                                            <span ng-show="OrderBy1 == 'LocationName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy1('BinName')" ng-class="{'orderby' : OrderBy1 == 'BinName'}">
                                                                        <div>
                                                                            Bin <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'BinName'"></i>
                                                                            <span ng-show="OrderBy1 == 'BinName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="min-width: 90px;text-align: center;">Move Bin</th>

                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy1('MaximumAssets')" ng-class="{'orderby' : OrderBy1 == 'MaximumAssets'}">
                                                                        <div style="min-width: 70px;">
                                                                            Qty <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'MaximumAssets'"></i>
                                                                            <span ng-show="OrderBy1 == 'MaximumAssets'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="text-align: center;">Archive</th>
                                                                    <th style="text-align: center;">Delete</th>


                                                                </tr>

                                                                <tr class="errornone">
                                                                    <td>&nbsp;</td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="FacilityName" ng-model="filter_text1[0].FacilityName" ng-change="MakeFilter1()"  aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="GroupName" ng-model="filter_text1[0].GroupName" ng-change="MakeFilter1()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="disposition" ng-model="filter_text1[0].disposition" ng-change="MakeFilter1()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="CriteriaName" ng-model="filter_text1[0].CriteriaName" ng-change="MakeFilter1()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>

                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="mpn_id" ng-model="filter_text1[0].mpn_id" ng-change="MakeFilter1()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                    <md-input-container class="md-block mt-0">
                                                                        <input type="text" name="part_spec_id" ng-model="filter_text1[0].part_spec_id" ng-change="MakeFilter1()" aria-label="text" />
                                                                    </md-input-container>
                                                                </td>
                                                                <td>
                                                                    <md-input-container class="md-block mt-0">
                                                                        <input type="text" name="COO" ng-model="filter_text1[0].COO" ng-change="MakeFilter1()" aria-label="text" />
                                                                    </md-input-container>
                                                                </td>
                                                                    
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="LocationName" ng-model="filter_text1[0].LocationName" ng-change="MakeFilter1()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="BinName" ng-model="filter_text1[0].BinName" ng-change="MakeFilter1()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>&nbsp;</td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="MaximumAssets" ng-model="filter_text1[0].MaximumAssets" ng-change="MakeFilter1()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>&nbsp;</td>
                                                                    <td>&nbsp;</td>

                                                                </tr>
                                                            </thead>

                                                            <tbody ng-show="pagedItems1.length > 0">
                                                                <tr ng-repeat="product in pagedItems1">
                                                                    <td class="action-icons" style="min-width: 40px;">
                                                                        <span ng-click="EditSortConfiguration(product,$event)"><i class="material-icons text-danger edit">edit</i></span>
                                                                    </td>
                                                                    <td>
                                                                        {{product.FacilityName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.GroupName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.disposition}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.CriteriaName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.mpn_id}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.part_spec_id}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.COO}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.LocationName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.BinName}}
                                                                    </td>
                                                                     <td style="text-align: center;">
                                                                        <i style="font-size: 25px;" class="material-icons text-success" ng-click="showAdvancedDi($event,product)">swap_horiz</i>
                                                                    </td>
                                                                    <td>
                                                                        {{product.MaximumAssets}}
                                                                    </td>
                                                                    <td style="text-align: center;">
                                                                        <i class="material-icons text-success" ng-click="Archivesortconfiguration(product,$event)">archive</i>
                                                                    </td>
                                                                    <td style="text-align: center;">
                                                                        <i class="material-icons text-danger" ng-click="Deletesortconfiguration(product,$event)">delete_forever</i>
                                                                    </td>
                                                                </tr>
                                                            </tbody>

                                                            <tfoot>
                                                                <tr>
                                                                    <td colspan="9">
                                                                        <div>
                                                                            <ul class="pagination">
                                                                                <li ng-class="prevPageDisabled(1)">
                                                                                    <a href ng-click="firstPage(1)"><< First</a>
                                                                                </li>
                                                                                <li ng-class="prevPageDisabled(1)">
                                                                                    <a href ng-click="prevPage(1)"><< Prev</a>
                                                                                </li>
                                                                                <li ng-repeat="n in range(1)" ng-class="{active: n == currentPage1}" ng-click="setPage(n,1)" ng-show="n >= 0">
                                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                                </li>
                                                                                <li ng-class="nextPageDisabled(1)">
                                                                                    <a href ng-click="nextPage(1)">Next >></a>
                                                                                </li>
                                                                                <li ng-class="nextPageDisabled(1)">
                                                                                    <a href ng-click="lastPage(1)">Last >></a>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </tfoot>

                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--Active List Close-->
                                        </p>
                                    </md-content>
                                </md-tab>

                                <md-tab label="Archive List">
                                    <md-content class="md-padding">
                                        <!--<h5><strong>Archive list</strong></h5>-->
                                        <p>
                                        <!--Archive List Start-->
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <a href="#!/SortConfiguration" ng-click="sortconfigurationListxls2()" class="md-button md-raised btn-w-md md-default pull-right" style="display: flex; margin-top: -10px;">
                                                        <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                                    </a>
                                                    <div ng-show="pagedItems2" class="pull-right mr-5">
                                                            <small>
                                                            Showing Results <span style="font-weight:bold;">{{(currentPage2 * itemsPerPage2) + 1}}</span>
                                                            to <span style="font-weight:bold;" ng-show="total2 >= (currentPage2 * itemsPerPage2) + itemsPerPage2">{{(currentPage2 * itemsPerPage2) + itemsPerPage2}}</span>
                                                                <span style="font-weight:bold;" ng-show="total2 < (currentPage2 * itemsPerPage2) + itemsPerPage2">{{total2}}</span>
                                                            of <span style="font-weight:bold;">{{total2}}</span>
                                                            </small>
                                                    </div>
                                                    <div style="clear:both;"></div>
                                                    <div class="table-responsive" style="overflow: auto;">
                                                        <table class="table table-striped mb-0">
                                                            <thead>

                                                                <tr class="th_sorting">
                                                                    <th style="min-width: 40px;">Edit</th>

                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('FacilityName')" ng-class="{'orderby' : OrderBy2 == 'FacilityName'}">
                                                                        <div style="min-width: 80px;">
                                                                            Facility<i class="fa fa-sort pull-right" ng-show="OrderBy2 != 'FacilityName'"></i>
                                                                            <span ng-show="OrderBy2 == 'FacilityName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>

                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('GroupName')" ng-class="{'orderby' : OrderBy2 == 'GroupName'}">
                                                                        <div style="min-width: 130px;">
                                                                            Work Station<i class="fa fa-sort pull-right" ng-show="OrderBy2 != 'GroupName'"></i>
                                                                            <span ng-show="OrderBy2 == 'GroupName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>

                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('disposition')" ng-class="{'orderby' : OrderBy2 == 'disposition'}">
                                                                        <div>
                                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy2 != 'disposition'"></i>
                                                                            <span ng-show="OrderBy2 == 'disposition'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CriteriaName')" ng-class="{'orderby' : OrderBy == 'CriteriaName'}">
                                                                        <div>
                                                                            Sort Key <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CriteriaName'"></i>
                                                                            <span ng-show="OrderBy == 'CriteriaName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('mpn_id')" ng-class="{'orderby' : OrderBy2 == 'mpn_id'}">
                                                                        <div>
                                                                            MPN <i class="fa fa-sort pull-right" ng-show="OrderBy2 != 'mpn_id'"></i>
                                                                            <span ng-show="OrderBy2 == 'mpn_id'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('part_spec_id')" ng-class="{'orderby' : OrderBy2 == 'part_spec_id'}">
                                                                        <div>
                                                                            SPEC <i class="fa fa-sort pull-right" ng-show="OrderBy2 != 'part_spec_id'"></i>
                                                                            <span ng-show="OrderBy2 == 'part_spec_id'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('COO')" ng-class="{'orderby' : OrderBy2 == 'COO'}">
                                                                        <div>
                                                                            COO <i class="fa fa-sort pull-right" ng-show="OrderBy2 != 'COO'"></i>
                                                                            <span ng-show="OrderBy2 == 'COO'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('BinName')" ng-class="{'orderby' : OrderBy2 == 'BinName'}">
                                                                        <div>
                                                                            Bin <i class="fa fa-sort pull-right" ng-show="MakeOrderBy2 != 'BinName'"></i>
                                                                            <span ng-show="OrderBy2 == 'BinName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('LocationName')" ng-class="{'orderby' : OrderBy2 == 'LocationName'}">
                                                                        <div style="min-width: 100px;">
                                                                            Location <i class="fa fa-sort pull-right" ng-show="OrderBy2 != 'LocationName'"></i>
                                                                            <span ng-show="OrderBy2 == 'LocationName'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>

                                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy2('MaximumAssets')" ng-class="{'orderby' : OrderBy2 == 'MaximumAssets'}">
                                                                        <div style="min-width: 70px;">
                                                                            Qty <i class="fa fa-sort pull-right" ng-show="OrderBy2 != 'MaximumAssets'"></i>
                                                                            <span ng-show="OrderBy2 == 'MaximumAssets'">
                                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType2 == 'asc'"></i>
                                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType2 == 'desc'"></i>
                                                                            </span>
                                                                        </div>
                                                                    </th>
                                                                    <th style=" min-width: 120px;">Active</th>
                                                                    <th style="text-align: center;">Delete</th>


                                                                </tr>

                                                                <tr class="errornone">
                                                                    <td>&nbsp;</td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="FacilityName" ng-model="filter_text2[0].FacilityName" ng-change="MakeFilter2()"  aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="GroupName" ng-model="filter_text2[0].GroupName" ng-change="MakeFilter2()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="disposition" ng-model="filter_text2[0].disposition" ng-change="MakeFilter2()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="CriteriaName" ng-model="filter_text1[0].CriteriaName" ng-change="MakeFilter1()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>

                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="mpn_id" ng-model="filter_text2[0].mpn_id" ng-change="MakeFilter2()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                    <md-input-container class="md-block mt-0">
                                                                        <input type="text" name="part_spec_id" ng-model="filter_text2[0].part_spec_id" ng-change="MakeFilter2()" aria-label="text" />
                                                                    </md-input-container>
                                                                </td>
                                                                <td>
                                                                    <md-input-container class="md-block mt-0">
                                                                        <input type="text" name="COO" ng-model="filter_text2[0].COO" ng-change="MakeFilter2()" aria-label="text" />
                                                                    </md-input-container>
                                                                </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="BinName" ng-model="filter_text2[0].BinName" ng-change="MakeFilter2()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="LocationName" ng-model="filter_text2[0].LocationName" ng-change="MakeFilter2()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block mt-0">
                                                                            <input type="text" name="MaximumAssets" ng-model="filter_text2[0].MaximumAssets" ng-change="MakeFilter2()" aria-label="text" />
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td>&nbsp;</td>
                                                                    <td>&nbsp;</td>

                                                                </tr>
                                                            </thead>

                                                            <tbody ng-show="pagedItems2.length > 0">
                                                                <tr ng-repeat="product in pagedItems2">
                                                                    <td class="action-icons" style="min-width: 40px;">
                                                                        <span ng-click="EditSortConfiguration(product,$event)"><i class="material-icons text-danger edit">edit</i></span>
                                                                    </td>
                                                                    <td>
                                                                        {{product.FacilityName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.GroupName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.disposition}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.CriteriaName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.mpn_id}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.part_spec_id}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.COO}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.BinName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.LocationName}}
                                                                    </td>
                                                                    <td>
                                                                        {{product.MaximumAssets}}
                                                                    </td>
                                                                    <td>
                                                                        <md-input-container class="md-block tdinput">
                                                                            <md-select name="Active" ng-model="product.Status" ng-change="ChangeSortconfigurationStatus(product);">
                                                                                <md-option value='1'> Active </md-option>
                                                                                <md-option value='0'> Inactive </md-option>
                                                                            </md-select>
                                                                        </md-input-container>
                                                                    </td>
                                                                    <td style="text-align: center;">
                                                                        <i class="material-icons text-danger" ng-click="Deletesortconfiguration(product,$event)">delete_forever</i>
                                                                    </td>
                                                                </tr>
                                                            </tbody>

                                                            <tfoot>
                                                                <tr>
                                                                    <td colspan="9">
                                                                        <div>
                                                                            <ul class="pagination">
                                                                                <li ng-class="prevPageDisabled(2)">
                                                                                    <a href ng-click="firstPage(2)"><< First</a>
                                                                                </li>
                                                                                <li ng-class="prevPageDisabled(2)">
                                                                                    <a href ng-click="prevPage(2)"><< Prev</a>
                                                                                </li>
                                                                                <li ng-repeat="n in range(2)" ng-class="{active: n == currentPage2}" ng-click="setPage(n,2)" ng-show="n >= 0">
                                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                                </li>
                                                                                <li ng-class="nextPageDisabled(2)">
                                                                                    <a href ng-click="nextPage(2)">Next >></a>
                                                                                </li>
                                                                                <li ng-class="nextPageDisabled(2)">
                                                                                    <a href ng-click="lastPage(2)">Last >></a>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </tfoot>

                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--Archive List Close-->
                                        </p>
                                    </md-content>
                                </md-tab>

                            </md-content>
                        </div>
                    </div>






                </div>
            </md-card>
        </article>
    </div>
</div>
