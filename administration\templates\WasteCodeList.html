<div ng-controller = "WasteCodeList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="RemovalCodeList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="RemovalCodeList = !RemovalCodeList" class="material-icons md-primary" ng-show="RemovalCodeList">keyboard_arrow_up</i>
                                <i ng-click="RemovalCodeList = !RemovalCodeList" class="material-icons md-primary" ng-show="! RemovalCodeList">keyboard_arrow_down</i>
                                <span ng-click="RemovalCodeList = !RemovalCodeList">Classification Code List</span>
                                <div flex></div> 
                                 <a href="#!/WasteCodeList" ng-click="ExportRemovalCodeList()" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>
                                <a href="#!/WasteCode" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Classification Code
                                </a>
                            </div>
                        </md-toolbar>
                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">                            
                            <p>No Classification Codes Available </p>
                        </div>
                        <div class="row"  ng-show="RemovalCodeList">
                            <div class="col-md-12">
                                <div class="col-md-12">                            
                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('WasteCode')" ng-class="{'orderby' : OrderBy == 'WasteCode'}">
                                                        <div>                               
                                                            Classification Code<i class="fa fa-sort pull-right" ng-show="OrderBy != 'WasteCode'"></i>                                 
                                                            <span ng-show="OrderBy == 'WasteCode'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">                           
                                                        <div>                               
                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>                                    
                                                            <span ng-show="OrderBy == 'disposition'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                           
                                                        <div>                               
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                    
                                                            <span ng-show="OrderBy == 'FacilityName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('InventoryType')" ng-class="{'orderby' : OrderBy == 'InventoryType'}">                           
                                                        <div>                               
                                                            Inventory Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'InventoryType'"></i>                                    
                                                            <span ng-show="OrderBy == 'InventoryType'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('part_type')" ng-class="{'orderby' : OrderBy == 'part_type'}">                           
                                                        <div>                               
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'part_type'"></i>                                    
                                                            <span ng-show="OrderBy == 'part_type'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">                           
                                                        <div>                               
                                                            Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>                                    
                                                            <span ng-show="OrderBy == 'Description'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('WasteClassificationType')" ng-class="{'orderby' : OrderBy == 'WasteClassificationType'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Classification Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'WasteClassificationType'"></i>                                  
                                                            <span ng-show="OrderBy == 'WasteClassificationType'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>                                  
                                                            <span ng-show="OrderBy == 'StatusName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>                                                    
                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="WasteCode" ng-model="filter_text[0].WasteCode" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="InventoryType" ng-model="filter_text[0].InventoryType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="part_type" ng-model="filter_text[0].part_type" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Description" ng-model="filter_text[0].Description" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="WasteClassificationType" ng-model="filter_text[0].WasteClassificationType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                   
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/WasteCode/{{product.WasteCodeID}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a></td>
                                                    <td>
                                                        {{product.WasteCode}}                            
                                                    </td>                       
                                                    <td>
                                                        {{product.disposition}}
                                                    </td>
                                                    <td>
                                                        {{product.FacilityName}}
                                                    </td>

                                                    <!-- <td>
                                                        {{product.InventoryType}}
                                                    </td> -->
                                                    <td>
                                                        {{product.part_type}}
                                                    </td>

                                                    <td>
                                                        {{product.Description}}
                                                    </td>
                                                    <td>
                                                        {{product.WasteClassificationType}}
                                                    </td>
                                                    <td>
                                                       {{product.StatusName}}
                                                    </td>                  
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="8">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>