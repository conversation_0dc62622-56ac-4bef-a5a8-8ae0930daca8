<?php
session_start();
include_once("connection.php");
include_once("config.php");
require 'vendor/finalvendor/autoload.php';
require 'administration/vendor/autoload.php';
use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;

class CommonClass {
	public $connectionlink;
	public function __construct() {
		$this->connectionlink = Connection::DBConnect();
    }

	public function isPermitted($ProfileID,$PageName) {
		try {
			if($ProfileID == '1') {//Admin Profile
				$query = "select count(*) from left_menus where TabName = '".mysqli_real_escape_string($this->connectionlink,$PageName)."' and AccountID = '".$_SESSION['user']['AccountID']."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > 0) {
						return true;
					} else {
						return false;
					}
				} else {					
					return false;
				}
			} else {
				$query = "select count(*) from left_menus_assignment a,left_menus l where a.ProfileID = '".mysqli_real_escape_string($this->connectionlink,$ProfileID)."' and a.Status = '1' and a.MenuID = l.MenuID and l.TabName = '".mysqli_real_escape_string($this->connectionlink,$PageName)."' and l.AccountID = '".$_SESSION['user']['AccountID']."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > 0) {
						return true;
					} else {
						return false;
					}
				} else {					
					return false;
				}
			}
		} catch (Exception $e) {
			return $e->getMessage();
		}
	}

	public function isWritePermitted($ProfileID,$PageName) {
		try {
			if($ProfileID == '1') {//Admin Profile
				$query = "select count(*) from left_menus where TabName = '".mysqli_real_escape_string($this->connectionlink,$PageName)."' and AccountID = '".$_SESSION['user']['AccountID']."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > 0) {
						return true;
					} else {
						return false;
					}
				} else {					
					return false;
				}
			} else {
				//$query = "select count(*) from left_menus_assignment a,left_menus l where a.ProfileID = '".mysqli_real_escape_string($this->connectionlink,$ProfileID)."' and a.Status = '1' and a.MenuID = l.MenuID and l.TabName = '".mysqli_real_escape_string($this->connectionlink,$PageName)."' and l.AccountID = '".$_SESSION['user']['AccountID']."' and l.ReadonlyEnabled = '1'";
				$query = "select count(*) from left_menus_assignment a,left_menus l where a.ProfileID = '".mysqli_real_escape_string($this->connectionlink,$ProfileID)."' and a.Status = '1' and a.MenuID = l.MenuID and l.TabName = '".mysqli_real_escape_string($this->connectionlink,$PageName)."' and l.AccountID = '".$_SESSION['user']['AccountID']."' and a.ReadonlyStatus = '1'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > 0) {
						return false;
					} else {
						return true;
					}
				} else {					
					return true;
				}
			}
		} catch (Exception $e) {
			return $e->getMessage();
		}
	}


	public function CheckDuplicate($type,$table,$column,$value,$AccountID,$ID,$IDValue) {
		try {	
			$duplicate = false;
			if($type == 'New') { //If New Record
				$query = "select count(*) from `".$table."` where ".$column." = '".mysqli_real_escape_string($this->connectionlink,$value)."' ";
				if($AccountID == true) {
					$query = $query . " and AccountID = '".$_SESSION['user']['AccountID']."'";
				}
			} else if($type == 'Edit') {
				$query = "select count(*) from `".$table."` where ".$column." = '".mysqli_real_escape_string($this->connectionlink,$value)."' and ".$ID." != '".mysqli_real_escape_string($this->connectionlink,$IDValue)."' ";
				if($AccountID == true) {
					$query = $query . " and AccountID = '".$_SESSION['user']['AccountID']."'";
				}
			}
			$q = mysqli_query($this->connectionlink,$query);		
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['count(*)'] > 0) {
					$duplicate = true;
				}
			} 
			return $duplicate;
		} catch (Exception $e) {
			return $e->getMessage();
		}
	}


	public function RecordAdminTracking($item_type,$item_name,$item,$action) {
		$query20 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID) values ('".$item_type."','".$item_name."','".mysqli_real_escape_string($this->connectionlink,$item)."','".mysqli_real_escape_string($this->connectionlink,$action)."',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."')";
		$q20 = mysqli_query($this->connectionlink,$query20);
		return true;
	}
	
	public function RecordAdminTracking1($item_type,$old,$new,$fields,$item) {
		$message = '';
		for($i=0;$i<count($fields);$i++) {
			if($old[$fields[$i]['field']] != $new[$fields[$i]['field']]) {
				if($fields[$i]['Reference'] == '1') { //Value in different table
					$old_value = '';
					$new_value = '';
					//Start get old value
					$query = "select ".$fields[$i]['TableColumn']." from ".$fields[$i]['Table']." where ".$fields[$i]['TableIDColumn']." = '".$old[$fields[$i]['field']]."'";
					$q = mysqli_query($this->connectionlink,$query);		
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row = mysqli_fetch_assoc($q);
						$old_value = $row[$fields[$i]['TableColumn']];
					}
					//End get old value

					//Start get new value
					$query = "select ".$fields[$i]['TableColumn']." from ".$fields[$i]['Table']." where ".$fields[$i]['TableIDColumn']." = '".$new[$fields[$i]['field']]."'";
					$q = mysqli_query($this->connectionlink,$query);
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row = mysqli_fetch_assoc($q);
						$new_value = $row[$fields[$i]['TableColumn']];
					}
					//End get new value
					$action = $fields[$i]['Display']." Changed from ".$old_value." to ".$new_value;
					$query2 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID) values ('".$item_type."','Edit','".mysqli_real_escape_string($this->connectionlink,$item)."','".mysqli_real_escape_string($this->connectionlink,$action)."',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."')";
					$q2 = mysqli_query($this->connectionlink,$query2);
					//$message = $message .$fields[$i]['Display']." Changed from ".$old_value." to ".$new_value."\n\n";
				} else {
					$action = $fields[$i]['Display']." Changed from ".$old[$fields[$i]['field']]." to ".$new[$fields[$i]['field']];
					$query1 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID) values ('".$item_type."','Edit','".mysqli_real_escape_string($this->connectionlink,$item)."','".mysqli_real_escape_string($this->connectionlink,$action)."',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."')";
					$q1 = mysqli_query($this->connectionlink,$query1);
					//$message = $message .$fields[$i]['Display']." Changed from ".$old[$fields[$i]['field']]." to ".$new[$fields[$i]['field']]."\n\n";
				}				
			}
		}
		return true;
	}

	public function ValidateBinName($BinName) {
		$query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$BinName)."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}			
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['InventoryBased'] == '1') {
				$json['Success'] = false;
				$json['Error'] = 'BIN is dedicated for Sub Component';
				return $json;
			}
			if($row['StatusID'] != '1') {					
				$json['Success'] = false;
				$json['Error'] = 'BIN Status is not Active';
				return $json;
			}

			if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {									
				$json['Success'] = false;
				$json['Error'] = 'BIN Facility is different from Users Facility';
				return $json;
			}

			$json['Success'] = true;
			$json['CustomPalletID'] = $row['CustomPalletID'];
			$json['BinName'] = $row['BinName'];
			return $json;
		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid BIN';
			return $json;
		}
	}


	public function ValidateInventoryBinName($BinName,$disposition_id) {
		$query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$BinName)."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}			
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['InventoryBased'] == '0') {
				// $json['Success'] = false;
				// $json['Error'] = 'BIN is not for Sub Component';
				// return $json;
			}
			if($row['StatusID'] != '1') {					
				$json['Success'] = false;
				$json['Error'] = 'BIN Status is not Active';
				return $json;
			}

			if($disposition_id != NULL) {
				if($row['disposition_id'] != $disposition_id) {					
					$json['Success'] = false;
					$json['Error'] = 'BIN Disposition is different from Media Out Disposition';
					return $json;
				}
			}

			if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {									
				$json['Success'] = false;
				$json['Error'] = 'BIN Facility is different from Users Facility';
				return $json;
			}

			$json['Success'] = true;
			$json['CustomPalletID'] = $row['CustomPalletID'];
			$json['BinName'] = $row['BinName'];
			return $json;
		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid BIN';
			return $json;
		}
	}

	public function ValidateInventorySerial($SerialNumber) {
		$query = "select count(*) from inventory where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$SerialNumber)."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}			
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['count(*)'] > 0) {
				$json['Success'] = false;
				$json['Error'] = 'Already Exists';
				return $json;
			} else {
				$json['Success'] = true;
				$json['Error'] = 'Unique Serial';
				return $json;
			}			
		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid Serial';
			return $json;
		}
	}

	public function ValidateInventorySerial1($SerialNumber) {
		$query = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$SerialNumber)."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}			
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['count(*)'] > 0) {
				$json['Success'] = false;
				$json['Error'] = 'Already Exists';
				return $json;
			} else {
				$json['Success'] = true;
				$json['Error'] = 'Unique Serial';
				return $json;
			}			
		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid Serial';
			return $json;
		}
	}			


	public function RecordUserTransaction($TransactionType,$Description) {
		$query = "insert into user_transactions (UserId,TransactionTime,TransactionIPAddress,TransactionType,Description) values ('".$_SESSION['user']['UserId']."',NOW(),'".$_SERVER['REMOTE_ADDR']."','".mysqli_real_escape_string($this->connectionlink,$TransactionType)."','".mysqli_real_escape_string($this->connectionlink,$Description)."')";
		$q = mysqli_query($this->connectionlink,$query);
		return true;
	}

	public function RecordUserTransactionWithURL($TransactionType,$Description,$URL) {
		$query = "insert into user_transactions (UserId,TransactionTime,TransactionIPAddress,TransactionType,Description,PageURL) values ('".$_SESSION['user']['UserId']."',NOW(),'".$_SERVER['REMOTE_ADDR']."','".mysqli_real_escape_string($this->connectionlink,$TransactionType)."','".mysqli_real_escape_string($this->connectionlink,$Description)."','".mysqli_real_escape_string($this->connectionlink,$URL)."')";
		$q = mysqli_query($this->connectionlink,$query);
		return true;
	}

	public function UnlockUserStations() {
		$query = "update site set Locked = 0,LockedForUser = NULL where LockedForUser = '".$_SESSION['user']['UserId']."'";
		$q = mysqli_query($this->connectionlink,$query);
		return true;
	}


	public function isContainerMPNLock($ShippingID,$MPN,$ShippingContainerID) {
		if($MPN == '') {
			$json['Success'] = false;
			$json['Error'] = 'MPN is empty';
			return $json;
		}

		if($ShippingID == '') {
			$json['Success'] = false;
			$json['Error'] = 'TicketID is empty';
			return $json;
		}

		//Start check IF Valid MPN
		$query11 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$MPN)."'";
		$q11 = mysqli_query($this->connectionlink,$query11);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {

		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid MPN';
			return $json;
		}
		//End check IF Valid MPN

		//$query = "select v.ContainerMPNLock from shipping s,vendor v where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$ShippingID)."' and s.VendorID = v.VendorID ";
		$query = "select v.ContainerMPNLock,s.VendorID,s.DestinationFacilityID from shipping s 
		left join vendor v on s.VendorID = v.VendorID 
		where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$ShippingID)."' ";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['DestinationFacilityID'] > 0) {
				$json['Success'] = true;
				$json['Error'] = 'Inter Facility Transfer';
				return $json;
			}
			if($row['ContainerMPNLock'] == '1') {//If Single MPN Lock for Destination
				$query1 = "select UniversalModelNumber from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Error'] = mysqli_error($this->connectionlink);
					return $json;
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['UniversalModelNumber'] != $MPN) {//Different MPN
						$json['Success'] = false;
						$json['Error'] = 'Unable to comingle MPNs in in this shipment container';
						return $json;
					} else {//Same MPN as before
						$json['Success'] = true;
						$json['Error'] = 'Same MPN as before';
						return $json;
					}
				} else {//First Record in the Container
					$json['Success'] = true;
					$json['Error'] = 'First Record in Container';
					return $json;
				}
			} else {
				$json['Success'] = true;
				$json['Error'] = 'No Lock for Destination';
				return $json;
			}
		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid TicketID or Invalid Destination';
			return $json;
		}		
	}


	public function isContainerCOOLock($ShippingID,$COOID,$ShippingContainerID) {
		if($COOID == '') {
			$json['Success'] = false;
			$json['Error'] = 'COO is empty';
			return $json;
		}

		if($ShippingID == '') {
			$json['Success'] = false;
			$json['Error'] = 'TicketID is empty';
			return $json;
		}		

		$query = "select v.ContainerCOOLock,s.VendorID,s.DestinationFacilityID from shipping s 
		left join vendor v on s.VendorID = v.VendorID 
		where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$ShippingID)."' ";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			if($row['DestinationFacilityID'] > 0) {
				$json['Success'] = true;
				$json['Error'] = 'Inter Facility Transfer';
				return $json;
			}
			if($row['ContainerCOOLock'] == '1') {//If Single MPN Lock for Destination
				$query1 = "select COOID from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Error'] = mysqli_error($this->connectionlink);
					return $json;
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['COOID'] != $COOID) {//Different COO
						$json['Success'] = false;
						$json['Error'] = 'Unable to comingle COOs in in this shipment container';
						return $json;
					} else {//Same COO as before
						$json['Success'] = true;
						$json['Error'] = 'Same COO as before';
						return $json;
					}
				} else {//First Record in the Container
					$json['Success'] = true;
					$json['Error'] = 'First Record in Container';
					return $json;
				}
			} else {
				$json['Success'] = true;
				$json['Error'] = 'No Lock for Destination';
				return $json;
			}
		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid TicketID or Invalid Destination';
			return $json;
		}		
	}


	public function ValidateMPN($MPN) {
		//$query = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$MPN)."'";
		$query = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$MPN)."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}			
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Error'] = 'Valid MPN';
			$json['MPN'] = $row;
			return $json;			
		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid';
			return $json;
		}
	}

	public function UploadToS3($filename,$file) {
		try {
			$s3 = S3Client::factory(
				array(
					'credentials' => array(
						'key' => S3_key_eviridis,
						'secret' => S3_secret_eviridis
					),
					'version' => 'latest',
					'region'  => S3_region_eviridis
				)
			);

			$result = $s3->putObject(
				array(
					'Bucket'=> S3_bucket_eviridis,
					'Key' =>  $filename,
					'SourceFile' => $file
					//'ACL'    => 'public-read'
				)
			);
			return true;
		} catch (Exception $ex) {
			return $ex->getMessage();
			return false;
		}
	}

	public function UnlockUserLockedSourceBins() {
		$query = "delete from source_bin_user_mapping where CreatedBy = '".$_SESSION['user']['UserId']."'";
		$q = mysqli_query($this->connectionlink,$query);
		return true;
	}


	public function GetExactMPN($MPN) {
		$input = $MPN;

		$MPN = preg_replace('/[^a-zA-Z0-9]/', '', $MPN);// Removing all special characters and spaces from the input mpn
		//Start check for Exact Match
		$query = "select idcatlog_creation,mpn_id,demand_type from catlog_creation where lower(mpn_id) = lower('".mysqli_real_escape_string($this->connectionlink,$MPN)."') ";
		$query = "select idcatlog_creation,mpn_id,demand_type from catlog_creation where lower(mpn_id) = lower('".mysqli_real_escape_string($this->connectionlink,$MPN)."') and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
		$query = "select idcatlog_creation,mpn_id,demand_type,idManufacturer from catlog_creation where lower(mpn_id) = lower('".mysqli_real_escape_string($this->connectionlink,$input)."') and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$query4 = "select mpn_id,demand_type from catlog_creation where idcatlog_creation = '".$row['idcatlog_creation']."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;
				$json['Error'] = mysqli_error($this->connectionlink);
				return $json;
			}
			$row4 = mysqli_fetch_assoc($q4);
			$json['Success'] = true;
			//$json['MPN'] = $MPN;
			$json['MPN'] = $row4['mpn_id'];
			if($row4['demand_type'] == 'Reuse') {
				$json['Reuse'] = '1';
			} else {
				$json['Reuse'] = '0';
			}
			return $json;
		} else {
			//Start get all MPN
			//$query5 = "select idcatlog_creation,mpn_id,demand_type from catlog_creation where FacilityID = '".$_SESSION['user']['FacilityID']."' order by mpn_id desc";
			//$query5 = "select idcatlog_creation,mpn_id,demand_type from catlog_creation where FacilityID = '".$_SESSION['user']['FacilityID']."' and LENGTH(mpn_id) > 3 and LENGTH(mpn_id) <= ".strlen($MPN)." order by mpn_id desc";
			//$query5 = "select idcatlog_creation,mpn_id,demand_type from catlog_creation where FacilityID = '".$_SESSION['user']['FacilityID']."' and LENGTH(mpn_id) > 5 and LENGTH(mpn_id) <= ".strlen($input)." order by mpn_id desc";
			$query5 = "select idcatlog_creation,mpn_id,demand_type,idManufacturer from catlog_creation where FacilityID = '".$_SESSION['user']['FacilityID']."' and LENGTH(mpn_id) > 3 and LENGTH(mpn_id) <= ".strlen($input)." order by length(mpn_id) desc";
			$q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Error'] = mysqli_error($this->connectionlink);
				return $json;
			}

			if(mysqli_affected_rows($this->connectionlink) > 0) {			
				while($row5 = mysqli_fetch_assoc($q5)) {
					$first = preg_replace('/\s+/', '', strtolower($MPN));
					$row5['mpn_id'] = preg_replace('/[^a-zA-Z0-9]/', '', $row5['mpn_id']);
					$second = preg_replace('/\s+/', '', strtolower($row5['mpn_id']));
					//if(strpos($MPN, $row5['mpn_id']) !== false) { //matched
					if(strpos($first,$second) !== false) { //matched
						//str_replace(' ', '', $string);

						$query44 = "select mpn_id,demand_type from catlog_creation where idcatlog_creation = '".$row5['idcatlog_creation']."'";
						$q44 = mysqli_query($this->connectionlink,$query44);
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;
							$json['Error'] = mysqli_error($this->connectionlink);
							return $json;
						}
						$row44 = mysqli_fetch_assoc($q44);

						if($row44['demand_type'] == 'Reuse') {
							$json['Reuse'] = '1';
						} else {
							$json['Reuse'] = '0';
						}

						$json['Success'] = true;
						$json['MPN'] = $row44['mpn_id'];
						return $json;			
					}			
				}	
				$json['Success'] = false;
				$json['Error'] = 'No matching MPN available';
				return $json;		
			} else {
				$json['Success'] = false;
				$json['Error'] = 'No MPN details available';
				return $json;
			}
		}		
	}


	public function GetRackItems($PalletID) {
		try {
			//Start check IF pallet exists in fake api
			// $query = "select * from upload_api_responses where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' ";			
			// $q = mysqli_query($this->connectionlink,$query);
			// if(mysqli_error($this->connectionlink)) {			
			// 	$json['Success'] = false;
			// 	$json['Error'] = mysqli_error($this->connectionlink);
			// 	return $json;
			// }
			// if(mysqli_affected_rows($this->connectionlink) > 0) {
			// 	$row = mysqli_fetch_assoc($q);
			// 	$response = $row['Response'];
			// } else {
			//End check IF pallet exists in fake api
			if(true) {
				//Start calling the API
				$curl = curl_init();

				curl_setopt_array($curl, array(
				CURLOPT_URL => APIURL.'?palletid='.$PalletID,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'GET',
				CURLOPT_POSTFIELDS =>'{}',
				CURLOPT_HTTPHEADER => array(
					'Content-Type: text/plain'
				),
				));
				$response = curl_exec($curl);
				curl_close($curl);
			}
			//End calling the API
			$res = json_decode($response,true);
			$OnDemandMedia = 'No';
			if(count($res['diskWipeResult']) > 0) {
				//Start insert in API History table
				$input = APIURL.'?palletid='.$PalletID;
				$query22 = "insert into speed_rackdetails_api (idPallet,Input,Output,Result,APICalledDATETIME,APICalledBy,FacilityID,rackIpn,site,workType) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$input)."','".mysqli_real_escape_string($this->connectionlink,$response)."','Pass',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$res['rackIpn'])."','".mysqli_real_escape_string($this->connectionlink,$res['site'])."','".mysqli_real_escape_string($this->connectionlink,$res['workType'])."')";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Error'] = mysqli_error($this->connectionlink);
					return $json;
				}
				$ID = mysqli_insert_id($this->connectionlink);
				//End insert in API History table				
				for($i=0;$i<count($res['diskWipeResult']);$i++) {

					if($res['diskWipeResult'][$i]['intact'] == true || $res['diskWipeResult'][$i]['intact'] == '1') {
						$intact = '1';

						if($res['diskWipeResult'][$i]['media']) {
							if(count($res['diskWipeResult'][$i]['media']) > 0) {
								if($res['diskWipeResult'][$i]['expectedHDDDriveCount'] == 0 && $res['diskWipeResult'][$i]['expectedSSDDriveCount'] == 0) {
									$res['diskWipeResult'][$i]['expectedHDDDriveCount'] = 0;
									$res['diskWipeResult'][$i]['expectedSSDDriveCount'] = 0;
								} else {											
								}
							} else {
								$res['diskWipeResult'][$i]['expectedHDDDriveCount'] = 0;
								$res['diskWipeResult'][$i]['expectedSSDDriveCount'] = 0;	
							}
						} else {
							$res['diskWipeResult'][$i]['expectedHDDDriveCount'] = 0;
							$res['diskWipeResult'][$i]['expectedSSDDriveCount'] = 0;	
						}							

					} else {
						$intact = '0';
						$res['diskWipeResult'][$i]['expectedHDDDriveCount'] = 0;
						$res['diskWipeResult'][$i]['expectedSSDDriveCount'] = 0;
					}

					//$query = "insert into speed_expected_servers (idPallet,SerialNumber,Type,APICalledDate,APICalledBy,HDDCount,SSDCount,IPN,intact) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentAssetId'])."','Server',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['expectedHDDDriveCount'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['expectedSSDDriveCount'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentIpn'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['intact'])."')";
					$query = "insert into speed_expected_servers (idPallet,SerialNumber,Type,APICalledDate,APICalledBy,HDDCount,SSDCount,IPN,intact) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentAssetId'])."','Server',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['expectedHDDDriveCount'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['expectedSSDDriveCount'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentIpn'])."','".$intact."')";
					$q = mysqli_query($this->connectionlink,$query);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;
						$json['Error'] = mysqli_error($this->connectionlink);
						return $json;
					}

					//$query23 = "insert into speed_rackdetails_api_servers (ID,idPallet,SerialNumber,Type,APICalledDate,APICalledBy,HDDCount,SSDCount,IPN,intact) values ('".$ID ."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentAssetId'])."','Server',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['expectedHDDDriveCount'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['expectedSSDDriveCount'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentIpn'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['intact'])."')";
					$query23 = "insert into speed_rackdetails_api_servers (ID,idPallet,SerialNumber,Type,APICalledDate,APICalledBy,HDDCount,SSDCount,IPN,intact) values ('".$ID ."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentAssetId'])."','Server',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['expectedHDDDriveCount'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['expectedSSDDriveCount'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentIpn'])."','".$intact."')";
					$q23 = mysqli_query($this->connectionlink,$query23);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;
						$json['Error'] = mysqli_error($this->connectionlink);
						return $json;
					}
					$API_SERVER_ID = mysqli_insert_id($this->connectionlink);

					//Start insert Media
					//if($res['diskWipeResult'][$i]['media']) {
					if($res['diskWipeResult'][$i]['media'] && $intact == '1' && ($res['diskWipeResult'][$i]['expectedHDDDriveCount'] > 0 || $res['diskWipeResult'][$i]['expectedSSDDriveCount'] > 0)) {
						for($j=0;$j<count($res['diskWipeResult'][$i]['media']);$j++) {

							$exact_mpn = $this->GetExactMPN($res['diskWipeResult'][$i]['media'][$j]['model']);
							if($exact_mpn['Success'] == true) {
								$MPN = $exact_mpn['MPN'];
							} else {
								$MPN = $res['diskWipeResult'][$i]['media'][$j]['model'];
							}							
							if($exact_mpn['Reuse'] == '1') {
								$OnDemandMedia = 'Yes';
							}

							//$query1 = "insert into speed_expected_media (idPallet,ServerSerialNumber,MediaSerialNumber,MediaMPN,APICalledDate,APICalledBy,MPNFromAPI) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentAssetId'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['media'][$j]['serial'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['media'][$j]['model'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['media'][$j]['model'])."')";
							$query1 = "insert into speed_expected_media (idPallet,ServerSerialNumber,MediaSerialNumber,MediaMPN,APICalledDate,APICalledBy,MPNFromAPI) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentAssetId'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['media'][$j]['serial'])."','".mysqli_real_escape_string($this->connectionlink,$MPN)."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['media'][$j]['model'])."')";
							$q1 = mysqli_query($this->connectionlink,$query1);
							if(mysqli_error($this->connectionlink)) {			
								$json['Success'] = false;
								$json['Error'] = mysqli_error($this->connectionlink);
								return $json;
							}

							$query24 = "insert into speed_rackdetails_api_media (API_SERVER_ID,idPallet,ServerSerialNumber,MediaSerialNumber,MediaMPN,APICalledDate,APICalledBy,ADJUSTED_MPN) values ('".$API_SERVER_ID."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['parentAssetId'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['media'][$j]['serial'])."','".mysqli_real_escape_string($this->connectionlink,$res['diskWipeResult'][$i]['media'][$j]['model'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$MPN)."')";
							$q24 = mysqli_query($this->connectionlink,$query24);
							if(mysqli_error($this->connectionlink)) {			
								$json['Success'] = false;
								$json['Error'] = mysqli_error($this->connectionlink);
								return $json;
							}

						}
					}
					//End insert Media
				}

				//Start inserting rackIPN in pallets
				if($res['rackIpn'] != '') {
					$query3 = "update pallets set rackIpn = '".mysqli_real_escape_string($this->connectionlink,$res['rackIpn'])."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' ";
					$q3 = mysqli_query($this->connectionlink,$query3);					
				}

				if($res['workType'] != '') {
					$query3 = "update pallets set workType = '".mysqli_real_escape_string($this->connectionlink,$res['workType'])."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' ";
					$q3 = mysqli_query($this->connectionlink,$query3);					
				}

				if($res['rackFunctional'] == true) {
					$POF = 1;
				} else {
					$POF = 0;
				}

				$query3 = "update pallets set POF = '".mysqli_real_escape_string($this->connectionlink,$POF)."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);	

				$query3 = "update pallets set OnDemandMedia = '".mysqli_real_escape_string($this->connectionlink,$OnDemandMedia)."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
				//End inserting rackIPN in pallets
				$json['OnDemandMedia'] = $OnDemandMedia;
				$json['Success'] = true;		
				return $json;
			} else {

				//Start insert in API History table
				$input = APIURL.'?palletid='.$PalletID;
				$query22 = "insert into speed_rackdetails_api (idPallet,Input,Output,Result,APICalledDATETIME,APICalledBy,FacilityID) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$input)."','".mysqli_real_escape_string($this->connectionlink,$response)."','Fail',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['FacilityID']."')";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Error'] = mysqli_error($this->connectionlink);
					return $json;
				}
				//End insert in API History table
				$json['OnDemandMedia'] = $OnDemandMedia;
				$json['Success'] = false;
				$json['Error'] = $res['message'];
				return $json;
			}		

			//Facking API Call and Creating Servers and Switches
			for($i=0;$i<2;$i++) {
				$SerialNumber = time()+$i;
				//$query = "insert into speed_expected_servers (idPallet,SerialNumber,Type,APICalledDate,APICalledBy) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".$SerialNumber."','Server',NOW(),'".$_SESSION['user']['UserId']."')";
				$query = "insert into speed_expected_servers (idPallet,SerialNumber,Type,APICalledDate,APICalledBy,HDDCount,SSDCount) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".$SerialNumber."','Server',NOW(),'".$_SESSION['user']['UserId']."',1,1)";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Error'] = mysqli_error($this->connectionlink);
					return $json;
				}

				//Start insert Server Media
				for($j=0;$j<2;$j++) {				
					if($j == 0) {
						$type = 'HDD';
						$MediaSerial = (time() * 2)+$j+$i;
					} else {
						$type = 'SSD';
						$MediaSerial = (time() * 3)+$j+$i;
					}				
					//$query1 = "insert into speed_expected_media (idPallet,ServerSerialNumber,MediaSerialNumber,MediaType,MediaMPN,APICalledDate,APICalledBy) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".$SerialNumber."','".$MediaSerial."','".$type."','WXK06-2601',NOW(),'".$_SESSION['user']['UserId']."')";
					$query1 = "insert into speed_expected_media (idPallet,ServerSerialNumber,MediaSerialNumber,MediaMPN,APICalledDate,APICalledBy) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".$SerialNumber."','".$MediaSerial."','WXK06-2601',NOW(),'".$_SESSION['user']['UserId']."')";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;
						$json['Error'] = mysqli_error($this->connectionlink);
						return $json;
					}
				}
				//End insert Server Media
			}

			// for($i=0;$i<2;$i++) {
			// 	$SerialNumber = time()+$i+5;
			// 	$query = "insert into speed_expected_servers (idPallet,SerialNumber,Type,APICalledDate,APICalledBy) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".$SerialNumber."','Switch',NOW(),'".$_SESSION['user']['UserId']."')";
			// 	$q = mysqli_query($this->connectionlink,$query);
			// 	if(mysqli_error($this->connectionlink)) {			
			// 		$json['Success'] = false;
			// 		$json['Error'] = mysqli_error($this->connectionlink);
			// 		return $json;
			// 	}
			// }

			$json['Success'] = true;		
			return $json;		
		
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Error'] = $ex->getMessage();
			return $json;			
		}
	}


	public function SendSNSMessage($message,$UniqueSerial,$event_type,$item_type,$MediaType,$NextBinID,$PalletID,$ServerID,$MediaID) {
		try {
			//Start sending SNS Message

			$curl = curl_init();
			curl_setopt_array($curl, array(
			CURLOPT_URL => SNSURL,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'PUT',
			CURLOPT_POSTFIELDS =>'{"message": '.$message.'}',
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			),
			));
			$response = curl_exec($curl);
			curl_close($curl);

			//End sending SNS Message
			$res = json_decode($response,true);
			if($res['MessageID']) {
				$MessageID = $res['MessageID'];
				$SNSResult = 'pass';
			} else {
				$MessageID = '';
				$SNSResult = 'fail';
			}
			
			//start insert into message history
			$query1 = "insert into speed_sns_messages (Input,OutPut,CreatedDate,CreatedBy,event_type,MessageID,SNSResult,idPallet";

			if($ServerID > 0) {
				$query1 = $query1. ",ServerID";
			}
			if($MediaID > 0) {
				$query1 = $query1. ",MediaID";
			}
			if($MediaType != NULL && $MediaType != '') {
				$query1 = $query1. ",MediaType";
			}
			if($NextBinID != NULL && $NextBinID != '') {
				$query1 = $query1. ",NextBinID";
			}
			if($item_type == 'SERVER') {
				$query1 = $query1. ",ServerSerialNumber";
			}
			if($item_type == 'MEDIA') {
				$query1 = $query1. ",MediaSerialNumber";
			}

			$query1 = $query1.") values ('".mysqli_real_escape_string($this->connectionlink,$message)."','".mysqli_real_escape_string($this->connectionlink,$response)."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$event_type)."','".mysqli_real_escape_string($this->connectionlink,$MessageID)."','".mysqli_real_escape_string($this->connectionlink,$SNSResult)."','".mysqli_real_escape_string($this->connectionlink,$PalletID)."'";

			if($ServerID > 0) {
				$query1 = $query1. ",'".mysqli_real_escape_string($this->connectionlink,$ServerID)."'";
			}
			if($MediaID > 0) {
				$query1 = $query1. ",'".mysqli_real_escape_string($this->connectionlink,$MediaID)."'";
			}
			if($MediaType != NULL && $MediaType != '') {
				$query1 = $query1. ",'".mysqli_real_escape_string($this->connectionlink,$MediaType)."'";
			}
			if($NextBinID != NULL && $NextBinID != '') {
				$query1 = $query1. ",'".mysqli_real_escape_string($this->connectionlink,$NextBinID)."'";
			}
			if($item_type == 'SERVER') {
				$query1 = $query1. ",'".mysqli_real_escape_string($this->connectionlink,$UniqueSerial)."'";
			}
			if($item_type == 'MEDIA') {
				$query1 = $query1. ",'".mysqli_real_escape_string($this->connectionlink,$UniqueSerial)."'";
			}

			$query1 = $query1.")";
			$q1 = mysqli_query($this->connectionlink,$query1);
			$json['id'] = mysqli_insert_id($this->connectionlink);
			//End insert into message history
			
			if($res['MessageID']) {
				$json['Success'] = true;		
				return $json;	
			} else {
				$json['Success'] = false;
				$json['Error'] = $res;
				return $json;
			}		
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Error'] = $ex->getMessage();
			return $json;			
		}			
	}

	public function getTimeSinceRackReceived($idPallet) {
		$query = "select p.*,TIMESTAMPDIFF(HOUR,ReceivedDate,NOW()) as HoursSinceReceived from pallets p where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$idPallet)."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}

		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['HoursSinceReceived'] = $row['HoursSinceReceived'];
			return $json;
		} else {
			$json['Success'] = false;
			$json['Error'] = 'Invalid Rack';
			return $json;
		}
	}

	public function RecordMPNUploadError($UploadID,$Error) {		

		//Start send Email
		//$API_KEY = "*********************************************************************";
		$API_KEY = "*********************************************************************";

		$email = new \SendGrid\Mail\Mail();
		$email->setFrom("<EMAIL>");

		$EmailSubject = 'MPN File ingestion failure';
		$email->setSubject($EmailSubject);

		$EmailContent = "<strong>MPN File Ingestion Failure </strong><br /><br />";
		$EmailContent = $EmailContent."<strong>Failure Reason : </strong>".$Error."<br />";

		$email->addTo('<EMAIL>');
		$email->addTo('<EMAIL>');
		$email->addTo('<EMAIL>');
		$email->addTo('<EMAIL>');

		$email->addContent(
			"text/html", $EmailContent
		);
		$sendgrid = new \SendGrid($API_KEY);

		$response = $sendgrid->send($email);

		//End send Email

		$query = "update mpn_file_uploads set Comments = '".mysqli_real_escape_string($this->connectionlink,$Error)."' where UploadID = '".mysqli_real_escape_string($this->connectionlink,$UploadID)."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		
		$json['Success'] = true;
		$json['Error'] = 'Updated';
		return $json;
	}

	public function UpdateShipmentContainerPartTypeSummary($ShippingContainerID) {
		$query = "select count(*),part_type FROM shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."' group by part_type";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		$summary = '';
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				if($i > 0) {
					$summary = $summary .', ';
				}
				$summary = $summary . $row['part_type'].' - '.$row['count(*)'];
				$i++;
			}
		}

		//Start get Removal Code
		$removal_code = '';

		$query4 = "select disposition_id from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."' ";
		$q4 = mysqli_query($this->connectionlink,$query4);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row4 = mysqli_fetch_assoc($q4);
			$disposition_id = $row4['disposition_id'];
		} else {
			$disposition_id = 0;
		}



		//$query2 = "select part_type from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."' and byproduct_id > 0";
		$query2 = "select distinct(part_type) from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."' ";
		$q2 = mysqli_query($this->connectionlink,$query2);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row2 = mysqli_fetch_assoc($q2)) {
				//$query3 = "select RemovalCode from removal_codes where FacilityID = '".$_SESSION['user']['FacilityID']."' and part_type = '".mysqli_real_escape_string($this->connectionlink,$row2['part_type'])."' and StatusID = '1' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$disposition_id)."' ";
				$query3 = "select WasteCode as RemovalCode from waste_codes where FacilityID = '".$_SESSION['user']['FacilityID']."' and part_type = '".mysqli_real_escape_string($this->connectionlink,$row2['part_type'])."' and StatusID = '1' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$disposition_id)."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Error'] = mysqli_error($this->connectionlink);
					return $json;
				}

				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
					if($i > 0) {
						$removal_code = $removal_code .'; ';
					}
					$removal_code = $removal_code . $row3['RemovalCode'];
					$i++;
				}				
			}			
		}
		//End get Removal Code
		//$removal_code = $query4;

		$query1 = "update shipping_containers set PartTypeSummary = '".mysqli_real_escape_string($this->connectionlink,$summary)."',RemovalCode = '".mysqli_real_escape_string($this->connectionlink,$removal_code)."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."'";
		$q1 = mysqli_query($this->connectionlink,$query1);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}

		return true;
	}


	public function ResetShipmentContainerPartTypeSummary($ShippingContainerID) {
		$query1 = "update shipping_containers set PartTypeSummary = '',RemovalCode = '' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$ShippingContainerID)."'";
		$q1 = mysqli_query($this->connectionlink,$query1);
		if(mysqli_error($this->connectionlink)) {			
			$json['Success'] = false;
			$json['Error'] = mysqli_error($this->connectionlink);
			return $json;
		}

		return true;
	}

	public function CreateASNinDeman($records) {
		try {
	
			$link1 = mysqli_connect(Deman_DB_HOST,Deman_DB_USER,Deman_DB_PASS,Deman_DB_NAME,null);
			if($mysqli->connect_error) {
				//exit('Error connecting to database'); //Should be a message a typical user could understand in production
	
				$json['Success'] = false;
				$json['Error'] = 'Error in connecting database';
				return $json;
			} else {
				for($i=0;$i<count($records);$i++) {
					$query = "insert into aws_shipments (origin_location_id,origin_ticket_id, container_id, container_type, seal1, seal2, seal3, seal4, weight_value, container_apn_id, serial_id, mpn_id, material_type, CreatedDate) values ('".mysqli_real_escape_string($link1,$records[$i]['origin_location_id'])."','".mysqli_real_escape_string($link1,$records[$i]['origin_ticket_id'])."','".mysqli_real_escape_string($link1,$records[$i]['container_id'])."','".mysqli_real_escape_string($link1,$records[$i]['container_type'])."','".mysqli_real_escape_string($link1,$records[$i]['seal1'])."','".mysqli_real_escape_string($link1,$records[$i]['seal2'])."','".mysqli_real_escape_string($link1,$records[$i]['seal3'])."','".mysqli_real_escape_string($link1,$records[$i]['seal4'])."','".mysqli_real_escape_string($link1,$records[$i]['weight_value'])."','".mysqli_real_escape_string($link1,$records[$i]['container_apn_id'])."','".mysqli_real_escape_string($link1,$records[$i]['serial_id'])."','".mysqli_real_escape_string($link1,$records[$i]['mpn_id'])."','".mysqli_real_escape_string($link1,$records[$i]['material_type'])."',NOW())";
					$q = mysqli_query($link1,$query);
					if(mysqli_error($link1)) {			
						$json['Success'] = false;
						$json['Error'] = mysqli_error($link1);
						return $json;
					}
					$id = mysqli_insert_id($link1);
					//Start check If Load already created
					$LoadID = $records[$i]['origin_ticket_id'];
					$query1 = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($link1,$LoadID)."'";
					$q1 = mysqli_query($link1,$query1);
					if(mysqli_error($link1)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($link1);
						return json_encode($json);
					}
					if(mysqli_affected_rows($link1) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						if($row1['count(*)'] == 0) {
							$query2 = "insert into loads (LoadId,DateCreated,LoadType,FacilityID) values ('".mysqli_real_escape_string($link1,$LoadID)."',NOW(),'SPEED SHIPMENT','14')";
							$q2 = mysqli_query($link1,$query2);
							if(mysqli_error($link1)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($link1);
								return json_encode($json);
							}
	
							//Start insert in load tracking
							$query10 = "insert into load_tracking (LoadId,`Action`,CreatedDate) values ('".mysqli_real_escape_string($link1,$LoadID)."','New Inbound Shipment Ticket Created through Button Click in SPEED',NOW())";
							$q10 = mysqli_query($link1,$query10);
							if(mysqli_error($link1)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($link1)."3";
								return json_encode($json);
							}
							//End insert in load tracking
	
						}
					}
					//End check If Load already created
	
	
	
					if($records[$i]['origin_location_id'] != '') { //origin_location_id
						//Start check If Customer exists in our database
						$query101 = "select * from customer where CustomerShotCode = '".mysqli_real_escape_string($link1,$records[$i]['origin_location_id'])."'";
						$q101 = mysqli_query($link1,$query101);
						if(mysqli_error($link1)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($link1);
							return json_encode($json);
						}
						if(mysqli_affected_rows($link1) > 0) {
							$row101 = mysqli_fetch_assoc($q101);
							$idCustomer = $row101['CustomerID'];
						} else {
							$idCustomer = NULL;
						}
						//End check If Customer exists in our database
					} else {
						$idCustomer = NULL;
					}
	
					$idPackage = 352;//Rack Package
	
					if($records[$i]['origin_ticket_id'] == '') {
						$query3 = "updateaws_shipments set exception_details = 'origin_ticket_id is null' where id = '".$id."' ";
						$q3 = mysqli_query($link1,$query3);
						if(mysqli_error($link1)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($link1);
							return json_encode($json);
						}
						continue;
					}
	
					if($records[$i]['container_id'] == '') {
						$query3 = "updateaws_shipments set exception_details = 'container_id is null' where id = '".$id."' ";
						$q3 = mysqli_query($link1,$query3);
						if(mysqli_error($link1)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($link1);
							return json_encode($json);
						}
						continue;
					}
	
	
	
					//Start check If Pallet exists
					$query3 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($link1,$records[$i]['container_id'])."'";
					$q3 = mysqli_query($link1,$query3);
					if(mysqli_error($link1)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($link1);
						return json_encode($json);
					}
					if(mysqli_affected_rows($link1) > 0) {
						$row3 = mysqli_fetch_assoc($q3);
						if($row3['count(*)'] == 0) {
							$query4 = "insert into pallets (idPallet,LoadId,status,CreatedDate,Type,SealNo1,SealNo2,SealNo3,SealNo4,PalletFacilityID,MaterialType,pallet_netweight,rackIpn,idPackage,BatchRecovery";
	
							if($idCustomer > 0) {
								$query4 = $query4 .",idCustomer";
							}
	
							$query4 = $query4 . ") values ('".mysqli_real_escape_string($link1,$records[$i]['container_id'])."','".mysqli_real_escape_string($link1,$LoadID)."','6',NOW(),'SPEED ASN BUTTON','".mysqli_real_escape_string($link1,$records[$i]['seal1'])."','n/a','n/a','n/a','14','".mysqli_real_escape_string($link1,$records[$i]['material_type'])."','".mysqli_real_escape_string($link1,$records[$i]['weight_value'])."','".mysqli_real_escape_string($link1,$records[$i]['container_apn_id'])."','".$idPackage."','".mysqli_real_escape_string($link1,$records[$i]['BatchRecovery'])."'";
	
							if($idCustomer > 0) {
								$query4 = $query4 .",'".$idCustomer."'";
							}
	
							$query4 = $query4 . ")";
							$q4 = mysqli_query($link1,$query4);
							if(mysqli_error($link1)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($link1);
								return json_encode($json);
							}
	
							$query12 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($link1,$records[$i]['container_id'])."','Container Created through SPEED ASN BUTTON','','',NOW(),'','','')";
							$q12 = mysqli_query($link1,$query12);
							if(mysqli_error($link1)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($link1);
								return json_encode($json);
							}
	
							//Start check If Pallet Item exists
							$query3 = "select id from pallet_items where palletId = '".mysqli_real_escape_string($link1,$records[$i]['container_id'])."' ";
							$q3 = mysqli_query($link1,$query3);
							if(mysqli_error($link1)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($link1);
								return json_encode($json);
							}
							if(mysqli_affected_rows($link1) > 0) {
								$row3 = mysqli_fetch_assoc($q3);
								$pallet_item_id = $row3['id'];
	
								$query5 = "update pallet_items set quantity = quantity + 1 where id = '".mysqli_real_escape_string($link1,$pallet_item_id)."'";
								$q5 = mysqli_query($link1,$query5);
								if(mysqli_error($link1)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($link1);
									return json_encode($json);
								}
							} else {
								$query4 = "insert into pallet_items (palletId,quantity,CreatedDate,Type) values ('".mysqli_real_escape_string($link1,$records[$i]['container_id'])."','1',NOW(),'SPEED ASN BUTTON')";
								$q4 = mysqli_query($link1,$query4);
								if(mysqli_error($link1)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($link1);
									return json_encode($json);
								}
								$pallet_item_id = mysqli_insert_id($link1);
							}
							//end check If Pallet Item exists
						}
					}
					//end check If Pallet exists
				}


				//Start loop through pallets in deman and for each pallet create asn
				if($LoadID != '') {
					$query = "select idPallet from pallets where LoadId = '".mysqli_real_escape_string($link1,$LoadID)."'";
					$q = mysqli_query($link1,$query);
					if(mysqli_error($link1)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($link1);
						return json_encode($json);
					}
					if(mysqli_affected_rows($link1) > 0) {
						while($row = mysqli_fetch_assoc($q)) {
							//Start create ASN
							$query16 = "select LoadId,idPallet,SerialNumber,UniversalModelNumber,`Type`,part_type from asn_assets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."'";
							$q16 = mysqli_query($this->connectionlink,$query16);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								while($row16 = mysqli_fetch_assoc($q16)) {
									$query17 = "insert into asn_assets (LoadId,idPallet,SerialNumber,UniversalModelNumber,CreatedDate,`Type`,part_type) values ('".mysqli_real_escape_string($link1,$row16['LoadId'])."','".mysqli_real_escape_string($link1,$row16['idPallet'])."','".mysqli_real_escape_string($link1,$row16['SerialNumber'])."','".mysqli_real_escape_string($link1,$row16['UniversalModelNumber'])."',NOW(),'".$row16['Type']."','".$row16['part_type']."')";
									$q17 = mysqli_query($link1,$query17);
									if(mysqli_error($link1)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($link1);
										return json_encode($json);
									}
								}
								
							}

						}
					}

				}
				//End loop through pallets in deman and for each pallet create asn
			}
			
	
			$json['Success'] = true;
			$json['Result'] = 'Created';
			return $json;
	
		} catch (Exception $e) {
			$json['Success'] = true;
			$json['Result'] = $e->getMessage();
			return $json;
		}
	}

	public function GetDBCurrentTime() {
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$query = "Select NOW()";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row['NOW()'];
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid";
		}
		return json_encode($json);
	}


	public function ApplyBusinessRule_old($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			$input = array();
			//Start get Input Information
			if($data['input_id'] > 0) {
				$query = "select wi.input,ER.EvaluationResult as input_type,wi.input_id from workflow_input wi LEFT JOIN EvaluationResult ER ON ER.EvaluationResultID=wi.input_type where wi.input_id = '" . mysqli_real_escape_string($this->connectionlink, $data['input_id']) . "'";
				
				/*$query = "select input,input_type,input_id from workflow_input where input_id = '".mysqli_real_escape_string($this->connectionlink,$data['input_id'])."'";*/
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					$input['Input'] = $row;
				}
			}
			//End get Input Information

			//Start get If serial is first time or not
			if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module
				$query1 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and AssetScanID != '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			} else {// If Asset is not created, generally in Receive Serial
				$query1 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			}			
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) { // Not first time
					$input['Internal']['first_arrival'] = 'No';
				} else { //First Time
					$input['Internal']['first_arrival'] = 'Yes';
				}
			}
			//End get If serial is first time or not

			//Start get If No Fleet Risk available
			/*$query1 = "select count(*) from fleet_risk_serials where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and fleet_risk = 'Yes'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) {
					$input['Internal']['no_fleet_risk'] = 'No';
				} else {
					$input['Internal']['no_fleet_risk'] = 'Yes';
				}
			}*/
			//End get If No Fleet Risk available

			//Start get Serial attributes like Fleet Risk
			$query1 = "select * from fleet_risk_serials where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if(($row1['fleet_risk']  == 'Yes') || ($row1['fleet_risk']  == 'YES')) {
					//$input['Internal']['no_fleet_risk'] = 'No';
					$input['Internal']['fleet_risk'] = 'Yes';
				} else {
					//$input['Internal']['no_fleet_risk'] = 'Yes';
					$input['Internal']['fleet_risk'] = 'No';
				}
				
				if(($row1['recovery_project']  == 'Yes') || ($row1['recovery_project']  == 'YES')) {
					$input['Internal']['recovery_project'] = 'Yes';	
				} else {
					$input['Internal']['recovery_project'] = 'No';	
				}

				if(($row1['special_handling']  == 'Yes') || ($row1['special_handling']  == 'YES')) {
					$input['Internal']['special_handling'] = 'Yes';	
				} else {
					$input['Internal']['special_handling'] = 'No';	
				}

				if(($row1['internal_use']  == 'Yes') || ($row1['internal_use']  == 'YES')) {
					$input['Internal']['internal_use'] = 'Yes';	
				} else {
					$input['Internal']['internal_use'] = 'No';	
				}
			} else {
				//$input['Internal']['no_fleet_risk'] = 'Yes';
				$input['Internal']['fleet_risk'] = 'No';
				$input['Internal']['recovery_project'] = 'No';
				$input['Internal']['special_handling'] = 'No';
				$input['Internal']['internal_use'] = 'No';
			}
			//End get Serial attributes like Fleet Risk


			//Start get If Warranty available
			/*$query1 = "select count(*) from asset_warranty where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and in_warranty = 'Yes'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) {
					$input['Internal']['in_warranty'] = 'Yes';
				} else {
					$input['Internal']['in_warranty'] = 'No';
				}
			}*/
			//End get If Warranty available


			//Start get Warranty Details
			$query1 = "select *,DATEDIFF(warranty_expiration_date, NOW()) as warranty_days from asset_warranty where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' ";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['warranty_days'] >= 0) {
					$input['Internal']['in_warranty'] = 'Yes';
				} else {
					$input['Internal']['in_warranty'] = 'No';
				}
			} else {
				$input['Internal']['in_warranty'] = 'No';
			}
			//End get Warranty Details

			//Start get PalletID from Serial
			if($data['idPallet'] == '') {
				if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module
					$query121 = "select idPallet from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				} else {// If Asset is not created, generally in Receive Serial
					$query121 = "select idPallet from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
				}			
				$q121 = mysqli_query($this->connectionlink,$query121);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row121 = mysqli_fetch_assoc($q121);
					$data['idPallet'] = $row121['idPallet'];
				} else {
					$data['idPallet'] = '';
				}
			}			
			//End get PalletID from Serial

			

			//Start get Source ID from pallet
			//$query1 = "select c.CustomerShotCode from pallets p,loads l,customer c where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and p.LoadId = l.LoadId and l.idCustomer = c.CustomerID ";
			//$query1 = "select c.CustomerShotCode,p.WasteClassificationType from pallets p,customer c where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and p.idCustomer = c.CustomerID ";
			$query1 = "select c.CustomerShotCode,p.WasteClassificationType,m.MaterialType,ct.Cumstomertype,a.Customer from pallets p 
			left join customer c on p.idCustomer = c.CustomerID 
			left join material_types m on p.MaterialTypeID = m.MaterialTypeID 
			left join aws_customers a on p.AWSCustomerID = a.AWSCustomerID 
			left join customertype ct on p.idCustomertype = ct.idCustomertype  
			where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$input['Internal']['CustomerShotCode'] = $row1['CustomerShotCode'];
				$input['Internal']['WasteClassificationType'] = $row1['WasteClassificationType'];
				$input['Internal']['MaterialType'] = $row1['MaterialType'];
				$input['Internal']['Cumstomertype'] = $row1['Cumstomertype'];
				$input['Internal']['Customer'] = $row1['Customer'];
			} else {
				$input['Internal']['CustomerShotCode'] = '';
				$input['Internal']['WasteClassificationType'] = '';
				$input['Internal']['MaterialType'] = '';
				$input['Internal']['Cumstomertype'] = '';
				$input['Internal']['Customer'] = '';
			}
			//End get Source ID from Pallet

			//Start get Source Type from pallet
			/*$query1 = "select ct.Cumstomertype from pallets p 
			left join loads l on p.LoadId = l.LoadId 
			left join customer c on l.idCustomer =  c.CustomerID 
			left join customertype ct on c.CustomerType = ct.idCustomertype 			
			where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'  ";*/

			// $query1 = "select ct.Cumstomertype from pallets p 			
			// left join customer c on p.idCustomer =  c.CustomerID 
			// left join customertype ct on c.CustomerType = ct.idCustomertype 			
			// where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'  ";

			// $q1 = mysqli_query($this->connectionlink,$query1);
			// if(mysqli_error($this->connectionlink)) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = mysqli_error($this->connectionlink);
			// 	return json_encode($json);
			// }
			// if(mysqli_affected_rows($this->connectionlink) > 0) {
			// 	$row1 = mysqli_fetch_assoc($q1);
			// 	$input['Internal']['Cumstomertype'] = $row1['Cumstomertype'];
			// } else {
			// 	$input['Internal']['Cumstomertype'] = '';
			// }			
			//End get Source Type from Pallet

			//Start get MPN Catalog Details
			//$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['mpn_id'])."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$data['apn_id'])."'";

			if($data['From'] == 'ReceiveSerial') {//Get Exact MPN
				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($data['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$data['UniversalModelNumber'] = $exact_mpn['MPN'];
					$json['ExactMPN'] = $exact_mpn['MPN'];
				}
				//End get exact MPN
			} else {
				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($data['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$data['UniversalModelNumber'] = $exact_mpn['MPN'];
					$json['ExactMPN'] = $exact_mpn['MPN'];
				}
				//End get exact MPN
			}

			//Start get APN
			if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module

				$query118 = "select apn_id from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
				$q118 = mysqli_query($this->connectionlink,$query118);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) { 
					$row118 = mysqli_fetch_assoc($q118);
					$APN = $row118['apn_id'];
				} else {
					$APN = '';
				}

			} else { // In Receive Serial Module
				if($data['ID'] > 0) {
					$query118 = "select apn_id from asn_assets where ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."' ";
					$q118 = mysqli_query($this->connectionlink,$query118);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) { 
						$row118 = mysqli_fetch_assoc($q118);
						$APN = $row118['apn_id'];
					} else {
						$APN = '';
					}
				} else {
					$query118 = "select apn_id from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
					$q118 = mysqli_query($this->connectionlink,$query118);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row118 = mysqli_fetch_assoc($q118);
						$APN = $row118['apn_id'];
					} else {
						$APN = '';
					}
				}
			}
			//End get APN

			//Start get daily extracted value
			$query1 = "select * from daily_received_summary where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."' and ReceivedDate = CURDATE()";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			} 
			if(mysqli_affected_rows($this->connectionlink) > 0) { 
				$row1 = mysqli_fetch_assoc($q1);
				$input['Internal']['daily_extracted_quantity'] = $row1['ReceivedCount'];
			} else {
				$input['Internal']['daily_extracted_quantity'] = 0;
			}
			//End get daily extracted value
			if($APN == '' || $APN == NULL || $APN == 'n/a') {
				$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
			} else {
				//$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
				$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
			}			
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If MPN Details available
				$row2 = mysqli_fetch_assoc($q2);
				$input['CatalogMPN'] = $row2;

				//Start get Sanitization type value file
				// if($row2['nd_sanitization_type'] != '') {
				// 	$query121 = "select file_url from business_rule_attribute_values where attribute_id = '7' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['nd_sanitization_type'])."'";
				// 	$q121 = mysqli_query($this->connectionlink,$query121);
				// 	if(mysqli_error($this->connectionlink)) {	
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row121 = mysqli_fetch_assoc($q121);
				// 		$row2['nd_sanitization_type_file_url'] = $row121['file_url'];
				// 	} else {
				// 		$row2['nd_sanitization_type_file_url'] = '';
				// 	}
				// } else {
				// 	$row2['nd_sanitization_type_file_url'] = '';
				// }			
				//End get Sanitization type value file

				//Start get Sanitization type
				if($data['AssetScanID'] > 0) {
					$query1231 = "select a.*,b.rule_name as nd_sanitization_type from asset a 					
					left join business_rule b on a.RecentDispositionRuleID = b.rule_id
					where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
					$q1231 = mysqli_query($this->connectionlink,$query1231);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1231 = mysqli_fetch_assoc($q1231);
						$row2['nd_sanitization_type'] = $row1231['nd_sanitization_type'];
					} else {
						$row2['nd_sanitization_type'] = '';
						$row2['CSPLINK'] = '';
					}
				} else {
					$row2['nd_sanitization_type'] = '';
					$row2['CSPLINK'] = '';
				}
				//End get Sanitization type

				//Start get Sanitization type value from Procedure id column				
				if($row2['procedure_id'] != '' && $row2['nd_sanitization_type'] != '') {
					$row2['CSPLINK'] = '';
					$procedure_id = $row2['procedure_id'];
					$procedure_array = explode('$', $procedure_id);					
					$san_contains = false;
					for($k=0;$k<count($procedure_array);$k++) {
						if(strpos($procedure_array[$k], $row2['nd_sanitization_type']) !== false) {
							$links_array = explode('*', $procedure_array[$k]);
							if(count($links_array) > 0) {
								$san_contains = true;								
								$row2['CSPLINK'] = $links_array[1];
							}
						}
					}					
				} else {
					//$row2['nd_sanitization_type_file_url'] = '';
					//$row2['nd_sanitization_type'] = '';
					$row2['CSPLINK'] = '';
				}
				//End get Sanitization type value from Procedure id column

				//Start get Sanitization type value file
				// if($row2['nd_sanitization_type'] != '') {
				// 	$query121 = "select file_url from business_rule_attribute_values where attribute_id = '7' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['nd_sanitization_type'])."'";
				// 	$q121 = mysqli_query($this->connectionlink,$query121);
				// 	if(mysqli_error($this->connectionlink)) {	
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row121 = mysqli_fetch_assoc($q121);
				// 		$row2['nd_sanitization_type_file_url'] = $row121['file_url'];
				// 	} else {
				// 		$row2['nd_sanitization_type_file_url'] = '';
				// 	}
				// } else {
				// 	$row2['nd_sanitization_type_file_url'] = '';
				// }			
				//End get Sanitization type value file

				//Start get Harvest type value file
				// if($row2['harvest_type'] != '') {
				// 	$query121 = "select file_url from business_rule_attribute_values where attribute_id = '9' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['harvest_type'])."'";
				// 	$q121 = mysqli_query($this->connectionlink,$query121);
				// 	if(mysqli_error($this->connectionlink)) {	
				// 		$json['Success'] = false;
				// 		$json['Result'] = mysqli_error($this->connectionlink);
				// 		return json_encode($json);
				// 	}
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row121 = mysqli_fetch_assoc($q121);
				// 		$row2['harvest_type_file_url'] = $row121['file_url'];
				// 	} else {
				// 		$row2['harvest_type_file_url'] = '';
				// 	}
				// } else {
				// 	$row2['harvest_type_file_url'] = '';
				// }			
				//End get Harvest type value file

				//Start get Repair type value file
				if($row2['RepairType'] != '') {
					$query121 = "select file_url from business_rule_attribute_values where attribute_id = '22' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['RepairType'])."'";
					$q121 = mysqli_query($this->connectionlink,$query121);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row121 = mysqli_fetch_assoc($q121);
						$row2['RepairType_file_url'] = $row121['file_url'];
					} else {
						$row2['RepairType_file_url'] = '';
					}
				} else {
					$row2['RepairType_file_url'] = '';
				}			
				//End get Repair type value file

				$json['MPN'] = $row2;
			} else { // MPN Details are missing
				$json['Success'] = false;
				$json['Result'] = 'MPN Details not available';
				return json_encode($json);
			}
			//End get MPN Catalog Details

			// $json['Success'] = false;
			// $json['Result'] = $input;
			// $json['Query'] = $query1;
			// return json_encode($json);
			
			//Start Get Rules
			//$query3 = "select r.*,d.disposition from business_rule r,disposition d where r.disposition_id = d.disposition_id and r.FacilityID = '".$_SESSION['user']['FacilityID']."' order by r.priority";
			$query3 = "select r.*,d.disposition,sd.disposition as sub_disposition from business_rule r 
			left join disposition d on r.disposition_id = d.disposition_id 
			left join disposition sd on r.sub_disposition_id = sd.disposition_id 
			where r.FacilityID = '".$_SESSION['user']['FacilityID']."' and r.status = 'Active' order by r.priority";

			// $query3 = "select r.*,d.disposition,sd.disposition as sub_disposition,d.color_code,d.color,sd.color_code as sub_color_code,sd.color as sub_color from business_rule r 
			// left join disposition d on r.disposition_id = d.disposition_id 
			// left join disposition sd on r.sub_disposition_id = sd.disposition_id  
			// left join business_rule_facilities rf  on r.rule_id = rf.rule_id 
			// left join business_rule_versions rv on r.version_id = rv.version_id 
			// where rf.FacilityID = '".$_SESSION['user']['FacilityID']."' and r.status = 'Active' and rv.current_version = '1' order by r.priority";

			$query3 = "select r.*,d.disposition,sd.disposition as sub_disposition,d.color_code,d.color,sd.color_code as sub_color_code,sd.color as sub_color from business_rule r 
			left join disposition d on r.disposition_id = d.disposition_id 
			left join disposition sd on r.sub_disposition_id = sd.disposition_id  			
			left join business_rule_versions rv on r.version_id = rv.version_id 
			where r.status = 'Active' and rv.current_version = '1' order by r.priority";

			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If Rules Defined
				while($rule = mysqli_fetch_assoc($q3)) { // Loop through Rules
					//Start check If input_id matches
					$input_passed = true;
					$conditions_passed = true;

					// $json['Success'] = false;
					// $json['Result'] = $rule['input_id']."##".$input['Input']['input_id'];
					// return json_encode($json);

					if(($rule['input_id'] > 0) && ($rule['input_id'] != $input['Input']['input_id'])) {
						$input_passed = false;
						continue; //Look for new Rule
					}
					//End check If input_id matched

					//Start get Rule Conditions
					$query4 = "select c.*,a.attribute_id,a.attribute_name,a.attribute_type,a.field_name from business_rule_condition c,business_rule_attributes a where c.attribute_id = a.attribute_id and c.rule_id = '".mysqli_real_escape_string($this->connectionlink,$rule['rule_id'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) { //If Conditions Defined
						while($condition = mysqli_fetch_assoc($q4)) { // Loop through Rule Conditions
							if($condition['multiple_values'] == 0) {
								if($condition['value'] == 'BRE_sort_quantity') {
									$condition['value'] = $input['CatalogMPN']['BRE_sort_quantity'];
								}
								if($condition['operator'] == '==') {
									if($condition['attribute_id'] == '16') {//Part Type Attribute, ignore case sensitivity
										if(strcasecmp($condition['value'], $input[$condition['attribute_type']][$condition['field_name']]) != 0) {
											$conditions_passed = false;
										}
									} else if(! ($condition['value'] == $input[$condition['attribute_type']][$condition['field_name']])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '!=') {
									if(! ($condition['value'] != $input[$condition['attribute_type']][$condition['field_name']])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '>') {
									if(! ($input[$condition['attribute_type']][$condition['field_name']] > $condition['value'])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '<') {									
									if(! ($input[$condition['attribute_type']][$condition['field_name']] < $condition['value'])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == 'contains') {									
									if(! strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $condition['value'])) {
										$conditions_passed = false;
									}
								} else { // not_contains
									if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $condition['value'])) {
										$conditions_passed = false;
									}
								}
							} else {
								$multiple_values = explode('@#$', $condition['value']);
								if($condition['operator'] == '==') {
									if(! in_array($input[$condition['attribute_type']][$condition['field_name']], $multiple_values)){
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '!=') {
									if(in_array($input[$condition['attribute_type']][$condition['field_name']], $multiple_values)){
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '>') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(($input[$condition['attribute_type']][$condition['field_name']] > $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '<') {							
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}										
										if(($input[$condition['attribute_type']][$condition['field_name']] < $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == 'contains') {																		
									$satisfied = false;									
									for($i=0;$i<count($multiple_values);$i++) {		
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}																	
										if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else { // not_contains
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {	
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}							
										if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $multiple_values[$i])) {
											$satisfied = true;
										}
									}									
									if($satisfied == true) {
										$conditions_passed = false;
									}
								}
							}
						}
					}
					//End get Rule Conditions
				if ($data['workflow_id'] == 10 && $input_passed &&	$conditions_passed) {
					$json['Success'] = true;
					$json['disposition'] = $rule['disposition'];
					$json['disposition_id'] = $rule['disposition_id'];
					$json['rule_id'] = $rule['rule_id'];
					return json_encode($json);
				} else if($input_passed &&	$conditions_passed ) {//Rule Satisfied
						//Start get Bin Details
						$query5 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m 
						left join custompallet cp on m.CustomPalletID = cp.CustomPalletID 
						where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."'";
						$q5 = mysqli_query($this->connectionlink,$query5);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
							$row5 = mysqli_fetch_assoc($q5);
							$json['CustomPalletID'] = $row5['CustomPalletID'];
							$json['BinName'] = $row5['BinName'];
						}
						//End get Bin Details
						if($rule['sub_disposition'] == NULL) {
							$rule['sub_disposition'] = 'n/a';
						}
						if($rule['sub_disposition_id'] > 0) {
							$query56 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m 
							left join custompallet cp on m.CustomPalletID = cp.CustomPalletID 
							where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['sub_disposition_id'])."'";
							$q56 = mysqli_query($this->connectionlink,$query56);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row56 = mysqli_fetch_assoc($q56);
								$json['Sub_CustomPalletID'] = $row56['CustomPalletID'];
								$json['Sub_BinName'] = $row56['BinName'];
							}
						}

						$json['Success'] = true;
						$json['Result'] = $rule;
						return json_encode($json);
					}
				}

				$json['Success'] = false;
				$json['Result'] = 'No Rules Satisfied';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Business Rules Defined';
				return json_encode($json);
			}
			//End get Rules

			$json['Success'] = false;
			$json['Result'] = $input;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}	
	}


	public function ApplyBusinessRule($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			$input = array();

			//Start get COO ID
			if($data['COOID'] > 0) {
				$input['Internal']['COOID'] = $data['COOID'];
			} else {
				$input['Internal']['COOID'] = '';
			}
			//End get COO ID

			//Start get Region Information
			$query119 = "select r.RegionName from facility f left join region r on f.Region = r.Region where f.FacilityID = '".$_SESSION['user']['FacilityID']."'";
			$q119 = mysqli_query($this->connectionlink,$query119);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row119 = mysqli_fetch_assoc($q119);
				$input['Internal']['RegionName'] = $row119['RegionName'];
			} else {
				$input['Internal']['RegionName'] = '';
			}

			//End get Region Information

			//Start get If serial is first time or not
			if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module
				$query1 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and AssetScanID != '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			} else {// If Asset is not created, generally in Receive Serial
				$query1 = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			}
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$count = (int)$row1['count(*)'];

				if($count == 0) { // First Time
					$input['Internal']['first_arrival'] = 'Yes';
					$input['Internal']['second_arrival'] = 'No';
					$input['Internal']['third_arrival'] = 'No';
				} else if($count == 1) { // Second Time
					$input['Internal']['first_arrival'] = 'No';
					$input['Internal']['second_arrival'] = 'Yes';
					$input['Internal']['third_arrival'] = 'No';
				} else { // Third Time or More (count >= 2)
					$input['Internal']['first_arrival'] = 'No';
					$input['Internal']['second_arrival'] = 'No';
					$input['Internal']['third_arrival'] = 'Yes';
				}
			}
			//End get If serial is first time or not

			//Start get Serial attributes like Fleet Risk
			$query1 = "select * from fleet_risk_serials where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if(($row1['fleet_risk']  == 'Yes') || ($row1['fleet_risk']  == 'YES')) {
					//$input['Internal']['no_fleet_risk'] = 'No';
					$input['Internal']['fleet_risk'] = 'Yes';
				} else {
					//$input['Internal']['no_fleet_risk'] = 'Yes';
					$input['Internal']['fleet_risk'] = 'No';
				}

				if(($row1['recovery_project']  == 'Yes') || ($row1['recovery_project']  == 'YES')) {
					$input['Internal']['recovery_project'] = 'Yes';
				} else {
					$input['Internal']['recovery_project'] = 'No';
				}

				if(($row1['special_handling']  == 'Yes') || ($row1['special_handling']  == 'YES')) {
					$input['Internal']['special_handling'] = 'Yes';
				} else {
					$input['Internal']['special_handling'] = 'No';
				}

				if(($row1['internal_use']  == 'Yes') || ($row1['internal_use']  == 'YES')) {
					$input['Internal']['internal_use'] = 'Yes';
				} else {
					$input['Internal']['internal_use'] = 'No';
				}
			} else {
				//$input['Internal']['no_fleet_risk'] = 'Yes';
				$input['Internal']['fleet_risk'] = 'No';
				$input['Internal']['recovery_project'] = 'No';
				$input['Internal']['special_handling'] = 'No';
				$input['Internal']['internal_use'] = 'No';
			}
			//End get Serial attributes like Fleet Risk

			//Start get Warranty Details
			$query1 = "select *,DATEDIFF(warranty_expiration_date, NOW()) as warranty_days from asset_warranty where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' ";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['warranty_days'] >= 0) {
					$input['Internal']['in_warranty'] = 'Yes';
				} else {
					$input['Internal']['in_warranty'] = 'No';
				}
			} else {
				$input['Internal']['in_warranty'] = 'No';
			}
			//End get Warranty Details

			//Start get PalletID from Serial
			if($data['idPallet'] == '') {
				if($data['AssetScanID'] > 0) { // If Asset is already created, generally in Services module
					$query121 = "select idPallet from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				} else {// If Asset is not created, generally in Receive Serial
					$query121 = "select idPallet from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
				}
				$q121 = mysqli_query($this->connectionlink,$query121);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row121 = mysqli_fetch_assoc($q121);
					$data['idPallet'] = $row121['idPallet'];
				} else {
					$data['idPallet'] = '';
				}
			}
			//End get PalletID from Serial



			//Start get Source ID from pallet
			if($data['UnknownidCustomer'] > 0) {
				$query1 = "select c.CustomerShotCode from customer c where c.CustomerID = '".mysqli_real_escape_string($this->connectionlink,$data['UnknownidCustomer'])."' ";
			} else {
				$query1 = "select c.CustomerShotCode,p.WasteClassificationType,m.MaterialType,ct.Cumstomertype,a.Customer from pallets p
				left join customer c on p.idCustomer = c.CustomerID
				left join material_types m on p.MaterialTypeID = m.MaterialTypeID
				left join aws_customers a on p.AWSCustomerID = a.AWSCustomerID
				left join customertype ct on p.idCustomertype = ct.idCustomertype
				where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' ";
			}
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($data['UnknownidCustomer'] > 0) {
					$input['Internal']['CustomerShotCode'] = $row1['CustomerShotCode'];
					$input['Internal']['WasteClassificationType'] = 'n/a';
					$input['Internal']['MaterialType'] = $data['MaterialType'];
					$input['Internal']['Cumstomertype'] = '';
					$input['Internal']['Customer'] = '';
				} else {
					$input['Internal']['CustomerShotCode'] = $row1['CustomerShotCode'];
					$input['Internal']['WasteClassificationType'] = $row1['WasteClassificationType'];
					$input['Internal']['MaterialType'] = $row1['MaterialType'];
					$input['Internal']['Cumstomertype'] = $row1['Cumstomertype'];
					$input['Internal']['Customer'] = $row1['Customer'];
				}
			} else {
				$input['Internal']['CustomerShotCode'] = '';
				$input['Internal']['WasteClassificationType'] = '';
				$input['Internal']['MaterialType'] = '';
				$input['Internal']['Cumstomertype'] = '';
				$input['Internal']['Customer'] = '';
			}
			//End get Source ID from Pallet

			if($data['From'] == 'ReceiveSerial') {//Get Exact MPN
				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($data['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$data['UniversalModelNumber'] = $exact_mpn['MPN'];
					$json['ExactMPN'] = $exact_mpn['MPN'];
				}
				//End get exact MPN
			} else {
				//Statt get exact MPN
				$exact_mpn = $this->GetExactMPN($data['UniversalModelNumber']);
				if($exact_mpn['Success'] == true) {
					$data['UniversalModelNumber'] = $exact_mpn['MPN'];
					$json['ExactMPN'] = $exact_mpn['MPN'];
				}
				//End get exact MPN
			}

			//Start get daily extracted value
			$query1 = "select * from daily_received_summary where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' and apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."' and ReceivedDate = CURDATE()";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$input['Internal']['daily_extracted_quantity'] = $row1['ReceivedCount'];
			} else {
				$input['Internal']['daily_extracted_quantity'] = 0;
			}
			//End get daily extracted value
			$query2 = "select * from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If MPN Details available
				$row2 = mysqli_fetch_assoc($q2);
				$input['CatalogMPN'] = $row2;

				//Start get Sanitization type
				if($data['AssetScanID'] > 0) {
					$query1231 = "select a.*,b.rule_name as nd_sanitization_type from asset a
					left join business_rule b on a.RecentDispositionRuleID = b.rule_id
					where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
					$q1231 = mysqli_query($this->connectionlink,$query1231);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1231 = mysqli_fetch_assoc($q1231);
						$row2['nd_sanitization_type'] = $row1231['nd_sanitization_type'];
					} else {
						$row2['nd_sanitization_type'] = '';
						$row2['CSPLINK'] = '';
					}
				} else {
					$row2['nd_sanitization_type'] = '';
					$row2['CSPLINK'] = '';
				}
				//End get Sanitization type

				//Start get Sanitization type value from Procedure id column
				if($row2['procedure_id'] != '' && $row2['nd_sanitization_type'] != '') {
					$row2['CSPLINK'] = '';
					$procedure_id = $row2['procedure_id'];
					$procedure_array = explode('$', $procedure_id);
					$san_contains = false;
					for($k=0;$k<count($procedure_array);$k++) {
						if(strpos($procedure_array[$k], $row2['nd_sanitization_type']) !== false) {
							$links_array = explode('*', $procedure_array[$k]);
							if(count($links_array) > 0) {
								$san_contains = true;
								$row2['CSPLINK'] = $links_array[1];
							}
						}
					}
				} else {
					//$row2['nd_sanitization_type_file_url'] = '';
					//$row2['nd_sanitization_type'] = '';
					$row2['CSPLINK'] = '';
				}
				//End get Sanitization type value from Procedure id column

				//Start get Repair type value file
				if($row2['RepairType'] != '') {
					$query121 = "select file_url from business_rule_attribute_values where attribute_id = '22' and value = '".mysqli_real_escape_string($this->connectionlink,$row2['RepairType'])."'";
					$q121 = mysqli_query($this->connectionlink,$query121);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row121 = mysqli_fetch_assoc($q121);
						$row2['RepairType_file_url'] = $row121['file_url'];
					} else {
						$row2['RepairType_file_url'] = '';
					}
				} else {
					$row2['RepairType_file_url'] = '';
				}
				//End get Repair type value file

				$json['MPN'] = $row2;
			} else { // MPN Details are missing
				$json['Success'] = false;
				$json['Result'] = 'MPN Details not available';
				return json_encode($json);
			}
			//End get MPN Catalog Details
			if($data['UnknownidCustomer'] > 0) {//Unknown Recovery
				//Forunknown Recovery the 3 fields are sent as input
			} else if($data['idPallet'] != '') {//Start get AWSCustomerID, Source Type, Material Type
				$query118 = "select MaterialType,AWSCustomerID,idCustomertype from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
				$q118 = mysqli_query($this->connectionlink,$query118);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row118 = mysqli_fetch_assoc($q118);
					$data['AWSCustomerID'] = $row118['AWSCustomerID'];
					$data['MaterialType'] = $row118['MaterialType'];
					$data['idCustomertype'] = $row118['idCustomertype'];
				} else {
					$data['AWSCustomerID'] = '';
					$data['MaterialType'] = '';
					$data['idCustomertype'] = '';
				}
			} else {
				$data['AWSCustomerID'] = '';
				$data['MaterialType'] = '';
				$data['idCustomertype'] = '';
			}

			// $json['Success'] = false;
			// $json['Result'] = $data;
			// $json['Query'] = $query1;
			// return json_encode($json);

			// $json['Success'] = false;
			// $json['Result'] = $input;
			// $json['Query'] = $query1;
			// return json_encode($json);

			//Start Get Rules
			$where_conditions = array();
			$where_conditions[] = "r.status = 'Active'";
			$where_conditions[] = "rv.current_version = '1'";
			//$where_conditions[] = "rv.version_id = '34'";

			// Add input_id filtering
			if(isset($data['input_id']) && $data['input_id'] > 0) {
				$where_conditions[] = "(r.input_id = '' OR r.input_id = '0' OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $data['input_id'])."', r.input_id) > 0)";
			}

			// Add Customer ID filtering - Updated to handle comma-separated Customer IDs in database
			if(isset($data['AWSCustomerID']) && $data['AWSCustomerID'] > 0) {
				// Input AWSCustomerID is a single integer, but database rules may have comma-separated values
				$customer_condition = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All'";
				$customer_condition .= " OR r.AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink, $data['AWSCustomerID'])."'";
				$customer_condition .= " OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $data['AWSCustomerID'])."', r.AWSCustomerID) > 0";
				$customer_condition .= ")";
				$where_conditions[] = $customer_condition;
			} else {
				// If AWSCustomerID is 0, empty, or not set - only check for All rules
				$where_conditions[] = "(r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All')";
			}

			// Add Facility filtering
			if(isset($_SESSION['user']['FacilityID'])) {
				$where_conditions[] = "(r.FacilityID = 'all' OR r.FacilityID = 'All' OR r.FacilityID = '".mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['FacilityID'])."')";
			}

			// Add Workflow filtering
			if(isset($data['workflow_id'])) {
				$where_conditions[] = "(r.workflow_id = 'all' OR r.workflow_id = 'All' OR r.workflow_id = '".mysqli_real_escape_string($this->connectionlink, $data['workflow_id'])."')";
			}

			// Add Part Type filtering using parttypeid
			$part_type_name = '';
			if(isset($data['parttypeid']) && $data['parttypeid'] > 0) {
				$parttype_query = "SELECT parttype FROM parttype WHERE parttypeid = '".mysqli_real_escape_string($this->connectionlink, $data['parttypeid'])."'";
				$parttype_result = mysqli_query($this->connectionlink, $parttype_query);
				if($parttype_result && mysqli_num_rows($parttype_result) > 0) {
					$parttype_row = mysqli_fetch_assoc($parttype_result);
					$part_type_name = $parttype_row['parttype'];
					$where_conditions[] = "(r.part_types = 'all' OR r.part_types = 'All' OR FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink, $part_type_name)."', r.part_types) > 0)";
				}
			}

			// Add Source Type filtering
			if($data['idCustomertype'] > 0) {
				$where_conditions[] = "(r.idCustomertype = 'all' OR r.idCustomertype = 'All' OR r.idCustomertype = '".mysqli_real_escape_string($this->connectionlink, $data['idCustomertype'])."')";
			} else {
				$where_conditions[] = "(r.idCustomertype = 'all' OR r.idCustomertype = 'All')";
			}

			// Add Material Type filtering
			if(isset($data['MaterialType'])) {
				$where_conditions[] = "(r.MaterialType = 'all' OR r.MaterialType = 'All' OR r.MaterialType = '".mysqli_real_escape_string($this->connectionlink, $data['MaterialType'])."')";
			}

			// Get filtered business rules ordered by priority
			$rules_query = "SELECT r.*, d.disposition, d.color_code, d.color
							FROM business_rule r
							LEFT JOIN disposition d ON r.disposition_id = d.disposition_id
							LEFT JOIN business_rule_versions rv ON r.version_id = rv.version_id
							WHERE " . implode(' AND ', $where_conditions) . "
							ORDER BY r.priority ASC";

			$rules_result = mysqli_query($this->connectionlink, $rules_query);

			// $json['Result'] = $rules_query;
			// $json['Success'] = false;
			// $json['Result'] = $rules_query;
			// return json_encode($json);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) { //If Rules Defined
				while($rule = mysqli_fetch_assoc($rules_result)) { // Loop through Rules
					//Start check If input_id matches
					$input_passed = true;
					$conditions_passed = true;

					//Start get Rule Conditions
					$query4 = "select c.*,a.attribute_id,a.attribute_name,a.attribute_type,a.field_name from business_rule_condition c,business_rule_attributes a where c.attribute_id = a.attribute_id and c.rule_id = '".mysqli_real_escape_string($this->connectionlink,$rule['rule_id'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) { //If Conditions Defined
						while($condition = mysqli_fetch_assoc($q4)) { // Loop through Rule Conditions
							if($condition['multiple_values'] == 0) {
								if($condition['value'] == 'BRE_sort_quantity') {
									$condition['value'] = $input['CatalogMPN']['BRE_sort_quantity'];
								}
								if($condition['operator'] == '==') {
									if($condition['attribute_id'] == '16') {//Part Type Attribute, ignore case sensitivity
										if(strcasecmp($condition['value'], $input[$condition['attribute_type']][$condition['field_name']]) != 0) {
											$conditions_passed = false;
										}
									} else if(! ($condition['value'] == $input[$condition['attribute_type']][$condition['field_name']])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '!=') {
									if(! ($condition['value'] != $input[$condition['attribute_type']][$condition['field_name']])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '>') {
									if(! ($input[$condition['attribute_type']][$condition['field_name']] > $condition['value'])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '<') {
									if(! ($input[$condition['attribute_type']][$condition['field_name']] < $condition['value'])) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == 'contains') {
									if(! strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $condition['value'])) {
										$conditions_passed = false;
									}
								} else { // not_contains
									if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $condition['value'])) {
										$conditions_passed = false;
									}
								}
							} else {
								$multiple_values = explode('@#$', $condition['value']);
								if($condition['operator'] == '==') {
									if(! in_array($input[$condition['attribute_type']][$condition['field_name']], $multiple_values)){
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '!=') {
									if(in_array($input[$condition['attribute_type']][$condition['field_name']], $multiple_values)){
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '>') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(($input[$condition['attribute_type']][$condition['field_name']] > $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == '<') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(($input[$condition['attribute_type']][$condition['field_name']] < $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else if($condition['operator'] == 'contains') {
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == false) {
										$conditions_passed = false;
									}
								} else { // not_contains
									$satisfied = false;
									for($i=0;$i<count($multiple_values);$i++) {
										if($multiple_values[$i] == 'BRE_sort_quantity') {
											$multiple_values[$i] = $input['CatalogMPN']['BRE_sort_quantity'];
										}
										if(strpos("123".$input[$condition['attribute_type']][$condition['field_name']], $multiple_values[$i])) {
											$satisfied = true;
										}
									}
									if($satisfied == true) {
										$conditions_passed = false;
									}
								}
							}
						}
					}
					//End get Rule Conditions
					if($input_passed &&	$conditions_passed && $data['workflow_id'] == '20') { // Parts Sort




						// get workstation group id of the work station
						$getWorkstationGroupQ = "select GroupID from WorkstationConfiguration_mapping where SiteID='".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."'";
						$getWorkstationGroupQEx = mysqli_query($this->connectionlink,$getWorkstationGroupQ);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						$GroupID = 0;
						if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
							$WorkstationGroup = mysqli_fetch_assoc($getWorkstationGroupQEx);
							$GroupID = $WorkstationGroup['GroupID'];
						} else {
							$json['Success'] = false;
							$json['Result'] = "No workstation Group available";
							return json_encode($json);
						}
						// get COO
						if(isset($data['COOID']) && $data['COOID'] != 'All'){
							$cooQuery = "select * from COO where COOID='".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."'";
							$cooQueryEx = mysqli_query($this->connectionlink,$cooQuery);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$CooRow = mysqli_fetch_assoc($cooQueryEx);
								$data['COO'] = $CooRow['COO'];
							} else {
								$json['Success'] = false;
								$json['Result'] = "No COO Data available";
								return json_encode($json);
							}
						}

						// get Bin for sort configuration based on sort validation key
						//Start get BIN Details for Parts Sort Page
						if($data['SortCriteriaID'] == '1') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 1";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$json['NoMapping'] = '1';
								$json['Success'] = true;
								$json['Result'] = "No Mapping Bin Available";
								return json_encode($json);
							}
						} else if($data['SortCriteriaID'] == '2') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and LOWER(m.mpn_id) = LOWER('".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."') and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 2";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 2";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '3') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 3";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 3";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '4') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 4";
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 4";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$json['NoMapping'] = '1';
									$json['Success'] = true;
									$json['Result'] = "No Mapping Bin Available";
									return json_encode($json);
								}
							}
						} else if($data['SortCriteriaID'] == '5') {
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 5";
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = '".mysqli_real_escape_string($this->connectionlink,$data['part_spec_id'])."' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 5";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 5";
									$q5 = mysqli_query($this->connectionlink,$query5);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
										$row5 = mysqli_fetch_assoc($q5);
										if($row5['AuditLocked'] == '1'){
											$json['Success'] = false;
											$json['Result'] = "BIN is locked for Audit";
											return json_encode($json);
										}
										$json['CustomPalletID'] = $row5['CustomPalletID'];
										$json['BinName'] = $row5['BinName'];
										$json['LocationID'] = $row5['LocationID'];
										$json['LocationName'] = $row5['LocationName'];
										$json['AssetsCount'] = $row5['AssetsCount'];
										$json['MaximumAssets'] = $row5['MaximumAssets'];
										$json['sortconfigurationid'] = $row5['sortconfigurationid'];
									}else{
										$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 5";
										$q5 = mysqli_query($this->connectionlink,$query5);
										if(mysqli_error($this->connectionlink)) {
											$json['Success'] = false;
											$json['Result'] = mysqli_error($this->connectionlink);
											return json_encode($json);
										}
										if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
											$row5 = mysqli_fetch_assoc($q5);
											if($row5['AuditLocked'] == '1'){
												$json['Success'] = false;
												$json['Result'] = "BIN is locked for Audit";
												return json_encode($json);
											}
											$json['CustomPalletID'] = $row5['CustomPalletID'];
											$json['BinName'] = $row5['BinName'];
											$json['LocationID'] = $row5['LocationID'];
											$json['LocationName'] = $row5['LocationName'];
											$json['AssetsCount'] = $row5['AssetsCount'];
											$json['MaximumAssets'] = $row5['MaximumAssets'];
											$json['sortconfigurationid'] = $row5['sortconfigurationid'];
										}else{
											$json['NoMapping'] = '1';
											$json['Success'] = true;
											$json['Result'] = "No Mapping Bin Available";
											return json_encode($json);
										}
									}
								}
							}
						} else if($data['SortCriteriaID'] == '6'){
							$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 6";
							//echo $query5;exit;
							$q5 = mysqli_query($this->connectionlink,$query5);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row5 = mysqli_fetch_assoc($q5);
								if($row5['AuditLocked'] == '1'){
									$json['Success'] = false;
									$json['Result'] = "BIN is locked for Audit";
									return json_encode($json);
								}
								$json['CustomPalletID'] = $row5['CustomPalletID'];
								$json['BinName'] = $row5['BinName'];
								$json['LocationID'] = $row5['LocationID'];
								$json['LocationName'] = $row5['LocationName'];
								$json['AssetsCount'] = $row5['AssetsCount'];
								$json['MaximumAssets'] = $row5['MaximumAssets'];
								$json['sortconfigurationid'] = $row5['sortconfigurationid'];
							}else{
								$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 6";
								$q5 = mysqli_query($this->connectionlink,$query5);
								if(mysqli_error($this->connectionlink)) {
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
									$row5 = mysqli_fetch_assoc($q5);
									if($row5['AuditLocked'] == '1'){
										$json['Success'] = false;
										$json['Result'] = "BIN is locked for Audit";
										return json_encode($json);
									}
									$json['CustomPalletID'] = $row5['CustomPalletID'];
									$json['BinName'] = $row5['BinName'];
									$json['LocationID'] = $row5['LocationID'];
									$json['LocationName'] = $row5['LocationName'];
									$json['AssetsCount'] = $row5['AssetsCount'];
									$json['MaximumAssets'] = $row5['MaximumAssets'];
									$json['sortconfigurationid'] = $row5['sortconfigurationid'];
								}else{
									$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = '".mysqli_real_escape_string($this->connectionlink,$data['COO'])."' and m.Status=1 and m.SortCriteriaID = 6";
									$q5 = mysqli_query($this->connectionlink,$query5);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
										$row5 = mysqli_fetch_assoc($q5);
										if($row5['AuditLocked'] == '1'){
											$json['Success'] = false;
											$json['Result'] = "BIN is locked for Audit";
											return json_encode($json);
										}
										$json['CustomPalletID'] = $row5['CustomPalletID'];
										$json['BinName'] = $row5['BinName'];
										$json['LocationID'] = $row5['LocationID'];
										$json['LocationName'] = $row5['LocationName'];
										$json['AssetsCount'] = $row5['AssetsCount'];
										$json['MaximumAssets'] = $row5['MaximumAssets'];
										$json['sortconfigurationid'] = $row5['sortconfigurationid'];
									}else{
										$query5 = "select m.CustomPalletID,m.BinName,m.sortconfigurationid,cp.LocationID,l.LocationName,cp.AuditLocked,cp.AssetsCount,cp.MaximumAssets from sortconfiguration m LEFT JOIN custompallet cp ON m.CustomPalletID=cp.CustomPalletID LEFT JOIN location l ON cp.LocationID=l.LocationID  where m.FacilityID = '".$_SESSION['user']['FacilityID']."' and m.GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."' and m.mpn_id = 'All' and m.part_spec_id = 'All' and m.COO = 'All' and m.Status=1 and m.SortCriteriaID = 6";
										$q5 = mysqli_query($this->connectionlink,$query5);
										if(mysqli_error($this->connectionlink)) {
											$json['Success'] = false;
											$json['Result'] = mysqli_error($this->connectionlink);
											return json_encode($json);
										}
										if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
											$row5 = mysqli_fetch_assoc($q5);
											if($row5['AuditLocked'] == '1'){
												$json['Success'] = false;
												$json['Result'] = "BIN is locked for Audit";
												return json_encode($json);
											}
											$json['CustomPalletID'] = $row5['CustomPalletID'];
											$json['BinName'] = $row5['BinName'];
											$json['LocationID'] = $row5['LocationID'];
											$json['LocationName'] = $row5['LocationName'];
											$json['AssetsCount'] = $row5['AssetsCount'];
											$json['MaximumAssets'] = $row5['MaximumAssets'];
											$json['sortconfigurationid'] = $row5['sortconfigurationid'];
										}else{
											$json['NoMapping'] = '1';
											$json['Success'] = true;
											$json['Result'] = "No Mapping Bin Available";
											return json_encode($json);
										}
									}
								}
							}
						}
						//End get BIN Details for Parts Sort Page

						//End get Bin Details
						if($rule['sub_disposition'] == NULL) {
							$rule['sub_disposition'] = 'n/a';
						}
						$json['Success'] = true;
						$json['Result'] = $rule;
						return json_encode($json);

					} else if($input_passed &&	$conditions_passed ) {//Rule Satisfied
						//Start get Bin Details
						$query5 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m
						left join custompallet cp on m.CustomPalletID = cp.CustomPalletID
						where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['disposition_id'])."'";
						$q5 = mysqli_query($this->connectionlink,$query5);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
							$row5 = mysqli_fetch_assoc($q5);
							$json['CustomPalletID'] = $row5['CustomPalletID'];
							$json['BinName'] = $row5['BinName'];
						}
						//End get Bin Details
						if($rule['sub_disposition'] == NULL) {
							$rule['sub_disposition'] = 'n/a';
						}
						if($rule['sub_disposition_id'] > 0) {
							$query56 = "select m.CustomPalletID,cp.BinName from station_custompallet_mapping m
							left join custompallet cp on m.CustomPalletID = cp.CustomPalletID
							where m.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and m.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$rule['sub_disposition_id'])."'";
							$q56 = mysqli_query($this->connectionlink,$query56);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) { //If Custom Pallet exists
								$row56 = mysqli_fetch_assoc($q56);
								$json['Sub_CustomPalletID'] = $row56['CustomPalletID'];
								$json['Sub_BinName'] = $row56['BinName'];
							}
						}

						$json['Success'] = true;
						$json['Result'] = $rule;
						return json_encode($json);
					}
				}

				$json['Success'] = false;
				$json['Result'] = 'No Rules Satisfied';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Matching Rules Available';
				//$json['Result'] = $rules_query;
				return json_encode($json);
			}
			//End get Rules

			$json['Success'] = false;
			$json['Result'] = $input;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetWasteCode($idPallet,$disposition_id,$part_type) {
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		//$query = "select * from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$idPallet)."'";
		$query = "select WasteClassificationType from disposition where disposition_id = '".mysqli_real_escape_string($this->connectionlink,$disposition_id)."'";//Need to get Waste code from disposition
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			//Start get Waste Code ID
			$query1 = "select WasteCode from waste_codes where FacilityID = '".mysqli_real_escape_string($this->connectionlink,$row['PalletFacilityID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$disposition_id)."' and part_type = '".mysqli_real_escape_string($this->connectionlink,$part_type)."' and StatusID = '1' and WasteClassificationType = '".mysqli_real_escape_string($this->connectionlink,$row['WasteClassificationType'])."'";
			$q1 = mysqli_query($this->connectionlink, $query1);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return $json;
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				$json['Success'] = true;
				$json['Result'] = $row1['WasteCode'];
				return $json;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No records';
				return $json;
			}
			//End get Waste Code ID			
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Disposition";
			return $json;
		}
		return $json;
	}


	public function QueryAIHelp($input) {
		try {
			//Start sending SNS Message

			$curl = curl_init();
			curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://awsapiprod.eviridis.com/genai/BlogGeneration',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS =>'{"Query": "'.$input.'"}',
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			),
			));
			$response = curl_exec($curl);
			curl_close($curl);

			//End sending SNS Message
			$res = json_decode($response,true);
			
			
			$json['Success'] = true;
			$json['Message'] = $res;
			return $json;				
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Error'] = $ex->getMessage();
			return $json;			
		}			
	}


	public function NLToSqlQuery($input) {
		try {
			//Start sending SNS Message

			$payload = json_encode([
				"body" => json_encode([
					"query" => $input
				])
			]);

			$curl = curl_init();
			curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://awsapiprod.eviridis.com/genai/GenerateQuery',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $payload,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			),
			));
			$response = curl_exec($curl);
			curl_close($curl);

			//End sending SNS Message
			//$res = json_decode($response,true);
			
			
			$json['Success'] = true;
			$json['Message'] = $response;
			return $json;				
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Error'] = $ex->getMessage();
			return $json;			
		}			
	}

	public function GetClassificationDetails($AssetScanID,$disposition_id) {
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$query = "select FacilityID,part_type from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$AssetScanID)."'";
		$q = mysqli_query($this->connectionlink, $query);		
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return $json;
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$query1 = "select WasteCode,WasteClassificationType from waste_codes where disposition_id = '".mysqli_real_escape_string($this->connectionlink,$disposition_id)."' and part_type = '".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."' and StatusID = '1' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$row['FacilityID'])."'";;
			$q1 = mysqli_query($this->connectionlink, $query1);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return $json;
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				
				$json['Success'] = true;
				$json['WasteCode'] = $row1['WasteCode'];
				$json['WasteClassificationType'] = $row1['WasteClassificationType'];
				return $json;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No records';
				return $json;
			}
			
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Asset";
			return $json;
		}
		return $json;
	}

	/**
	 * Validate customer lock for bin before placing records
	 *
	 * @param int $customPalletID The bin ID to validate
	 * @param int $recordAWSCustomerID The customer ID of the record being placed
	 * @param string $binName The bin name for tracking purposes
	 * @param int $userId User ID for tracking
	 * @return array Result array with Success (bool) and Result (string message)
	 */
	public function ValidateCustomerLockForBin($customPalletID, $recordAWSCustomerID, $binName = '', $userId = 0) {
		$result = array(
			'Success' => false,
			'Result' => 'No data',
			'BinUpdated' => false
		);

		try {
			// Get bin details including CustomerLock and AWSCustomerID
			$binQuery = "SELECT CustomerLock, AWSCustomerID, AssetsCount, BinName FROM custompallet WHERE CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $customPalletID) . "'";
			$binResult = mysqli_query($this->connectionlink, $binQuery);

			if (mysqli_error($this->connectionlink)) {
				$result['Success'] = false;
				$result['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
				return $result;
			}

			if (mysqli_num_rows($binResult) == 0) {
				$result['Success'] = false;
				$result['Result'] = 'Bin not found';
				return $result;
			}

			$binData = mysqli_fetch_assoc($binResult);
			$binNameForTracking = !empty($binName) ? $binName : $binData['BinName'];

			// If CustomerLock is not enabled, allow any customer
			if ($binData['CustomerLock'] != '1') {
				$result['Success'] = true;
				$result['Result'] = 'Customer lock not enabled for this bin';
				return $result;
			}

			// Get customer names for messaging
			$recordCustomerName = '';
			$binCustomerName = '';

			if ($recordAWSCustomerID > 0) {
				$recordCustomerQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '" . mysqli_real_escape_string($this->connectionlink, $recordAWSCustomerID) . "'";
				$recordCustomerResult = mysqli_query($this->connectionlink, $recordCustomerQuery);
				if ($recordCustomerResult && mysqli_num_rows($recordCustomerResult) > 0) {
					$recordCustomerRow = mysqli_fetch_assoc($recordCustomerResult);
					$recordCustomerName = $recordCustomerRow['Customer'];
				}
			}

			if ($binData['AWSCustomerID'] > 0) {
				$binCustomerQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '" . mysqli_real_escape_string($this->connectionlink, $binData['AWSCustomerID']) . "'";
				$binCustomerResult = mysqli_query($this->connectionlink, $binCustomerQuery);
				if ($binCustomerResult && mysqli_num_rows($binCustomerResult) > 0) {
					$binCustomerRow = mysqli_fetch_assoc($binCustomerResult);
					$binCustomerName = $binCustomerRow['Customer'];
				}
			}

			// CustomerLock is enabled (= 1)
			// Case 1: Bin has no assigned customer (AWSCustomerID is NULL) and AssetsCount is 0
			if (($binData['AWSCustomerID'] == '' || $binData['AWSCustomerID'] == null) && $binData['AssetsCount'] == 0) {
				// Update bin with the customer ID of the first record
				$updateQuery = "UPDATE custompallet SET AWSCustomerID = '" . mysqli_real_escape_string($this->connectionlink, $recordAWSCustomerID) . "', LastModifiedDate = NOW(), LastModifiedBy = '" . mysqli_real_escape_string($this->connectionlink, $userId) . "' WHERE CustomPalletID = '" . mysqli_real_escape_string($this->connectionlink, $customPalletID) . "'";
				$updateResult = mysqli_query($this->connectionlink, $updateQuery);

				if (mysqli_error($this->connectionlink)) {
					$result['Success'] = false;
					$result['Result'] = 'Failed to update bin customer: ' . mysqli_error($this->connectionlink);
					return $result;
				}

				// Add tracking record for customer assignment
				if ($userId > 0) {
					$customerDisplayName = !empty($recordCustomerName) ? $recordCustomerName : "Customer ID: $recordAWSCustomerID";
					$trackingAction = "Bin customer lock assigned to $customerDisplayName (first record placed in empty bin)";
					$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy) VALUES ('" . mysqli_real_escape_string($this->connectionlink, $customPalletID) . "', '" . mysqli_real_escape_string($this->connectionlink, $binNameForTracking) . "', '" . mysqli_real_escape_string($this->connectionlink, $trackingAction) . "', NOW(), '" . mysqli_real_escape_string($this->connectionlink, $userId) . "')";
					mysqli_query($this->connectionlink, $trackingQuery);
				}

				$customerDisplayName = !empty($recordCustomerName) ? $recordCustomerName : "Customer ID: $recordAWSCustomerID";
				$result['Success'] = true;
				$result['Result'] = "Bin customer assigned to $customerDisplayName";
				$result['BinUpdated'] = true;
				return $result;
			}

			// Case 2: Bin has assigned customer - validate match
			if ($binData['AWSCustomerID'] != $recordAWSCustomerID) {
				$binCustomerDisplay = !empty($binCustomerName) ? $binCustomerName : "Customer ID: " . $binData['AWSCustomerID'];
				$recordCustomerDisplay = !empty($recordCustomerName) ? $recordCustomerName : "Customer ID: $recordAWSCustomerID";
				$result['Success'] = false;
				$result['Result'] = "Customer lock violation: Bin is locked to $binCustomerDisplay, but record belongs to $recordCustomerDisplay";
				return $result;
			}

			// Case 3: Customer matches - allow placement
			$result['Success'] = true;
			$result['Result'] = 'Customer validation passed';
			return $result;

		} catch (Exception $e) {
			$result['Success'] = false;
			$result['Result'] = 'Error: ' . $e->getMessage();
			return $result;
		}
	}

}