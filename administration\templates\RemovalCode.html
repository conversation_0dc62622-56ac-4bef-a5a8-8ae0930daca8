
<div class="row page" data-ng-controller="RemovalCode">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Removal Code Creation</span>
                        <div flex></div>
                           <a href="#!/RemovalCodeList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Removal Code</label>
                                        <input type="text" name="RemovalCode"  ng-model="RemovalCode['RemovalCode']"  required ng-disabled="RemovalCode.RemovalCodeID" ng-maxlength="45" />
                                         <div class="error-sapce">
                                        <div ng-messages="material_signup_form.RemovalCode.$error" multiple ng-if='material_signup_form.RemovalCode.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 45.</div>                           
                                        </div>
                                    </div>
                                    </md-input-container>
                                </div>
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Facility</label>
                                        <md-select name="FacilityID" ng-model="RemovalCode.FacilityID" required ng-disabled="RemovalCode.RemovalCodeID" ng-change="RemovalCode.part_type = '';GetPartTypes()">
                                            <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities">{{fac.FacilityName}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                    </md-input-container>
                                </div>
                                <div class="col-md-6">
                                    <md-input-container class="md-block">
                                        <label>Removal Type</label>   
                                        <md-select name="disposition_id" ng-model="RemovalCode.disposition_id" required aria-label="select" ng-change="RemovalCode.part_type = '';GetDisposition();GetPartTypes()" ng-disabled="RemovalCode.RemovalCodeID">
                                            <md-option ng-repeat="dis_position in disposition" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                        </md-select>
                                        <div class="error-space"> 
                                            <div ng-messages="material_signup_form.disposition_id.$error" multiple ng-if='material_signup_form.disposition_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>  
                                    </md-input-container>
                                </div>
                                <div class="col-md-12" style="clear:both;">
                                </div>
                                <div class="col-md-3">
                                    <md-input-container class="md-block">                                            
                                        <label>Inventory Type </label>
                                        <md-select name="InventoryType" ng-model="RemovalCode.InventoryType" required aria-label="select" ng-change="RemovalCode.part_type = '';GetPartTypes()">
                                            <md-option value="Inventory"> Inventory </md-option>
                                            <md-option value="Sub Component Inventory"> Sub Component Inventory </md-option>
                                            <md-option value="Byproduct"> Byproduct </md-option>
                                        </md-select>  
                                         <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.InventoryType.$error" multiple ng-if='material_signup_form.InventoryType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div> 
                                        </div>                                            
                                    </md-input-container>
                                </div>
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Part Type</label>
                                        <md-select name="part_type" ng-model="RemovalCode.part_type" required>
                                            <md-option value="{{part.part_type}}" ng-repeat="part in PartTypes">{{part.part_type}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                        <div ng-messages="material_signup_form.part_type.$error" multiple ng-if='material_signup_form.part_type.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-6">
                                    <md-input-container class="md-block">
                                        <label>Description</label>
                                        <input type="text" name="Description"  ng-model="RemovalCode['Description']"  required ng-maxlength="250" />
                                         <div class="error-sapce">
                                        <div ng-messages="material_signup_form.Description.$error" multiple ng-if='material_signup_form.Description.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 250.</div>                           
                                        </div>
                                    </div>
                                    </md-input-container>
                                </div>
                           
                                <div class="col-md-3">
                                    <md-input-container class="md-block">                                            
                                        <label>Status</label>
                                        <md-select name="StatusID" ng-model="RemovalCode.StatusID" required aria-label="select">
                                            <md-option value="1"> Active </md-option>
                                            <md-option value="2"> In Active </md-option>
                                        </md-select>  
                                         <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.StatusID.$error" multiple ng-if='material_signup_form.StatusID.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                        </div>                                            
                                    </md-input-container>
                                </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/RemovalCodeList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="RemovalCodeSave()">
                                <span ng-show="! RemovalCode.busy">Save</span>
                                <span ng-show="RemovalCode.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>