<style>
    .approvers-box{background-color: #f4f4f4; padding-top: 8px; margin-top: 10px; border: 1px solid #e4e4e4;}
    .approvers-box md-switch{ margin-top: 10px;}
</style>
<div class="row page" ng-controller="incident_workflow">
    <div class="col-md-12">
        
        <article class="article">            
            <div class="clearfix">
                <h2 class="section-header" style="display:flex">                    
                    Migration Workflow
                </h2>
            </div>

            <div class="panel panel-default" ng-repeat="action in TicketActions" ng-init="action.showDetails = false">
                <div class="panel-heading" ng-click="action.showDetails = ! action.showDetails">
                    <h3 class="panel-title">{{action.Action}}</h3>
                </div>
                <div class="panel-body" ng-show="action.showDetails">
                    <div class="row">
                        <div class="col-md-4">
                            <md-switch ng-model="action.RequireApproval" aria-label="Switch 1" ng-change="ApprovalChanged(action)"> Requires Approval to Start Step</md-switch>
                            <md-input-container class="md-block" ng-if="action.RequireApproval">
                                <label>Approval Type</label>
                                <md-select ng-model="action.ApprovalType" aria-label="select">
                                    <md-option value="Concurrent"> Concurrent </md-option>
                                    <md-option value="In Sequence"> In Sequence </md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div class="col-md-4">
                            <md-switch ng-model="action.EmailNotifications" aria-label="Switch 2"> Send Email notifications</md-switch>                            
                        </div>
                    </div>

                    <div class="row" ng-if="action.RequireApproval">                                
                        <div class="col-md-12">
                            
                            <div class="approvers-box" style="padding-bottom: 10px;">

                                <!--box start-->
                                <div class="row" ng-repeat="approver in action.Approvers">
                                    <div class="col-md-12">
                                        <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Approver {{$index + 1}}</label>                                                
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Choose User to Approve</label>
                                                <md-select ng-model="approver.ApproverUserID" aria-label="select">
                                                    <md-option ng-repeat="user in Users" value="{{user.UserId}}"> {{user.FirstName}} {{user.LastName}} </md-option>
                                                </md-select>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-button class="md-raised md-dark" style="margin-top: 10px;" ng-click="AddMoreApprover(action)" ng-if="$index == 0"><i class="fa fa-plus" aria-hidden="true"></i> Add</md-button>
                                            <md-button class="md-raised md-dark" style="margin-top: 10px;" ng-click="DeleteApprover(action,$index)" ng-if="$index > 0"><i class="fa fa-remove" aria-hidden="true"></i> Delete</md-button>
                                        </div>
                                    </div>
                                </div>
                                <!--box start-->

                            </div>
                        </div>
                                        
                    </div>
                </div>
            </div>

        </article>        
    </div>
</div>