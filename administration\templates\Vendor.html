
<div class="row page" data-ng-controller="Vendor">
    <div class="col-md-12">
        <article class="article">

            <form name="Vendor_Form" class="form-validation" data-ng-submit="submitForm()">

                <md-card class="no-margin-h">
                    <!--  
                    <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <span>Vendor Creations</span>
                                <div flex></div>
                                    <a href="#!/VendorList" class="md-button md-raised btn-w-md" style="display: flex;">
                                        <i class="material-icons">chevron_left</i>Back to Vendor List
                                    </a>
                            </div>

                    </md-toolbar>-->
        
                    <md-toolbar class="md-table-toolbar md-default" ng-init="CreateNewVendor = true">
                            <div class="md-toolbar-tools" >
                                <i ng-click="CreateNewVendor = !CreateNewVendor" class="material-icons md-primary" ng-show="CreateNewVendor">keyboard_arrow_up</i>
                                <i ng-click="CreateNewVendor = !CreateNewVendor" class="material-icons md-primary" ng-show="! CreateNewVendor">keyboard_arrow_down</i>
                                <span ng-click="CreateNewVendor = !CreateNewVendor">Destination Creations</span>
                                <div flex></div> 
                                    <a href="#!/VendorList" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                        <i class="material-icons">chevron_left</i> Back to List
                                    </a>
                            </div>
                    </md-toolbar>
               
                    <div class="row" ng-show="CreateNewVendor">
                        <div class="col-md-12">                        
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Destination Name</label>
                                    <input type="text" name="VendorName"  ng-model="Vendor['VendorName']"  required ng-maxlength="45" />
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.VendorName.$error" multiple ng-if='Vendor_Form.VendorName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                             <!-- <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>ShortCode</label>
                                    <input value="ShortCode" name="Vendor.ShortCode" ng-disabled="true" />
                                </md-input-container>
                            </div> -->
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>ShortCode ID</label>
                                    <input type="text" name="CustomerShotCode"  ng-model="Vendor['ShortCode']"  required ng-maxlength="50" />
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.ShortCode.$error" multiple ng-if='Vendor_Form.ShortCode.$dirty'>                            
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>
                                    </div>
                                    </div>
                                </md-input-container>   
                            </div> 
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Street Address</label>
                                    <input type="text" name="StreetAddress"  ng-model="Vendor['StreetAddress']"  required ng-maxlength="250" />
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.StreetAddress.$error" multiple ng-if='Vendor_Form.StreetAddress.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                               <md-input-container class="md-block">
                                    <label>Country</label>   
                                    <md-select name="Country" ng-model="Vendor.Country" required aria-label="select" ng-change="Getcountries()">
                                        <md-option ng-repeat="Cou in countries" value="{{Cou.CountryID}}"> {{Cou.Country}} </md-option>
                                    </md-select>
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.CountryID.$error" multiple ng-if='Vendor_Form.CountryID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>   
                                    </div>  
                                </md-input-container>
                            </div>
                           <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>State/Province</label>
                                    <md-select name="State" ng-model="Vendor.State" ng-change="Getstates()">
                                    <md-option ng-repeat="sta in states" value="{{sta.StateID}}"> {{sta.State}} 
                                    </md-option>
                                    </md-select>  
                                    <div class="error-space">   
                                    <div ng-messages="Vendor_Form.State.$error" multiple ng-if='Vendor_Form.State.$dirty'>
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>City</label>
                                    <input type="text" name="City"  ng-model="Vendor['City']" required ng-maxlength="45" />
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.City.$error" multiple ng-if='Vendor_Form.City.$dirty'>    
                                       <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>   
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Zip</label>
                                    <input type="text" name="Zip"  ng-model="Vendor['Zip']" ng-maxlength="20"/>
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.Zip.$error" multiple ng-if='Vendor_Form.Zip.$dirty'>      
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>  
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Phone</label>
                                    <input type="text" name="Phone"  ng-model="Vendor['Phone']" ng-maxlength="20" />
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.Phone.$error" multiple ng-if='Vendor_Form.Phone.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Fax</label>
                                    <input type="text" name="Fax"  ng-model="Vendor['Fax']" ng-maxlength="20" />
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.Fax.$error" multiple ng-if='Vendor_Form.Fax.$dirty'>      
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>  
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                   <label>WebSite</label>
                                    
                                   <input type="text" name="Website"  ng-model="Vendor['Website']" ng-maxlength="20"/>
                                   <div class="error-space"> 
                                   <div ng-messages="Vendor_Form.Website.$error" multiple ng-if='Vendor_Form.Website.$dirty'>     <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Title</label>
                                    <input type="text" name="Title"  ng-model="Vendor['Title']" ng-maxlength="20"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.Title.$error" multiple ng-if='Vendor_Form.Title.$dirty'>    <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Contact Name</label>
                                    <input type="text" name="ContactName"  ng-model="Vendor['ContactName']" required ng-maxlength="20"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.ContactName.$error" multiple ng-if='Vendor_Form.ContactName.$dirty'>
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>                                                
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Contact Phone</label>
                                    <input type="text" name="ContactPhone"  ng-model="Vendor['ContactPhone']" ng-maxlength="20"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.ContactPhone.$error" multiple ng-if='Vendor_Form.ContactPhone.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>                                                
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Contact Email</label>
                                    <input type="text" name="ContactEmail"  ng-model="Vendor['ContactEmail']" required ng-maxlength="100"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.ContactEmail.$error" multiple ng-if='Vendor_Form.ContactEmail.$dirty'>
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>                                                
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                               <md-input-container class="md-block">
                                    <label>Destination Category</label>   
                                    <md-select name="VendorCategory" ng-model="Vendor.VendorCategory" required aria-label="select" ng-change="GetVendorCategory()">
                                        <md-option ng-repeat="gvc in vendor_category" value="{{gvc.idvendor_category}}"> {{gvc.VendorCategoryName}} </md-option>
                                    </md-select>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.VendorCategory.$error" multiple ng-if='Vendor_Form.VendorCategory.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>  
                                    </div>   
                                </md-input-container>
                            </div>
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                   <label>Special Instructions</label>
                                   <input type="text" name="SpecialInstructions"  ng-model="Vendor['SpecialInstructions']" ng-maxlength="250"/>
                                   <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.SpecialInstructions.$error" multiple ng-if='Vendor_Form.SpecialInstructions.$dirty'>                            
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>                             
                             <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="Vendor.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> Inactive </md-option>
                                    </md-select>  
                                    <div class="error-space">  
                                    <div ng-messages="Vendor_Form.Status.$error" multiple ng-if='Vendor_Form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>   
                                    </div>                                          
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Currency</label>
                                    <md-select name="vendor_currency" ng-model="Vendor.vendor_currency" aria-label="select">
                                        <md-option value="C1"> USD </md-option>
                                        <md-option value="C2"> EURO </md-option>
                                    </md-select>   
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.vendor_currency.$error" multiple ng-if='Vendor_Form.vendor_currency.$dirty'>
                                        <!-- <div ng-message="required">This is required.</div>-->
                                    </div>     
                                    </div>                                        
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Waste Certification ID</label>
                                    <input type="text" required name="WasteCertificationID"  ng-model="Vendor['WasteCertificationID']" ng-maxlength="50"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.WasteCertificationID.$error" multiple ng-if='Vendor_Form.WasteCertificationID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                                                
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="Vendor.ContainerMPNLock" aria-label="ContainerMPNLock" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Container MPN Lock </md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="Vendor.ContainerCOOLock" aria-label="ContainerCOOLock" ng-true-value="'1'" ng-false-value="'0'" class="mb-10"> Container COO Lock </md-checkbox>
                                </md-input-container>
                            </div>
                        </div>                       
                    </div>

                </md-card>

                  
                <md-card class="no-margin-h">
                    <md-toolbar class="md-table-toolbar md-default" ng-init="BillingInstruction = true">
                        <div class="md-toolbar-tools" >
                            <i ng-click="BillingInstruction = !BillingInstruction" class="material-icons md-primary" ng-show="BillingInstruction">keyboard_arrow_up</i>
                            <i ng-click="BillingInstruction = !BillingInstruction" class="material-icons md-primary" ng-show="! BillingInstruction">keyboard_arrow_down</i>
                            <span ng-click="BillingInstruction = !BillingInstruction">Billing Instruction</span>
                            <div flex></div>
                            <span class="dis_none_v"><md-switch aria-label="default" class="md-default mr-20" ng-model="CopyVendorDetails" ng-change="CopyDetails()">Copy Destination Information</md-switch></span>
                        </div>
                        <div class="dis_open_v col-md-12" style="display: none;"><md-switch aria-label="default" class="md-default mr-20" ng-model="CopyVendorDetails" ng-change="CopyDetails()">Copy Destination Info</md-switch></div>
                    </md-toolbar>

                        <!-- <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <span>Billing Instruction</span>
                            </div>
                            </md-toolbar> -->
                    <div class="row" ng-show="BillingInstruction">
                        <div class="col-md-12">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Bill Contact</label>
                                    <input type="text" name="BillContact"  ng-model="Vendor['BillContact']" ng-maxlength="50" />
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.BillContact.$error" multiple ng-if='Vendor_Form.BillContact.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                                                
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                <label>Bill City</label>
                                <input type="text" name="BillCity"  ng-model="Vendor['BillCity']" ng-maxlength="20" />
                                <div class="error-space"> 
                                <div ng-messages="Vendor_Form.BillCity.$error" multiple ng-if='Vendor_Form.BillCity.$dirty'> 
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 20.</div>                                                
                                </div>
                                </div>

                            </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Bill Zip</label>
                                    <input type="text" name="BillZip"  ng-model="Vendor['BillZip']" ng-maxlength="20" />
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.BillZip.$error" multiple ng-if='Vendor_Form.BillZip.$dirty'> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div> 
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Bill Phone</label>
                                    <input type="text" name="BillPhone"  ng-model="Vendor['BillPhone']" ng-maxlength="20" />
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.BillPhone.$error" multiple ng-if='Vendor_Form.BillPhone.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div> 
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Bill Email</label>
                                    <input type="text" name="BillEmail"  ng-model="Vendor['BillEmail']" ng-maxlength="45"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.BillEmail.$error" multiple ng-if='Vendor_Form.BillEmail.$dirty'>      
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div> 

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Payment Method</label>
                                    <input type="text" name="PaymentCode"  ng-model="Vendor['PaymentCode']" ng-maxlength="20"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.PaymentCode.$error" multiple ng-if='Vendor_Form.PaymentCode.$dirty'> 
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 20.</div>     
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Customer Code</label>
                                    <input type="text" name="SAPCustomer"  ng-model="Vendor['SAPCustomer']" ng-maxlength="20" />
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.SAPCustomer.$error" multiple ng-if='Vendor_Form.SAPCustomer.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div> 
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                <label>Document Currency</label>
                                <input type="text" name="DocCurrency"  ng-model="Vendor['DocCurrency']" ng-maxlength="20" />
                                <div class="error-space"> 
                                <div ng-messages="Vendor_Form.DocCurrency.$error" multiple ng-if='Vendor_Form.DocCurrency.$dirty'> 
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 20.</div>
                                </div>
                                </div>
                            </md-input-container>
                            </div> 
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Bill Address</label>
                                    <input type="text" name="BillAddress"  ng-model="Vendor['BillAddress']" ng-maxlength="250" />
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.BillAddress.$error" multiple ng-if='Vendor_Form.BillAddress.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                    <label>Country</label>   
                                    <md-select name="BillCountry" ng-model="Vendor.BillCountry" aria-label="select" ng-change="Getcountries()">
                                        <md-option ng-repeat="Cou in countries" value="{{Cou.CountryID}}"> {{Cou.Country}} </md-option>
                                    
                                    </md-select>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.CountryID.$error" multiple ng-if='Vendor_Form.CountryID.$dirty'>
                                    </div> 
                                    </div>    
                            </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>State/Province</label>
                                    <md-select name="BillState" ng-model="Vendor.BillState" ng-change="Getstates()">
                                    <md-option ng-repeat="sta in states" value="{{sta.StateID}}"> {{sta.State}} 
                                    </md-option>
                                    </md-select>   
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.State.$error" multiple ng-if='Vendor_Form.State.$dirty'>
                                        
                                    </div>    
                                    </div>                                         
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Bill Fax</label>
                                    <input type="text" name="BillFax"  ng-model="Vendor['BillFax']" ng-maxlength="20"/>
                                    <div class="error-space"> 
                                        <div ng-messages="Vendor_Form.BillFax.$error" multiple ng-if='Vendor_Form.BillFax.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 20.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Payment Terms</label>   
                                    <md-select name="PaymentTerms" ng-model="Vendor.PaymentTerms" aria-label="select" ng-change="GetPaymentTerms()">
                                        <md-option ng-repeat="gpt in payment_terms" value="{{gpt.payment_terms_ID}}"> {{gpt.payment_terms}} </md-option>
                                    </md-select>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.PaymentTerms.$error" multiple ng-if='Vendor_Form.PaymentTerms.$dirty'>
                                    </div>  
                                    </div>   
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Invoice Code</label>
                                    <input type="text" name="InvoiceCode"  ng-model="Vendor['InvoiceCode']" ng-maxlength="20"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.InvoiceCode.$error" multiple ng-if='Vendor_Form.InvoiceCode.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Tax Code</label>
                                    <input type="text" name="TaxCode"  ng-model="Vendor['TaxCode']" ng-maxlength="20"/>
                                    <div class="error-space"> 
                                    <div ng-messages="Vendor_Form.TaxCode.$error" multiple ng-if='Vendor_Form.TaxCode.$dirty'>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 20.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                        </div>
                    </div>

                </md-card>

                <md-card class="no-margin-h">
                    <div class="row">
                        <div class="col-md-12 btns-row">
                            <a href="#!/VendorList" style="text-decoration: none;">
                            <md-button class="md-button md-raised btn-w-md  md-default">
                                Cancel
                            </md-button>
                            </a> 
                            <md-button class="md-raised btn-w-md md-primary btn-w-md"
                            data-ng-disabled="Vendor_Form.$invalid || Vendor.busy" ng-click="NewVendor()">
                            <span ng-show="! Vendor.busy">Save</span>
                            <span ng-show="Vendor.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                        </div>
                    </div>
                </md-card>

            </form>
            <div style="clear: both;"></div>
            <md-card class="no-margin-h" ng-show="Vendor.VendorID">
                    
                <md-toolbar class="md-table-toolbar md-default"  ng-init="attributesPanel = true;" >
                    <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="attributesPanel = !attributesPanel">                            
                        <i class="material-icons" ng-click="attributesPanel = !attributesPanel" ng-show="attributesPanel">keyboard_arrow_up</i>
                        <i class="material-icons" ng-click="attributesPanel = !attributesPanel" ng-show="! attributesPanel">keyboard_arrow_down</i>
                        <span>Removal Types</span>
                    </div>
                </md-toolbar>
                <div class="row" ng-show="attributesPanel">
                    <form>                            
                        <div class="col-md-12">
                            <div class="col-md-4 col-sm-8 col-xs-8" >
                                <md-input-container class="md-block">                                            
                                    <label>Add Removal Type</label>
                                    <md-select name="disposition_id" ng-model="disposition_id" aria-label="select">
                                        <md-option value="{{disp.disposition_id}}" ng-repeat="disp in removal_types"> {{disp.disposition}} <span style="color:red;" ng-show="disp.sub_disposition > 0">(Sub Disposition)</span> </md-option> 
                                    </md-select>                                
                                </md-input-container>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-4">
                                <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px;" ng-disabled="!disposition_id" ng-click="AddRemovalTypeToDestination(disposition_id)">
                                    Add
                                    <!--<i class="material-icons">add</i> Add-->
                                </button>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="col-md-12">
                                    <table md-table md-row-select  ng-show="MappedRemovalTypes.length > 0">
                                        <thead md-head>
                                            <tr md-row>
                                                <th md-column>Removal Type</th>
                                                <th md-column style="padding-left: 5px;">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody md-body>
                                            <tr md-row ng-repeat="mr in MappedRemovalTypes">
                                                <td md-cell>{{mr.disposition}}</td>
                                                <td md-cell>
                                                    <span class="text-danger actionlinkicon" ng-click="DeleteMappedRemovalType(mr,$index,$event)"><i class="material-icons">delete_forever</i> Delete  </span>                                  
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>  
                            </div>
                        </div>
                    </form>
                </div>

            </md-card>

        </article>        
    </div>
</div>