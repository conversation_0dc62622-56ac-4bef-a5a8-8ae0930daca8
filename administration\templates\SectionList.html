<div class="row page" data-ng-controller="SectionList">
    <div class="col-md-12">
        <article class="article">
            
            <div class="row ui-section">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-body" style="padding-top:0px;">

                            <md-toolbar class="md-table-toolbar md-default" ng-hide="options.rowSelection && selected.length">
                                <div class="md-toolbar-tools">
                                    <span>Section List</span>
                                    <div flex></div>      
                                    <div ng-show="pagedItems && ! busy" style="margin-right: 20px;">
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                    </div>                       
                                    <a href="#!/Section" class="md-button md-raised btn-w-md" style="display: flex;">
                                        <i class="material-icons">add</i> Create New Section
                                    </a>
                                </div>
                            </md-toolbar>

                            <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                                <!-- <h4>Callout heading</h4> -->
                                <p>No data available </p>
                            </div>

                            <div class="table-responsive" ng-show="pagedItems.length > 0">
                                <table class="table table-striped no-margin" style="min-width: 1500px;">
                                    <thead>
                                        <tr>
                                            <th>Edit</th>   
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('SectionName')">
                                                Section Name 
                                                <span ng-show="OrderBy == 'SectionName'">                                                    
                                                    <i class="material-icons" ng-show="OrderByType == 'desc'">arrow_downward</i>
                                                    <i class="material-icons" ng-show="OrderByType == 'asc'">arrow_upward</i>
                                                </span>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('ModuleName')">
                                                Module Name 
                                                <span ng-show="OrderBy == 'ModuleName'">                                                    
                                                    <i class="material-icons" ng-show="OrderByType == 'desc'">arrow_downward</i>
                                                    <i class="material-icons" ng-show="OrderByType == 'asc'">arrow_upward</i>
                                                </span>
                                            </th>                                         
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('ApplicationName')">
                                                Application 
                                                <span ng-show="OrderBy == 'ApplicationName'">                                                    
                                                    <i class="material-icons" ng-show="OrderByType == 'desc'">arrow_downward</i>
                                                    <i class="material-icons" ng-show="OrderByType == 'asc'">arrow_upward</i>
                                                </span>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('CustomerName')">
                                                Customer
                                                <span ng-show="OrderBy == 'CustomerName'">                                                    
                                                    <i class="material-icons" ng-show="OrderByType == 'desc'">arrow_downward</i>
                                                    <i class="material-icons" ng-show="OrderByType == 'asc'">arrow_upward</i>
                                                </span>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')">
                                                Status                                                 
                                                <span ng-show="OrderBy == 'Status'">                                                    
                                                    <i class="material-icons" ng-show="OrderByType == 'desc'">arrow_downward</i>
                                                    <i class="material-icons" ng-show="OrderByType == 'asc'">arrow_upward</i>
                                                </span>
                                            </th>                                            
                                        </tr>                                        
                                    </thead>
                                    <tbody>
                                        <tr class="tsearch">
                                            <td><md-input-container class="md-block"></md-input-container></th>                                            
                                            <td><md-input-container class="md-block"><input type="text" name="SectionName" ng-model="filter_text[0].SectionName" ng-change="MakeFilter()" aria-label="text"  /></md-input-container></td>
                                            <td><md-input-container class="md-block"><input type="text" name="ModuleName" ng-model="filter_text[0].ModuleName" ng-change="MakeFilter()" aria-label="text"  /></md-input-container></td>
                                            <td><md-input-container class="md-block"><input type="text" name="ApplicationName" ng-model="filter_text[0].ApplicationName" ng-change="MakeFilter()" aria-label="text"  /></md-input-container></td>
                                            <td><md-input-container class="md-block"><input type="text" name="CustomerName" ng-model="filter_text[0].CustomerName" ng-change="MakeFilter()" aria-label="text" /></md-input-container></td>
                                            <td><md-input-container class="md-block"><input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" /></md-input-container></td>                                            
                                        </tr>
                                        <tr ng-repeat="tkt in pagedItems">
                                            <td><a href="#!/Section/{{tkt.SectionID}}"><md-icon class="material-icons action-icons">edit</md-icon></a></td>                                            
                                            <td>{{tkt.SectionName}}</td>
                                            <td>{{tkt.ModuleName}}</td>
                                            <td>{{tkt.ApplicationName}}</td>
                                            <td>{{tkt.CustomerName}}</td>
                                            <td>{{tkt.Status}}</td>
                                        </tr>

                                    </tbody>
                                </table>

                                <ul class="pagination">
									<li ng-class="prevPageDisabled()">
										<a href ng-click="firstPage()"><< First</a>
									</li>
									<li ng-class="prevPageDisabled()">
										<a href ng-click="prevPage()"><< Prev</a>
									</li>
									<li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
										<a style="cursor:pointer;">{{n+1}}</a>
									</li>
									<li ng-class="nextPageDisabled()">
										<a href ng-click="nextPage()">Next >></a>
									</li>
									<li ng-class="nextPageDisabled()">
										<a href ng-click="lastPage()">Last >></a>
									</li>
								</ul>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </article>                            
    </div>

</div>