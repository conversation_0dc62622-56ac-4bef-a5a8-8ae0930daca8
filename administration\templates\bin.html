
<div class="row page" data-ng-controller="bin">
    <style type="text/css">
        .autocomplete md-autocomplete .md-whiteframe-z1{ border-bottom:none !important;}
        .autocomplete {padding: 0px 0px 0px 0px !important;}
        .noerrorspacer .md-errors-spacer{display: none !important;}
    </style>
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Bin</span>
                        <div flex></div>
                            <a href="#!/BinList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to Bin List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">

                            <!-- First Row: Bin Type, Bin Name with Generate Button, Facility -->
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Bin Type</label>
                                    <md-select name="idPackage" ng-model="cpalletForm.idPackage" required aria-label="select" ng-disabled="cpalletForm.CustomPalletID">
                                        <md-option ng-repeat="packageType in PackageTypes" value="{{packageType.idPackage}}"> {{packageType.packageName}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.idPackage.$error" multiple ng-if='material_signup_form.idPackage.$dirty'>
                                            <div ng-message="required">Bin Type is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Bin Name</label>
                                    <div style="display: inline-block; width: 100%;">
                                        <input name="BinName" style="width: 88%; float: left;" ng-model="cpalletForm.BinName" value="" required ng-disabled="cpalletForm.CustomPalletID" />
                                        <md-button class="md-raised md-primary" style="float: right;min-width: 32px; height: 30px; min-height: 30px; margin: 0; padding: 0;" ng-click="generateUniqueBinName()" ng-disabled="cpalletForm.CustomPalletID">
                                            <i class="material-icons" style="font-size: 16px;">refresh</i>
                                        </md-button>
                                    </div>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.BinName.$error" multiple ng-if='material_signup_form.BinName.$dirty'>
                                            <div ng-message="required">Bin Name is required.</div>
                                        </div>
                                        <div ng-if="cpalletForm.CustomPalletID" style="color: #ff9800; font-size: 12px; margin-top: 5px;">
                                            Bin name cannot be changed after creation
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="cpalletForm.FacilityID" required aria-label="select" ng-disabled="true" ng-change="GetBinPackageTypes()">
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Disposition -->
                            <div class="col-md-4" ng-if="cpalletForm.AcceptAllDisposition == 0">
                                <md-input-container class="md-block" style="position: relative;">
                                    <label>Disposition (Optional)</label>
                                    <md-select name="disposition_id" ng-model="cpalletForm.disposition_id" aria-label="select" ng-change="GetDisposition()" ng-disabled="cpalletForm.CustomPalletID && cpalletForm.AssetsCount > 0">
                                        <md-option ng-repeat="dis_position in disposition" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                    </md-select>
                                    <md-button class="md-icon-button" ng-click="clearDisposition()" ng-if="cpalletForm.disposition_id" style="position: absolute; right: 0; top: 50%; transform: translateY(-50%); min-width: 24px; width: 24px; height: 24px; margin: 0; padding: 0;">
                                        <md-icon style="font-size: 18px; width: 18px; height: 18px;">clear</md-icon>
                                    </md-button>
                                    <div class="error-space">
                                        <div ng-if="cpalletForm.CustomPalletID && cpalletForm.AssetsCount > 0" style="color: #ff9800; font-size: 12px; margin-top: 5px;">
                                            Disposition can only be changed when bin has 0 assets (Current: {{cpalletForm.AssetsCount}} assets)
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div style="clear: both;"></div>

                            <!-- Accept All Disposition (Hidden) -->
                            <div class="col-md-4" style="display: none;">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="cpalletForm.AcceptAllDisposition" aria-label="AcceptAllDisposition" ng-true-value="'1'" ng-false-value="'0'" class="md-primary" ng-disabled="cpalletForm.CustomPalletID && cpalletForm.AssetsCount > 0"> Accept All Disposition </md-checkbox>
                                </md-input-container>
                                <div class="error-space"></div>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Location Type</label>
                                    <md-select name="LocationType" ng-model="cpalletForm.LocationType" required aria-label="select" ng-change="onLocationTypeChange()">
                                        <md-option value="WIP">WIP</md-option>
                                        <md-option value="Outbound Storage">Outbound Storage</md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.LocationType.$error" multiple ng-if='material_signup_form.LocationType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Location Group -->
                            <div class="col-md-4 noerrorspacer">
                                <md-input-container class="md-block">
                                    <label>Location Group</label>
                                    <div style="display: flex; align-items: center;">
                                        <md-autocomplete flex style="margin-bottom:0px !important; margin-top:0px !important; padding-top: 0px !important; flex: 1; margin-right: 8px;"
                                            md-input-name="location"
                                            md-input-maxlength="100"
                                            ng-disabled="cpalletForm.FacilityID == 0 || !cpalletForm.FacilityID || !cpalletForm.LocationType"
                                            md-no-cache="noCache"
                                            md-search-text-change="LocationChange(cpalletForm.group)"
                                            md-search-text="cpalletForm.group"
                                            md-items="item in queryLocationSearch(cpalletForm.group)"
                                            md-item-text="item.GroupName"
                                            md-selected-item-change="selectedLocationChange(item)"
                                            md-min-length="0"
                                            ng-model-options='{ debounce: 1000 }'
                                            placeholder="Search Location Group">
                                            <md-item-template>
                                                <span md-highlight-text="cpalletForm.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                            </md-item-template>
                                            <md-not-found>
                                                No Records matching "{{cpalletForm.group}}" were found.
                                            </md-not-found>
                                            <div ng-messages="material_signup_form.group.$error" ng-if="material_signup_form.group.$touched">
                                                <div ng-message="required">No Records matching.</div>
                                            </div>
                                        </md-autocomplete>
                                        <md-button class="md-raised md-warn" ng-click="clearLocationGroup()" ng-if="cpalletForm.group" style="min-width: 32px; height: 32px; margin: 0; padding: 0;">
                                            <i class="material-icons" style="font-size: 16px;">clear</i>
                                        </md-button>
                                    </div>
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                           

                            <!-- Location Name (when selected) -->
                            <div class="col-md-4" ng-show="cpalletForm.LocationName != '' && cpalletForm.LocationName">
                                <md-input-container class="md-block">
                                    <label>Location Name</label>
                                    <input type="text" name="LocationName"  ng-model="cpalletForm['LocationName']" data-ng-disabled="true"  />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <!-- Parent Bin -->
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Parent Bin</label>
                                    <input ng-model="cpalletForm.ParentBinName" type="text" placeholder="Enter Parent Bin Name" ng-change="onParentBinChange()">
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <!-- Physical/Logical -->
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Physical / Logical</label>
                                    <md-select name="BinType" ng-model="cpalletForm.BinType" required aria-label="select">
                                        <md-option value="Physical"> Physical </md-option>
                                        <md-option value="Logical"> Logical </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.BinType.$error" multiple ng-if='material_signup_form.BinType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>


                            <!-- Notes -->
                            <div class="col-md-12">
                                <md-input-container class="md-block">
                                    <label>Notes</label>
                                    <input type="text" name="Notes"  ng-model="cpalletForm['Description']"  ng-maxlength="250" />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.Notes.$error" multiple ng-if='material_signup_form.Notes.$dirty'>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 250.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- Reference Fields -->
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Reference Type</label>
                                    <md-select name="ReferenceTypeID" ng-model="cpalletForm.ReferenceTypeID" aria-label="select" ng-change="GetReferenceType()">
                                        <md-option ng-repeat="ref_type in ReferenceType" value="{{ref_type.ReferenceTypeID}}"> {{ref_type.ReferenceType}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.ReferenceTypeID.$error" multiple ng-if='material_signup_form.ReferenceTypeID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <md-switch ng-model="cpalletForm.ReferenceIDRequired" ng-disabled="true" aria-label="ReferenceIDRequired" ng-true-value="'1'" ng-false-value="'0'" class="md-primary">
                                        Reference ID Required
                                    </md-switch>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Reference ID</label>
                                    <input name="ReferenceID" ng-model="cpalletForm.ReferenceID" ng-required="cpalletForm.ReferenceIDRequired == '1'" />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.ReferenceID.$error" multiple ng-if='material_signup_form.ReferenceID.$dirty'>
                                            <div ng-message="required">Reference ID is required when Reference Type requires it.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>



                            <!-- Maximum Assets, Status, Customer Lock -->
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Maximum Assets</label>
                                    <input type="number" required name="MaximumAssets" ng-model="cpalletForm['MaximumAssets']" ng-min="1" ng-max="10000" />
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.MaximumAssets.$error" multiple ng-if='material_signup_form.MaximumAssets.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="min">Minimum 1.</div>
                                            <div ng-message="max">Maximum 10000.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="StatusID" ng-model="cpalletForm.StatusID" required aria-label="select" ng-change="GetStatus()">
                                        <md-option ng-repeat="sta_tus in status" value="{{sta_tus.StatusID}}"> {{sta_tus.Status}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div ng-messages="material_signup_form.StatusID.$error" multiple ng-if='material_signup_form.StatusID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <md-switch ng-model="cpalletForm.CustomerLock" aria-label="Customer Lock" class="md-primary" ng-disabled="cpalletForm.CustomPalletID && cpalletForm.AssetsCount > 0">
                                        Customer Lock
                                    </md-switch>
                                    </md-input-container>
                            </div>

                            <!-- Customer (conditional) and Mobility Name (conditional) -->
                            <div class="col-md-6" ng-if="cpalletForm.CustomerLock">
                                <md-input-container class="md-block">
                                    <label>Customer</label>
                                    <md-select name="AWSCustomerID" ng-model="cpalletForm.AWSCustomerID" aria-label="select" ng-disabled="cpalletForm.CustomPalletID && cpalletForm.AssetsCount > 0">
                                        <md-option ng-repeat="customer in customers" value="{{customer.AWSCustomerID}}"> {{customer.Customer}} </md-option>
                                    </md-select>
                                </md-input-container>
                            </div>

                            <div class="col-md-6" ng-if="cpalletForm.CustomPalletID">
                                <md-input-container class="md-block">
                                    <label>Mobility Name</label>
                                    <input type="text" name="MobilityName" ng-model="cpalletForm['MobilityName']" ng-maxlength="100" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="col-md-12 btns-row" style="text-align: center; margin-top: 30px; clear: both;">
                                <a href="#!/BinList" style="text-decoration: none; margin-right: 10px;">
                                    <md-button class="md-button md-raised btn-w-md md-default">
                                        Cancel
                                    </md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary"
                                    data-ng-disabled="material_signup_form.$invalid || cpalletForm.busy" ng-click="savebincreation()">
                                    <span ng-show="!cpalletForm.busy">Save</span>
                                    <span ng-show="cpalletForm.busy">
                                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular>
                                    </span>
                                </md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>