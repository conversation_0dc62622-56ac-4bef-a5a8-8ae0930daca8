<?php
session_start();
include_once("admin.class.php");
class ExceptionReasonClass extends AdminClass
{
    public function ExceptionReason($data)
    {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }
        $json = array(
            'Success' => false,
            'Result' => $data
        );
        /*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/
        //return json_encode($json);
        if ($data['ExceptionReasonID'] == '') { //If New Class
            //$sql = "CALL ClassCreate('".mysqli_real_escape_string($this->connectionlink,$data['ProductClassName'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProductClassDesc'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProductClassStatus'])."')";
            $duplicate = $this->CheckDuplicate('New', 'ExceptionReason', 'ExceptionReason', $data['ExceptionReason'], true, '', '');
            if ($duplicate) {
                $json['Success'] = false;
                $json['Result'] = 'ExceptionReason already Exists';
                return json_encode($json);
            }
            $query = "insert into ExceptionReason (ExceptionReason,Status,DateCreated,CreatedBy) values ('" . mysqli_real_escape_string($this->connectionlink, $data['ExceptionReason']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',NOW(),'" . $_SESSION['user']['UserId'] . "')";
        } else {
            $duplicate = $this->CheckDuplicate('Edit', 'ExceptionReason', 'ExceptionReason', $data['ExceptionReason'], true, 'ExceptionReasonID', $data['ExceptionReasonID']);
            if ($duplicate) {
                $json['Success'] = false;
                $json['Result'] = 'ExceptionReason already Exists';
                return json_encode($json);
            }
            $query = "update ExceptionReason set ExceptionReason='" . mysqli_real_escape_string($this->connectionlink, $data['ExceptionReason']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',UpdatedDate =NOW(),UpdatedBy='" . $_SESSION['user']['UserId'] . "' where ExceptionReasonID='" . mysqli_real_escape_string($this->connectionlink, $data['ExceptionReasonID']) . "'";
        }
        $q = mysqli_query($this->connectionlink, $query);
        if (mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
            return json_encode($json);
        }
        if ($data['ExceptionReasonID'] == '') {
            $insert_id = mysqli_insert_id($this->connectionlink);
            $json['Success'] = true;
            $json['Result'] = "New ExceptionReason created";
            $json['parttypeid'] = $insert_id;
        } else {
            $json['Success'] = true;
            $json['Result'] = "ExceptionReason modified";
        }
        return json_encode($json);
    }

    public function GetExceptionReasonList($data)
    {
        try {
            if (!isset($_SESSION['user'])) {
                $json['Success'] = false;
                $json['Result'] = 'Login to continue';
                return json_encode($json);
            }
            $json = array(
                'Success' => false,
                'Result' => $data['ExceptionReasonID']
            );

            /*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Recoverytype Page';
				return json_encode($json);
			}*/

            $query = "select C.*,ss.StatusName from ExceptionReason C join statusses ss on C.Status = ss.StatusID";
            if (count($data[0]) > 0) {
                foreach ($data[0] as $key => $value) {
                    if ($value != '') {
                        if ($key == 'ExceptionReason') {
                            $query = $query . " AND C.ExceptionReason like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
                        }
                        if ($key == 'StatusName') {
                            $query = $query . " AND ss.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
                        }
                    }
                }
            }

            if ($data['OrderBy'] != '') {
                if ($data['OrderByType'] == 'asc') {
                    $order_by_type = 'asc';
                } else {
                    $order_by_type = 'desc';
                }

                if ($data['OrderBy'] == 'ExceptionReason') {
                    $query = $query . " order by C.ExceptionReason " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'StatusName') {
                    $query = $query . " order by ss.StatusName " . $order_by_type . " ";
                }
            } else {
                $query = $query . " order by ExceptionReason desc ";
            }

            $query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));
            $q = mysqli_query($this->connectionlink, $query);
            if (mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if (mysqli_affected_rows($this->connectionlink) > 0) {
                $i = 0;
                while ($row = mysqli_fetch_assoc($q)) {
                  if($row['Default'] == '1'){
                    $row['Default'] = true;
                  }else{
                    $row['Default'] = false;
                  }
                    $result[$i] = $row;
                    $i++;
                }
                $json['Success'] = true;
                $json['Result'] = $result;
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Data Available";
            }

            if ($data['skip'] == 0) {

                $query1 = "select count(*) from ExceptionReason C join statusses ss on C.Status = ss.StatusID";
                if (count($data[0]) > 0) {
                    foreach ($data[0] as $key => $value) {
                        if ($value != '') {

                            if ($key == 'ExceptionReason') {
                                $query1 = $query1 . " AND C.ExceptionReason like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
                            }
                            if ($key == 'StatusName') {
                                $query1 = $query1 . " AND ss.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
                            }
                        }
                    }
                }

                $q1 = mysqli_query($this->connectionlink, $query1);
                if (mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if (mysqli_affected_rows($this->connectionlink) > 0) {
                    $row1 = mysqli_fetch_assoc($q1);
                    $count = $row1['count(*)'];
                }
                $json['total'] = $count;
            }
            return json_encode($json);
        } catch (Exception $ex) {
            $json['Success'] = false;
            $json['Result'] = $ex->getMessage();
            return json_encode($json);
        }
    }

    public function GenerateExceptionReasonListxls($data)
    {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }
        $json = array(
            'Success' => false,
            'Result' => $data
        );

        $transaction = 'Administration ---> eViridis Administration ---> ExceptionReason';
        $description = 'ExceptionReason List Exported';
        $this->RecordUserTransaction($transaction, $description);

        $_SESSION['ExceptionReasonListxls'] = $data;
        $json['Success'] = true;
        //$json['Result'] = $result;
        return json_encode($json);
    }

    public function GetExceptionReasonDetails($data)
    {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }
        $json = array(
            'Success' => false,
            'Result' => $data
        );

        /*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/

        //return json_encode($json);
        $query = "select * from ExceptionReason where ExceptionReasonID = '" . mysqli_real_escape_string($this->connectionlink, $data['ExceptionReasonID']) . "' ORDER BY ExceptionReason";
        $q = mysqli_query($this->connectionlink, $query);
        if (mysqli_error($this->connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($this->connectionlink);
        }
        if (mysqli_affected_rows($this->connectionlink) > 0) {
            $row = mysqli_fetch_assoc($q);
            $json['Success'] = true;
            $json['Result'] = $row;
        } else {
            $json['Success'] = false;
            $json['Result'] = "Invalid ExceptionReason ID";
        }
        return json_encode($json);
    }

    public function GetAllExceptionReasons($data)
    {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }
        $json = array(
            'Success' => false,
            'Result' => $data
        );
        try {
            if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
                $json['Success'] = false;
                $json['Result'] = 'No Access to Workflow Page';
                return json_encode($json);
            }
            $query = "select ExceptionReasonID, ExceptionReason from ExceptionReason where Status = '1' order by `ExceptionReason`";
            $q = mysqli_query($this->connectionlink, $query);
            if (mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if (mysqli_affected_rows($this->connectionlink) > 0) {
                $result =  array();
                $i = 0;
                while ($row = mysqli_fetch_assoc($q)) {
                    $result[$i] = $row;
                    $i++;
                }
                $json['Success'] = true;
                $json['Result'] = $result;
                return json_encode($json);
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Dispositions Available";
            }
            return json_encode($json);
        } catch (Exception $e) {
            $json['Success'] = false;
            $json['Result'] = $e->getMessage();
            return json_encode($json);
        }
    }

    public function UpdateDefaultExceptionReason($data)
  	{
  		if (!isset($_SESSION['user'])) {
  			$json['Success'] = false;
  			$json['Result'] = 'Login to continue';
  			return json_encode($json);
  		}
  		$json = array(
  			'Success' => false,
  			'Result' => $data
  		);


  		if ($data['Default'] == 'true') {
  			$data['Default'] = 1;
  			$query2 = "UPDATE ExceptionReason SET `Default` = '0'";
  			$q2 = mysqli_query($this->connectionlink, $query2);
  			if (mysqli_error($this->connectionlink)) {
  				$json['Success'] = false;
  				$json['Result'] = mysqli_error($this->connectionlink);
  				return json_encode($json);
  			}
  		} else {
  			$data['Default'] = 0;
  		}
  		$query = "UPDATE ExceptionReason SET `Default` = '" . $data['Default'] . "' WHERE ExceptionReasonID = '" . $data['ExceptionReasonID'] . "'";
  		$q = mysqli_query($this->connectionlink, $query);
  		if (mysqli_error($this->connectionlink)) {
  			$json['Success'] = false;
  			$json['Result'] = mysqli_error($this->connectionlink);
  			return json_encode($json);
  		} else {
  			$json['Success'] = true;
  			$json['Result'] = "Updated Successfully";
  			return json_encode($json);
  		}
  	}

}
