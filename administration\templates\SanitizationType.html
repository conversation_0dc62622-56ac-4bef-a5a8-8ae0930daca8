
<div class="row page" data-ng-controller="SanitizationType">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Sanitization Type</span>
                        <div flex></div>
                            <a href="#!/SanitizationTypeList">
                            <md-button class="md-raised btn-w-md md-default pull-right" style="margin-top:-6px;"><span class="fa fa-chevron-left"></span> Back to List</md-button>
                        </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Sanitization Type</label>
                                    <input type="text" name="value"  ng-model="SanitizationType['value']"  required ng-maxlength="50" ng-disabled="SanitizationType.value_id" />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                           
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="description"  ng-model="SanitizationType['description']"  ng-maxlength="100" />
                                    <div ng-messages="material_signup_form.description.$error" multiple ng-if='material_signup_form.description.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 1000.</div>                           
                                    </div>

                                </md-input-container>
                            </div>
                           
                            <div class="col-md-4">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="status" ng-model="SanitizationType.status" required aria-label="select">
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="Inactive"> Inactive </md-option>
                                    </md-select>   
                                    <div ng-messages="material_signup_form.status.$error" multiple ng-if='material_signup_form.status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                            </div>
                            <div style="clear:both;"></div>
                            <div class="col-md-8">
                                <label style="font-size:13px; color:#353535">File Upload</label><br />
                                <div class="upload-btn-wrapper text-center" ng-show="SanitizationType.file_url == '' || SanitizationType.file_url == NULL || !SanitizationType.file_url">
                                    
                                    <md-button class="md-button md-raised btn-w-md md-primary" style="display: flex; cursor: pointer;line-height: 26px;">
                                        <span ng-show="! busy">
                                            <i class="material-icons mr-5" style="margin-top: 2px; float: left;">file_upload</i>Upload File
                                        </span>
                                        
                                        <span ng-show="busy">
                                            <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular>
                                        </span>
                                    </md-button>                           
                                    <input type="file" ng-file-select="onFileSelect($files)" id="SanitizationFile">                                         
                                </div>

                                
                                <div ng-show="SanitizationType.file_url != ''" class="border-radius" style="background-color:#f4f4f4; display: inline-flex; border:1px solid #dbdbdb; padding: 6px;">                                    
                                    <a class="mr-5" target="_blank" href="../download_s3.php?key={{SanitizationType.file_url}}">{{SanitizationType.file_url}}</a>
                                    <i class="material-icons text-danger" aria-hidden="true" ng-show="SanitizationType.file_url != '' && SanitizationType.file_url != NULL" class="md-raised md-dark" ng-click="DeleteMPNAttributeFile($event)">close</i>
                                </div>

                            </div>

                            <div class="col-md-12 btns-row">
                                <a href="#!/SanitizationTypeList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || SanitizationType.busy" ng-click="SaveSanitizationType()">
                                <span ng-show="! SanitizationType.busy">Save</span>
                                <span ng-show="SanitizationType.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>