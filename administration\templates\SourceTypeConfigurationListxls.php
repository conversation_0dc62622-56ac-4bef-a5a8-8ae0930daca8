<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['SourceTypeConfigurationListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "SourceTypeConfigurationList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$datatoday = array('Generated Date',$today);
$datahead = array('Source Type Configuration List');
$header = array('Facility','Customer ID','Material Type','Source Type','Status');

        $sql = "select s.*,f.FacilityName,c.Customer,m.MaterialType,ct.Cumstomertype from source_type_configuration s 
            left join facility f on s.FacilityID = f.FacilityID 
            left join aws_customers c on s.AWSCustomerID = c.AWSCustomerID 
            left join material_types m on s.MaterialTypeID = m.MaterialTypeID 
            left join customertype ct on s.idCustomertype = ct.idCustomertype where s.ConfigurationID 
            ";
    
        if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                    if($key == 'FacilityName') {
                        $sql = $sql . " AND f.FacilityName like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                    }                   
                    if($key == 'Customer') {
                        $sql = $sql . " AND c.Customer like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                    }
                    if($key == 'MaterialType') {
                        $sql = $sql . " AND m.MaterialType like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                    }
                    if($key == 'Cumstomertype') {
                        $sql = $sql . " AND ct.Cumstomertype like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                    }
                    if($key == 'Status') {
                        $sql = $sql . " AND s.Status like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                    }
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

            if($data['OrderBy'] == 'FacilityName') {
                $sql = $sql . " order by f.FacilityName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'Customer') {
                $sql = $sql . " order by c.Customer ".$order_by_type." ";
            } else if($data['OrderBy'] == 'MaterialType') {
                $sql = $sql . " order by m.MaterialType ".$order_by_type." ";
            } else if($data['OrderBy'] == 'Cumstomertype') {
                $sql = $sql . " order by ct.Cumstomertype ".$order_by_type." ";
            } else if($data['OrderBy'] == 'Status') {
                $sql = $sql . " order by s.Status ".$order_by_type." ";
            }           
        } else {
            $sql = $sql . " order by c.Customer desc ";
        }
$query = mysqli_query($connectionlink,$sql);
if(mysqli_error($connectionlink)) {
    echo mysqli_error($connectionlink);    
}
            while($row = mysqli_fetch_assoc($query))
            {
                $row2  = array($row['FacilityName'],$row['Customer'],$row['MaterialType'],$row['Cumstomertype'],$row['Status']);
                $rows[] = $row2;
            }

$sheet_name = 'Source Type Configuration List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 