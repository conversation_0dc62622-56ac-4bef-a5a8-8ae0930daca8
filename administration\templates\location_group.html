<div class="row page" data-ng-controller="LocationGroup">
    <div class="col-md-12">
        <article class="article">

            <style>
                md-select[disabled], .md-input[disabled]{ margin-bottom: 12px;}
            </style>

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Location Group</span>
                        <div flex></div>
                            <a href="#!/LocationGroup" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">add</i> Create New Group
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Group Name</label>
                                    <input type="text" name="GroupName"  ng-model="location['GroupName']"  required ng-maxlength="45" />
                                    <div ng-messages="material_signup_form.GroupName.$error" multiple ng-if='material_signup_form.GroupName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Group Description</label>
                                    <input type="text" name="GroupDescription"  ng-model="location['GroupDescription']"   ng-maxlength="250" />
                                    <div ng-messages="material_signup_form.GroupDescription.$error" multiple ng-if='material_signup_form.GroupDescription.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>   
                                    <!-- <md-select name="FacilityID" ng-model="location.FacilityID" aria-label="select" ng-change="GetLocations();GetBinTypes()" ng-disabled="location.GroupID" required>  -->
                                    <md-select name="FacilityID" ng-model="location.FacilityID" aria-label="select" ng-change="GetLocations();" ng-disabled="location.GroupID" required> 
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                    
                                    </md-select>   
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Total Locations</label>
                                    <input type="number" name="TotalLocations"  ng-model="location['TotalLocations']" ng-disabled="location.SortLocation == '1' || location.GroupID" required ng-max="500" ng-min="1" ng-change="ValidateSortLocation()"/>
                                    <div ng-messages="material_signup_form.TotalLocations.$error" multiple ng-if='material_signup_form.TotalLocations.$dirty'>     
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="max">Max 500.</div>  
                                        <div ng-message="min">Min 1.</div>                                               
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Lock</label>
                                    <md-select name="Locked" ng-model="location.Locked" required aria-label="select" ng-disabled="location.GroupID" ng-init="location.Locked='2'">
                                        <md-option value="2"> Unlocked </md-option>
                                        <md-option value="1"> Locked </md-option>
                                    </md-select>   
                                    <div ng-messages="material_signup_form.Locked.$error" multiple ng-if='material_signup_form.Locked.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Location Type</label>
                                    <md-select name="LocationType" ng-model="location.LocationType" required aria-label="select" ng-change="location.LableID = '';GetLocationTypeLabels();ValidateSortLocation()"> 
                                        <md-option value="Outbound Storage"> Outbound Storage </md-option>
                                        <md-option value="Inbound Storage"> Inbound Storage </md-option>
                                        <md-option value="WIP"> WIP </md-option>
                                        <!-- <md-option value="B2D"> B2D </md-option> -->
                                    </md-select>   
                                    <div class="error-space"> 
                                    <div class="md-errors-spacer" ng-messages="material_signup_form.LocationType.$error" multiple ng-if='material_signup_form.LocationType.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Label</label>   
                                    <md-select name="LableID" ng-model="location.LableID" required aria-label="select" ng-change="GetLabel()">
                                        <md-option ng-repeat="lable in lables" value="{{lable.id}}"> {{lable.lable_name}} </md-option>
                                    </md-select> 
                                    <div class="error-space"> 
                                    <div class="md-errors-spacer" ng-messages="material_signup_form.LableID.$error" multiple ng-if='material_signup_form.LableID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>  
                                    </div>     
                                </md-input-container>
                            </div>
                            
                            <!-- <div class="col-md-3" ng-if="location.LocationType == 'WIP'">
                                <md-input-container class="md-block">
                                    <label>Bin Type</label>   
                                    <md-select name="BinTypeID" id="BinTypeID" ng-model="location.BinTypeID" aria-label="select" ng-disabled="location.GroupID || !location.GroupName" required > -->
                                    <!-- <md-select name="BinTypeID" id="BinTypeID" ng-model="location.BinTypeID" aria-label="select" ng-disabled="! location.AllEmpty" required> 
                                    <md-select name="BinTypeID" id="BinTypeID" ng-model="location.BinTypeID" aria-label="select" required> 
                                        <md-option ng-repeat="bt in BinTypes" value="{{bt.BinTypeID}}"> {{bt.BinType}} </md-option>                                    
                                    </md-select>   
                                </md-input-container>
                            </div> -->

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Location Status</label>
                                    <md-select name="LocationStatus" ng-model="location.LocationStatus" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In active </md-option>
                                    </md-select> 
                                     <div class="error-sapce"></div>    
                                    <div ng-messages="material_signup_form.LocationStatus.$error" multiple ng-if='material_signup_form.LocationStatus.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                            </div>
                           <div class="col-md-3" ng-show="location.GroupID">
                                <md-input-container class="md-block includedsearch">
                                    <label>Add More Locations</label>
                                    <input type="number" name="MoreLocationsCount" ng-model="location['MoreLocationsCount']" min="1" max="100" ng-disabled=" location.SortLocation == '1'" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" type="button" aria-label="searchSerialNO" ng-click="AddMoreLocations()" ng-disabled="!location.MoreLocationsCount">
                                        <i class="material-icons mt-3">add</i>
                                    </md-button>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="location.SortLocation" aria-label="SortLocation" ng-true-value="'1'" ng-false-value="'0'" class="md-primary" ng-change="GetTotallocations()" ng-disabled=" canSortLocationDisable()"> Sort Location </md-checkbox>
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/LocationGroupList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || location.busy" ng-click="CreateLocationGroup()">
                                <span ng-show="! location.busy">Save</span>
                                <span ng-show="location.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>

                <md-card class="no-margin-h" ng-if="location.GroupID">
                    
                <md-toolbar class="md-table-toolbar md-default"  ng-init="locationPanel = true;" ng-show="ProductClass.ProductClassID">
                    <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="locationPanel = !locationPanel">                            
                        <i class="material-icons" ng-click="locationPanel = !locationPanel" ng-show="locationPanel">keyboard_arrow_up</i>
                        <i class="material-icons" ng-click="locationPanel = !locationPanel" ng-show="! locationPanel">keyboard_arrow_down</i>
                        <span>Attributes</span>
                    </div>
                </md-toolbar>
                <div class="row" ng-show="locationPanel">
                            
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <!-- <div class="tablemovebtns">
                                <a class="md-button md-raised md-default" id="left-button"><i class="material-icons">keyboard_arrow_left</i></a>
                            </div>
                            <div class="tablemovebtns">
                                <a class="md-button md-raised md-default" id="right-button"><i class="material-icons">keyboard_arrow_right</i></a>
                            </div>  -->

                            <div ng-show="pagedItems" class="pull-right pageditems">
                                <small>
                                    Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                    to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                        <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                    of <span style="font-weight:bold;">{{total}}</span>
                                </small>
                            </div>
                            <div style="clear:both;"></div>

                            <div class="table-container table-responsive" style="overflow: auto;">
                                
                                <table class="table table-striped">
                                    <thead>
                                        <tr class="th_sorting">
                                            <th style="min-width: 40px;">Print</th>
                                            <th style="cursor:pointer;min-width:180px;" ng-click="MakeOrderBy('LocationName')" ng-class="{'orderby' : OrderBy == 'LocationName'}">
                                                <div>                               
                                                    Location Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationName'"></i>                                 
                                                    <span ng-show="OrderBy == 'LocationName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>
                                            </th>
                                                                                                                                    
                                            <th style="cursor:pointer; min-width:180px;" ng-click="MakeOrderBy('LocationDesc')" ng-class="{'orderby' : OrderBy == 'LocationDesc'}">                           
                                                <div>                               
                                                    Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationDesc'"></i>                                    
                                                    <span ng-show="OrderBy == 'LocationDesc'">                                 
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>
                
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                          
                                                <div>                               
                                                    Facility<i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                  
                                                    <span ng-show="OrderBy == 'FacilityName'">                                  
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('currentItemID')" ng-class="{'orderby' : OrderBy == 'currentItemID'}">                          
                                                <div>                               
                                                    Item ID<i class="fa fa-sort pull-right" ng-show="OrderBy != 'currentItemID'"></i>                                  
                                                    <span ng-show="OrderBy == 'currentItemID'">                                  
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('LockType')" ng-class="{'orderby' : OrderBy == 'LockType'}">                          
                                                <div>                               
                                                    Lock<i class="fa fa-sort pull-right" ng-show="OrderBy != 'LockType'"></i>                                  
                                                    <span ng-show="OrderBy == 'LockType'">                                  
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>
                                             <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">                          
                                                <div>                               
                                                    Status<i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>                                  
                                                    <span ng-show="OrderBy == 'StatusName'">                                  
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>

                                        </tr>
                                        
                                        <tr class="errornone">                        
                                            <td>&nbsp;</td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="LocationName" ng-model="filter_text[0].LocationName" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
    
                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="LocationDesc" ng-model="filter_text[0].LocationDesc" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td> 
                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="currentItemID" ng-model="filter_text[0].currentItemID" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="LockType" ng-model="filter_text[0].LockType" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td>                   
                                             <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td>
                                        </tr>
                                    </thead>
                                    
                                    <tbody ng-show="pagedItems.length > 0">
                                        <tr ng-repeat="product in pagedItems">
                                            <td><a target="_blank" href="../label/master/examples/locationlabel.php?id={{product.LocationID}}"><i class="material-icons print mt-10">print</i></a></td>
    
                                            <td>
                                                {{product.LocationName}}                            
                                            </td>                       
                                             <td>
                                                {{product.LocationDesc}}
                                            </td> 
                                            <td>
                                                {{product.FacilityName}}
                                            </td>
                                             <td>
                                                <span ng-show="product.currentItemType != NULL && product.currentItemType != ''">{{product.currentItemID}}  ({{product.currentItemType}})</span>
                                            </td>  
                                            <td>
                                                {{product.LockType}}
                                            </td>
                                             <td>
                                                {{product.StatusName}}
                                            </td> 
                                        </tr>
                                    </tbody>
                                    
                                    <tfoot>
                                        <tr>
                                            <td colspan="7">
                                                <div>
                                                    <ul class="pagination">
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="firstPage()"><< First</a>
                                                        </li>
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="prevPage()"><< Prev</a>
                                                        </li>
                                                        <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                            <a style="cursor:pointer;">{{n+1}}</a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="nextPage()">Next >></a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="lastPage()">Last >></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>   
                                        </tr>             
                                    </tfoot>
                                </table>
                            </div>
                            
                        </div>
                    </div>
                </div>
                
            </md-card>
        </article>        
    </div>
</div>