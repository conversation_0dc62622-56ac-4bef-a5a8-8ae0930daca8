(function () {
    'use strict';
    angular.module('app').controller("bin_to_container_consolidation", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams) {
        $scope.FromCustomPallet = {};
        $scope.ToCustomPallet = {};
        $scope.SearchCustomPallet = {};
        $scope.test = 'working';
        $scope.GetCustomPalletDetails = function (type,BinName) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/bin_to_container_consolidation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCustomPalletDetails&BinName='+BinName,                    
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        if(type == 'From') {
                            $scope.FromCustomPallet = data.Result;
                        } else if(type == 'Search') {
                            $scope.SearchCustomPallet = data.Result;
                        } else {
                            $scope.ToCustomPallet = data.Result;
                        }                        
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        if(type == 'From') {
                            $scope.FromCustomPallet = {};
                        } else {
                            $scope.ToCustomPallet = {};
                        }
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {          
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        $scope.GetShipmentContainerDetails = function () {
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'administration/includes/bin_to_container_consolidation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetShipmentContainerDetails&ShippingContainerID='+$scope.ToBinName,                    
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {  
                        if($scope.FromCustomPallet.disposition_id) {
                            if($scope.FromCustomPallet.disposition_id != data.Result.disposition_id) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content('Container Disposition is not matching with Bin Disposition')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            } else {
                                $scope.ToCustomPallet = data.Result;
                            }
                        } else {
                            $scope.ToCustomPallet = data.Result;
                        }                        
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.ToCustomPallet = {};
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {          
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });

        };


        $scope.ConsolidateBINToContainer = function (ev) {
            if($scope.FromCustomPallet.CustomPalletID > 0 && $scope.ToCustomPallet.ShippingContainerID != '') {
                
                var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Consolidate BIN to Container ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Yes')
                .cancel('Cancel');
                $mdDialog.show(confirm).then(function() {
                    $rootScope.$broadcast('preloader:active');
                    $scope.busy = true;
                    jQuery.ajax({
                        url: host+'administration/includes/bin_to_container_consolidation_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ConsolidateBINToContainer&FromCustomPalletID='+$scope.FromCustomPallet.CustomPalletID+'&ShippingContainerID='+$scope.ToCustomPallet.ShippingContainerID,
                        success: function(data) {
                            $scope.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            if(data.Success) {                                
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );               
                                location.reload();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );                        
                            }                                
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {     
                            $scope.busy = false;     
                            $rootScope.$broadcast('preloader:hide');     
                            initSessionTime(); $scope.$apply();
                        }
                    });

                }, function() {
                });
            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Enter Bin and Container')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        
    });
})(); 