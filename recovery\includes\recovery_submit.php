<?php
	session_start();
	include_once("../database/recovery.class.php");
	$obj = new RecoveryClass();

	if($_POST['ajax'] == "GetPartTypesByRecoverytype") {
		$result = $obj->GetPartTypesByRecoverytype($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateBinParent"){
		$result = $obj->UpdateBinParent($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CloseBin"){
		$result = $obj->CloseBin($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetTopLevelAssetDetails") {
		$result = $obj->GetTopLevelAssetDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSerializedPartTypeRecoveryDetails") {
		$result = $obj->GetSerializedPartTypeRecoveryDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateSerialRecoverypart") {
		$result = $obj->CreateSerialRecoverypart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRecoveredPartsByTopLeveAssetID") {
		$result = $obj->GetRecoveredPartsByTopLeveAssetID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateUnserialRecoverypart") {
		$result = $obj->CreateUnserialRecoverypart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetEvaluationResultsByPart") {
		$result = $obj->GetEvaluationResultsByPart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetDispositionDetailsByPart") {
		$result = $obj->GetDispositionDetailsByPart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetUnserializedPartTypeRecoveryDetails") {
		$result = $obj->GetUnserializedPartTypeRecoveryDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CloseToplevelAsset") {
		$result = $obj->CloseToplevelAsset($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "MPNValidate") {
		$result = $obj->MPNValidate($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteSerialRecoveryPart") {
		$result = $obj->DeleteSerialRecoveryPart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ReopenToplevelAsset") {
		$result = $obj->ReopenToplevelAsset($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteUnserialRecoveryPart") {
		$result = $obj->DeleteUnserialRecoveryPart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetWIPRecoveredPartsByTopLeveAssetID") {
		$result = $obj->GetWIPRecoveredPartsByTopLeveAssetID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SaveWIPRecoveryParts") {
		$result = $obj->SaveWIPRecoveryParts($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRecoveredSerialPartsByTopLeveAssetID") {
		$result = $obj->GetRecoveredSerialPartsByTopLeveAssetID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRecoveredUnserialPartsByTopLeveAssetID") {
		$result = $obj->GetRecoveredUnserialPartsByTopLeveAssetID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCurrentTime") {
		$result = $obj->GetCurrentTime($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetExceptionPartTypes") {
		$result = $obj->GetExceptionPartTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetEvaluationResultsByExceptionPart") {
		$result = $obj->GetEvaluationResultsByExceptionPart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetDispositionDetailsByExceptionPart") {
		$result = $obj->GetDispositionDetailsByExceptionPart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateSerialExceptionRecoverypart") {
		$result = $obj->CreateSerialExceptionRecoverypart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetWIPExceptionRecoveredPartsBySiteID") {
		$result = $obj->GetWIPExceptionRecoveredPartsBySiteID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SaveWIPExceptionRecoveryParts") {
		$result = $obj->SaveWIPExceptionRecoveryParts($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetExceptionReasonList") {
		$result = $obj->GetExceptionReasonList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteSerialExceptionRecoveryPart") {
		$result = $obj->DeleteSerialExceptionRecoveryPart($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "RecoveryPendingSave") {
		$result = $obj->RecoveryPendingSave($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "RecoveryPendingClose") {
		$result = $obj->RecoveryPendingClose($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SaveUniqueID") {
		$result = $obj->SaveUniqueID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CloseUniqueID") {
		$result = $obj->CloseUniqueID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetMPNFromSerial") {
		$result = $obj->GetMPNFromSerial($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateRackItemSerial") {
		$result = $obj->ValidateRackItemSerial($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateMediaSerial") {
		$result = $obj->ValidateMediaSerial($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateMediaSerial") {
		$result = $obj->CreateMediaSerial($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateComponentRecoveryRecord") {
		$result = $obj->CreateComponentRecoveryRecord($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GenerateSerialNumber") {
		$result = $obj->GenerateSerialNumber($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateIfMPN") {
		$result = $obj->ValidateIfMPN($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CheckIfServerIsScanned") {
		$result = $obj->CheckIfServerIsScanned($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSerializedPartTypes") {
		$result = $obj->GetSerializedPartTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SaveRecoveryConfiguration") {
		$result = $obj->SaveRecoveryConfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRecoverConfigurationList"){
  		$result = $obj->GetRecoverConfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRecoveryConfigurationDetails"){
  		$result = $obj->GetRecoveryConfigurationDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetComponentDispositionDetails"){
		$result = $obj->GetComponentDispositionDetails($_POST);
	  echo $result;
  	}

	if($_POST['ajax'] == "GetComponentRecoveryInputResults"){
		$result = $obj->GetComponentRecoveryInputResults($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "MakeDefaultInputResult"){
		$result = $obj->MakeDefaultInputResult($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "PopulateDefaultEvaluationResult"){
		$result = $obj->PopulateDefaultEvaluationResult($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "SavePendingSerial"){
		$result = $obj->SavePendingSerial($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetAllFacilities"){
		$result = $obj->GetAllFacilities($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "DeleteRecoverConfiguration"){
		$result = $obj->DeleteRecoverConfiguration($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetRecoveryStations") {
		$result = $obj->GetRecoveryStations($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ConsolidateBin") {
		$result = $obj->ConsolidateBin($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "ConsolidateShipmentContainer") {
		$result = $obj->ConsolidateShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCOOList") {
		$result = $obj->GetCOOList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBatchRecoveryDispositionDetails") {
		$result = $obj->GetBatchRecoveryDispositionDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBatchRecoveryDispositionDetails1") {
		$result = $obj->GetBatchRecoveryDispositionDetails1($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SaveBatchRecoveryParts") {
		$result = $obj->SaveBatchRecoveryParts($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBulkRecoveryDispositions") {
		$result = $obj->GetBulkRecoveryDispositions($_POST);
		echo $result;
	}

	// Create Bin functionality routes
	if($_POST['ajax'] == "GetBinPackageTypes") {
		$result = $obj->GetBinPackageTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetStationLocationGroup") {
		$result = $obj->GetStationLocationGroup($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetStationLocationGroupAndAssignLocation") {
		$result = $obj->GetStationLocationGroupAndAssignLocation($_POST);
		echo $result;
	}



	if($_POST['ajax'] == "GetStationDetails") {
		$result = $obj->GetStationDetails($_POST);
		echo $result;
	}



	if($_POST['ajax'] == "GetDisposition") {
		$result = $obj->GetDisposition($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "SaveBin") {
		$result = $obj->SaveBin($_POST);
		echo $result;
	}

?>
