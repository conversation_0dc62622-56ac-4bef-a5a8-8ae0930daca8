<div ng-controller = "help" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">
                <div class="body_inner_content">
                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <span>Bin</span>
                                <div flex></div>
                                <a href="#!/Help" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">chevron_left</i> Back To Help List
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <p class="mt-10">The <strong>Bin</strong> second level page controls bins in the system.</p>

                                    <div class="callout callout-success">
                                        <code>Step 1:</code> Click on <strong>Administration </strong> to open the sub-module list. Click <strong>eViridis Administration</strong> to open the second level page list. Choose <strong>Bin</strong>.
                                    </div>

                                    <div class="image_space">
                                        <img src="../assets/images/help_images/207.jpg" style="max-width:50%;" alt="Image" />
                                    </div>

                                    <div class="callout callout-success">
                                        <code>Step 2:</code> 
                                        <p>On the <strong>Bin List</strong> page, click <span><img src="../assets/images/help_images/sorting.jpg" alt="Image" /></span> to sort by <strong>Facility, Bin Name, Qty x party Type, Mapped Stations, Disposition,</strong> or <strong>Status.</strong></p>
                                        <p>Type in the space below any of the above header categories to filter them.</p>
                                        <p>Click <strong>Export to Excel</strong> to download the the bin list.</p>
                                        <p>Click <strong>+ Create New Bin </strong> to add a new bin to the system.</p>
                                    </div>

                                    <div class="image_space">
                                        <img src="../assets/images/help_images/208.jpg" alt="Image" />
                                    </div>

                                    <div class="callout callout-success">
                                        <code>Step 3:</code> 
                                        <p>On the <strong>Bin</strong> page, choose a <strong>Facility, Room, Serialization, Bin Type, Disposition, Status,</strong> and <strong>Sub Component Based</strong> from the drop-downs.</p>
                                        <p>Click the check box next to <strong>Accept All Dispositions</strong> to designate this as bin that can accept an SN with any disposition.</p>
                                        <p>Enter a <strong>Description</strong> and <strong>Total Bins</strong> and click <strong>Save</strong>.</p>
                                    </div>

                                    <div class="image_space">
                                        <img src="../assets/images/help_images/209.jpg" alt="Image" />
                                    </div>

                                    <div class="callout callout-success">
                                        <code>Step 4:</code>On the <strong>Bin List</strong> page, click on the <span><img src="../assets/images/help_images/100.jpg" alt="Image" /></span> button to <strong>Edit</strong> a bin.                                  
                                    </div>

                                    <div class="image_space">
                                        <img src="../assets/images/help_images/210.jpg" alt="Image" />
                                    </div>

                                    <div class="callout callout-success">
                                        <code>Step 5:</code> Update any of the editable fields for this MPN (<strong>Room, Serialization, Bin Type, Description,</strong> or <strong>Status</strong>) and click <strong>Save</strong>.
                                    </div>

                                    <div class="image_space">
                                        <img src="../assets/images/help_images/211.jpg" alt="Image" />
                                    </div>

                                    <div class="callout callout-success">
                                        <code>Step 6:</code>On the <strong>Bin List</strong> page, click on the <span><img src="../assets/images/help_images/212.jpg" alt="Image" /></span> button to <strong>Print</strong> a bin label and apply it to the physical bin.                                  
                                    </div>

                                    <div class="image_space">
                                        <img src="../assets/images/help_images/213.jpg" alt="Image" />
                                    </div>

                                    <div class="callout callout-success">
                                        <code>Step 7:</code>Print the label from the PDF window that opens in the browser.
                                    </div>

                                    <div class="image_space">
                                        <img src="../assets/images/help_images/214.jpg" alt="Image" />
                                    </div>
                                    
                                </div>
                            </div>
                        </div>                        

                    </md-card>
                </div>
            </article>
        </div>
    </div>    

</div>