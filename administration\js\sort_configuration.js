(function () {
    'use strict';
    angular.module('dialogDemo1', ['ngMaterial'])
    angular.module('app').controller("SortConfiguration", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,$mdDialog,$interval,facilityinformation, UserFacility,$upload) {
        $scope.Facility = [];
        $scope.site = {};
        $scope.dispositions = [];
        $scope.CustomPallets = [];
        $scope.sortconfiguration = {};
        $scope.Groups = [];
        $scope.SortCriteria = [];
        $scope.COOList = [];
        $scope.SPECList = [];
        $scope.PartTypelist = [];
        $scope.sortconfiguration.Allmpn_id = 1;
        $scope.sortconfiguration.mpn_id = 'All';
        $scope.sortconfiguration.Allspec_id = 1;
        $scope.sortconfiguration.part_spec_id = 'All';
        $scope.sortconfiguration.Allcoo_id = 1;
        $scope.sortconfiguration.COO = 'All';
        $scope.ArchiveList = '0';

         $scope.CurrentBin = {};

        $scope.showAdvancedDi = function (ev, Bin) {
            $scope.CurrentBin = Bin;
            $mdDialog.show({
                controller: DialogController,
                templateUrl: '../administration/templates/SortConfigurationBinChange.tmpl.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                resolve: {
                    CurrentBin: function () {
                        return Bin;
                    }
                }
                //fullscreen: $scope.customFullscreen // Only for -xs, -sm breakpoints.
            })
        };

        function DialogController($scope, $mdDialog, CurrentBin, $mdToast) {
            $scope.CurrentBin = CurrentBin;
            $scope.hide = function () {
                $mdDialog.hide();
            };

            $scope.cancel = function () {
                $mdDialog.cancel();
            };

            $scope.answer = function (answer) {
                $mdDialog.hide(answer);
            };

            $scope.CurrentNewBin = { 'group': '', 'NewBinName': ''};

            $scope.isValidBinSelected = function () {

                if (($scope.CurrentNewBin.group != '') && ($scope.CurrentNewBin.NewBinName != '')) {
                    return false;
                } else {
                    return true;
                }
            };

            $scope.CreateMoveBin = function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'administration/includes/admin_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateMoveBin&sortconfigurationid=' + $scope.CurrentBin.sortconfigurationid + '&CurrentBin=' +$scope.CurrentBin.BinName + '&CurrentLocationName=' +$scope.CurrentBin.LocationID + '&CurrentBinID=' +$scope.CurrentBin.CustomPalletID + '&NewBin=' +$scope.CurrentNewBin.NewBinName + '&NewLocationName=' +$scope.CurrentNewBin.group,
                    success: function (data) {
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success')
                            );
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        $scope.$apply();
                    }
                });

            };

            function LocationChange(text) {
                $scope.CurrentBin.group = text;
            }

            function selectedLocationChange(item) {
                if(item) {
                    if(item.value) {
                        $scope.CurrentBin.group = item.value;
                        //$scope.SelectSortBin();
                    } else {
                        $scope.CurrentBin.group = '';
                    }
                } else {
                    $scope.CurrentBin.group = '';
                }
                console.log('Item changed to ' + JSON.stringify(item));
            }

            $scope.queryLocationSearch   = queryLocationSearch;
            $scope.LocationChange   = LocationChange;
            $scope.selectedLocationChange   = selectedLocationChange;
            function queryLocationSearch (query) {
                if(query) {
                    if(query != '' && query != 'undefined') {
                        //return $http.get(host+'administration/includes/admin_submit.php?ajax=GetMatchingLocations&keyword='+query+'&FacilityID='+$scope.cpalletForm.FacilityID)
                        return $http.get(host + 'administration/includes/admin_submit.php?ajax=GetMatchingLocationGroupsBin&keyword=' + query + '&LocationType=WIP')
                        .then(function(res) {
                            if(res.data.Success == true) {
                                if(res.data.Result.length > 0) {
                                    var result_array = [];
                                    for(var i=0;i<res.data.Result.length;i++) {
                                        result_array.push({value: res.data.Result[i]['GroupName'],GroupName: res.data.Result[i]['GroupName']});
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            }
                            else {
                                console.log(res.data.Result);
                                return [];
                            }
                        });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }
        };

        $scope.showAdvancedDiForm = function (ev, Bin) {
            $scope.CurrentBin = Bin;
            $mdDialog.show({
                controller: DialogControllerForm,
                templateUrl: '../administration/templates/SortConfigurationBinFormChange.tmpl.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                resolve: {
                    CurrentBin: function () {
                        return Bin;
                    }
                }
                //fullscreen: $scope.customFullscreen // Only for -xs, -sm breakpoints.
            })
        };

        function DialogControllerForm($scope, $mdDialog, CurrentBin, $mdToast) {
            $scope.CurrentBin = CurrentBin;
            $scope.hide = function () {
                $mdDialog.hide();
            };

            $scope.cancel = function () {
                $mdDialog.cancel();
            };

            $scope.answer = function (answer) {
                $mdDialog.hide(answer);
            };

            $scope.CurrentNewBin = { 'group': '', 'NewBinName': ''};

            $scope.isValidBinSelected = function () {

                if (($scope.CurrentNewBin.group != '') && ($scope.CurrentNewBin.NewBinName != '')) {
                    return false;
                } else {
                    return true;
                }
            };

            $scope.CreateMoveBin = function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'administration/includes/admin_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateMoveBin&sortconfigurationid=' + $scope.CurrentBin.sortconfigurationid + '&CurrentBin=' +$scope.CurrentBin.BinName + '&CurrentLocationName=' +$scope.CurrentBin.LocationID + '&CurrentBinID=' +$scope.CurrentBin.CustomPalletID + '&NewBin=' +$scope.CurrentNewBin.NewBinName + '&NewLocationName=' +$scope.CurrentNewBin.group,
                    success: function (data) {
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success')
                            );
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        $scope.$apply();
                    }
                });

            };

            function LocationChange(text) {
                $scope.CurrentBin.group = text;
            }

            function selectedLocationChange(item) {
                if(item) {
                    if(item.value) {
                        $scope.CurrentBin.group = item.value;
                        //$scope.SelectSortBin();
                    } else {
                        $scope.CurrentBin.group = '';
                    }
                } else {
                    $scope.CurrentBin.group = '';
                }
                console.log('Item changed to ' + JSON.stringify(item));
            }

            $scope.queryLocationSearch   = queryLocationSearch;
            $scope.LocationChange   = LocationChange;
            $scope.selectedLocationChange   = selectedLocationChange;
            function queryLocationSearch (query) {
                if(query) {
                    if(query != '' && query != 'undefined') {
                        //return $http.get(host+'administration/includes/admin_submit.php?ajax=GetMatchingLocations&keyword='+query+'&FacilityID='+$scope.cpalletForm.FacilityID)
                        return $http.get(host + 'administration/includes/admin_submit.php?ajax=GetMatchingLocationGroupsBin&keyword=' + query + '&LocationType=WIP')
                        .then(function(res) {
                            if(res.data.Success == true) {
                                if(res.data.Result.length > 0) {
                                    var result_array = [];
                                    for(var i=0;i<res.data.Result.length;i++) {
                                        result_array.push({value: res.data.Result[i]['GroupName'],GroupName: res.data.Result[i]['GroupName']});
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            }
                            else {
                                console.log(res.data.Result);
                                return [];
                            }
                        });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }
        };


        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetWorkstationGroups',
            success: function(data){
                if(data.Success == true) {
                    $scope.Groups = data.Result;
                } else {
                    $scope.Groups = [];
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }

        });

        facilityinformation.async().then(function (d) { //2. so you can use .then()
            $scope.Facility = d['data']['Result'];
        });

        UserFacility.async().then(function (d) { //2. so you can use .then()
            $scope.UserFacility = d.data;
            //alert($scope.UserFacility);
            $scope.sortconfiguration.FacilityID = $scope.UserFacility;
        });

        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetSortCriteria',
            success: function(data){
                if(data.Success == true) {
                    $scope.SortCriteria = data.Result;
                } else {
                    $scope.SortCriteria = [];
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });

        function LocationChange(text) {
        $scope.sortconfiguration.group = text;
        }

        function selectedLocationChange(item) {
            if(item) {
                if(item.value) {
                    $scope.sortconfiguration.group = item.value;
                    $scope.SelectSortBin();
                } else {
                    $scope.sortconfiguration.group = '';
                }
            } else {
                $scope.sortconfiguration.group = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryLocationSearch   = queryLocationSearch;
        $scope.LocationChange   = LocationChange;
        $scope.selectedLocationChange   = selectedLocationChange;
        function queryLocationSearch (query) {
            if(query) {
                if(query != '' && query != 'undefined') {
                    //return $http.get(host+'administration/includes/admin_submit.php?ajax=GetMatchingLocations&keyword='+query+'&FacilityID='+$scope.cpalletForm.FacilityID)
                    return $http.get(host + 'administration/includes/admin_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&LocationType=WIP')
                    .then(function(res) {
                        if(res.data.Success == true) {
                            if(res.data.Result.length > 0) {
                                var result_array = [];
                                for(var i=0;i<res.data.Result.length;i++) {
                                    result_array.push({value: res.data.Result[i]['GroupName'],GroupName: res.data.Result[i]['GroupName']});
                                }
                                return result_array;
                            } else {
                                return [];
                            }
                        }
                        else {
                            console.log(res.data.Result);
                            return [];
                        }
                    });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }

        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPartTypeList1',
            success: function(data){
                if(data.Success == true) {
                    $scope.PartTypelist = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                console.log(data);
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetCOOList',
            success: function(data){
                if(data.Success == true) {
                    $scope.COOList = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
        url: host+'administration/includes/admin_extended_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetSPECList',
        success: function(data){
            if(data.Success == true) {
                $scope.SPECList = data.Result;
            } else {
                // $mdToast.show (
                //     $mdToast.simple()
                //     .content(data.Result)
                //     .action('OK')
                //     .position('right')
                //     .hideDelay(0)
                //     .toastClass('md-toast-danger md-block')
                // );
            }
            initSessionTime(); $scope.$apply();
        }, error : function (data) {
            $scope.data = data;
            initSessionTime(); $scope.$apply();
        }
        });

        // Show the progress dialog
        var interval = 0;
        $scope.showProgressDialog = function() {
            var totalTime = 10; // Total time in seconds for the progress bar
            var dialogScope = $scope.$new();
            dialogScope.timeRemaining = totalTime;
            dialogScope.progress = 100; // Initial progress value (100%)
            dialogScope.indeterminate = false; // Flag for switching to indeterminate mode

            // Open the dialog
            $mdDialog.show({
                scope: dialogScope,
                templateUrl: 'progressDialog.tmpl.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                escapeToClose: false
            });

            // Start the countdown timer
            interval = $interval(function() {
                if (dialogScope.timeRemaining > 0) {
                    dialogScope.timeRemaining--;
                    dialogScope.progress = (dialogScope.timeRemaining / totalTime) * 100;
                } else {
                    dialogScope.indeterminate = true; // Switch to indeterminate mode
                    $interval.cancel(interval); // Stop the countdown
                }
            }, 1000);

            // Allow cancellation of the dialog
            dialogScope.cancelProgress = function() {
                $interval.cancel(interval);
                $mdDialog.hide();
            };
        };

        // Function to handle search completion
        $scope.searchComplete = function() {
            if (interval) {
                $interval.cancel(interval);
            }
            $mdDialog.hide(); // Close the dialog when search is complete
            //alert("Search completed successfully!");
        };

        $scope.changeMPN = function(){
        if($scope.sortconfiguration.mpn_id && $scope.sortconfiguration.mpn_id != ''){
            $scope.sortconfiguration.Allspec_id = 1;
            $scope.sortconfiguration.part_spec_id = 'All';
        }
        /*else{
            $scope.sortconfiguration.Allspec_id = 1;
            $scope.sortconfiguration.part_spec_id = 'All';
        }*/
        }

        $scope.changeSPEC = function(){
        if($scope.sortconfiguration.part_spec_id && $scope.sortconfiguration.part_spec_id != ''){
            $scope.sortconfiguration.Allmpn_id = 1;
            $scope.sortconfiguration.mpn_id = 'All';
        }
        /*else{
            $scope.sortconfiguration.Allspec_id = 1;
            $scope.sortconfiguration.part_spec_id = 'All';
        }*/
        }

        $scope.CPMPNChanged = function () {
        $scope.material_signup_form.parttypeid.$setValidity('MPNMandate',true);
            if ($scope.sortconfiguration.Allmpn_id) {
                $scope.sortconfiguration.mpn_id = 'All';
            } else {
                $scope.sortconfiguration.mpn_id = '';
            }
        };
        $scope.SpecChanged = function () {
            if ($scope.sortconfiguration.Allspec_id) {
                $scope.sortconfiguration.part_spec_id = 'All';
            } else {
                $scope.sortconfiguration.part_spec_id = '';
            }
        };
        $scope.COOChanged = function () {
            if ($scope.sortconfiguration.Allcoo_id) {
                $scope.sortconfiguration.COO = 'All';
            } else {
                $scope.sortconfiguration.COO = '';
            }
        };
        $scope.CriteriaChange = function(){
        if($scope.sortconfiguration.SortCriteriaID == '1'){
            $scope.sortconfiguration.Allmpn_id = 1;
            $scope.sortconfiguration.Allspec_id = 1;
            $scope.sortconfiguration.Allcoo_id = 1;
            $scope.sortconfiguration.mpn_id = 'All';
            $scope.sortconfiguration.part_spec_id = 'All';
            $scope.sortconfiguration.COO = 'All';
            $scope.sortconfiguration.mpnCheckboxDisabled = true;
            $scope.sortconfiguration.SPECCheckboxDisabled = true;
            $scope.sortconfiguration.COOCheckboxDisabled = true;
        } else if($scope.sortconfiguration.SortCriteriaID == '2'){
            $scope.sortconfiguration.Allmpn_id = 0;
            $scope.sortconfiguration.Allspec_id = 1;
            $scope.sortconfiguration.Allcoo_id = 1;
            $scope.sortconfiguration.mpn_id = '';
            $scope.sortconfiguration.part_spec_id = 'All';
            $scope.sortconfiguration.COO = 'All';
            $scope.sortconfiguration.mpnCheckboxDisabled = false;
            $scope.sortconfiguration.SPECCheckboxDisabled = true;
            $scope.sortconfiguration.COOCheckboxDisabled = true;
        } else if($scope.sortconfiguration.SortCriteriaID == '3'){
            $scope.sortconfiguration.Allmpn_id = 1;
            $scope.sortconfiguration.Allspec_id = 0;
            $scope.sortconfiguration.Allcoo_id = 1;
            $scope.sortconfiguration.mpn_id = 'All';
            $scope.sortconfiguration.part_spec_id = '';
            $scope.sortconfiguration.COO = 'All';
            $scope.sortconfiguration.mpnCheckboxDisabled = true;
            $scope.sortconfiguration.SPECCheckboxDisabled = false;
            $scope.sortconfiguration.COOCheckboxDisabled = true;
        } else if($scope.sortconfiguration.SortCriteriaID == '4'){
            $scope.sortconfiguration.Allmpn_id = 1;
            $scope.sortconfiguration.Allspec_id = 1;
            $scope.sortconfiguration.Allcoo_id = 0;
            $scope.sortconfiguration.mpn_id = 'All';
            $scope.sortconfiguration.part_spec_id = 'All';
            $scope.sortconfiguration.COO = '';
            $scope.sortconfiguration.mpnCheckboxDisabled = true;
            $scope.sortconfiguration.SPECCheckboxDisabled = true;
            $scope.sortconfiguration.COOCheckboxDisabled = false;
        } else if($scope.sortconfiguration.SortCriteriaID == '5'){
            $scope.sortconfiguration.Allmpn_id = 1;
            $scope.sortconfiguration.Allspec_id = 0;
            $scope.sortconfiguration.Allcoo_id = 0;
            $scope.sortconfiguration.mpn_id = 'All';
            $scope.sortconfiguration.part_spec_id = '';
            $scope.sortconfiguration.COO = '';
            $scope.sortconfiguration.mpnCheckboxDisabled = true;
            $scope.sortconfiguration.SPECCheckboxDisabled = false;
            $scope.sortconfiguration.COOCheckboxDisabled = false;
        } else if($scope.sortconfiguration.SortCriteriaID == '6'){
            $scope.sortconfiguration.Allmpn_id = 0;
            $scope.sortconfiguration.Allspec_id = 1;
            $scope.sortconfiguration.Allcoo_id = 0;
            $scope.sortconfiguration.mpn_id = '';
            $scope.sortconfiguration.part_spec_id = 'All';
            $scope.sortconfiguration.COO = '';
            $scope.sortconfiguration.mpnCheckboxDisabled = false;
            $scope.sortconfiguration.SPECCheckboxDisabled = true;
            $scope.sortconfiguration.COOCheckboxDisabled = false;
        }
        }

        $scope.ShowArchiveList = function(){
        if($scope.ArchiveList == '1'){

        }
        }


        /*jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilityStations&Workflow=Parts Sort&workflow_id=20',
            success: function(data){
                if(data.Success == true) {
                    $scope.site = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });*/

        $scope.GetSortDisposition = function () {
            if($scope.sortconfiguration.GroupID) {
                jQuery.ajax({
                    url: host+'administration/includes/admin_extended_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetSortDisposition&GroupID='+$scope.sortconfiguration.GroupID,
                    success: function(data){
                        if(data.Success == true) {
                            //alert('saved');
                            $scope.dispositions = data.Result;
                        }
                        else {
                            $scope.dispositions = {};
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(5000)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    }, error : function (data) {
                        $scope.data = data;
                        $scope.$apply();
                    }
                });
            } else {
                $scope.dispositions = {};
            }
        };

        $scope.GetPartTypeByMPN = function () {
        $scope.material_signup_form.parttypeid.$setValidity('MPNMandate',true);
            if($scope.sortconfiguration.mpn_id && $scope.sortconfiguration.mpn_id != 'All') {
                jQuery.ajax({
                    url: host+'administration/includes/admin_extended_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetPartTypeByMPN&MPN='+$scope.sortconfiguration.mpn_id,
                    success: function(data){
                        if(data.Success == true) {
                            //alert('saved');
                            $scope.sortconfiguration.parttypeid = data.Result.parttypeid;
                            $scope.material_signup_form.parttypeid.$setValidity('MPNValidation',true);
                        }
                        else {
                            $scope.material_signup_form.parttypeid.$setValidity('MPNValidation',false);
                            $scope.sortconfiguration.parttypeid = '';
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(5000)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    }, error : function (data) {
                        $scope.data = data;
                        $scope.$apply();
                    }
                });
            }
        };

        $scope.ValidateParttypeMPN = function () {
        if($scope.sortconfiguration.mpn_id != 'All'){
            if($scope.sortconfiguration.mpn_id && $scope.sortconfiguration.parttypeid) {
            $scope.material_signup_form.parttypeid.$setValidity('MPNValidation',true);
            $scope.material_signup_form.parttypeid.$setValidity('MPNMandate',true);
                jQuery.ajax({
                    url: host+'administration/includes/admin_extended_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ValidateParttypeMPN&MPN='+$scope.sortconfiguration.mpn_id+'&parttypeid='+$scope.sortconfiguration.parttypeid,
                    success: function(data){
                        if(data.Success == true) {
                        }
                        else {
                            $scope.material_signup_form.parttypeid.$setValidity('MPNValidation',false);
                        }
                        $scope.$apply();
                    }, error : function (data) {
                        $scope.data = data;
                        $scope.$apply();
                    }
                });
            }else{
            $scope.material_signup_form.parttypeid.$setValidity('MPNMandate',false);
            }
          }
        };

         $scope.SelectSortBin = function ($data) {
            //if($scope.sortconfiguration.LocationName) {
                jQuery.ajax({
                    url: host+'administration/includes/admin_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=SelectSortBin&group='+$scope.sortconfiguration.group,
                    success: function(data){
                        console.log(data);
                        if(data.Success == true) {
                            //alert('saved');
                            console.log(data.MaximumAssets);
                            $scope.sortconfiguration.MaximumAssets = data.Result.MaximumAssets;
                            $scope.sortconfiguration.BinName = data.Result.BinName;
                            $scope.sortconfiguration.CustomPalletID = data.Result.CustomPalletID;
                        }
                        else {
                          $scope.sortconfiguration.MaximumAssets = '';
                          $scope.sortconfiguration.BinName = '';
                          $scope.sortconfiguration.CustomPalletID = '';
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(5000)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    }, error : function (data) {
                        $scope.data = data;
                        $scope.$apply();
                    }
                });
          //  }
        };

         $scope.SelectSortManualBin = function ($data) {
            //if($scope.sortconfiguration.LocationName) {
                jQuery.ajax({
                    url: host+'administration/includes/admin_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=SelectSortManualBin&BinName='+$scope.sortconfiguration.BinName,
                    success: function(data){
                        console.log(data);
                        if(data.Success == true) {
                            //alert('saved');
                            console.log(data.MaximumAssets);
                            $scope.sortconfiguration.MaximumAssets = data.Result.MaximumAssets;
                            $scope.sortconfiguration.CustomPalletID = data.Result.CustomPalletID;
                        }
                        else {
                          $scope.sortconfiguration.MaximumAssets = '';
                          $scope.sortconfiguration.CustomPalletID = '';
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(5000)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    }, error : function (data) {
                        $scope.data = data;
                        $scope.$apply();
                    }
                });
          //  }
        };
        


        /*jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=SelectSortBin',
            success: function(data){
                if(data.Success == true) {
                    $scope.CustomPallets = data.Result;
                    $scope.GetSortLocation();
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });*/

        $scope.GetSortLocation = function () {
            if($scope.sortconfiguration.CustomPalletID) {
                jQuery.ajax({
                    url: host+'administration/includes/admin_extended_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetSortLocation&CustomPalletID='+$scope.sortconfiguration.CustomPalletID,
                    success: function(data){
                        if(data.Success == true) {
                            //alert('saved');
                            $scope.sortconfiguration.LocationName = data.LocationName;
                            $scope.sortconfiguration.MaximumAssets = data.MaximumAssets;
                            $scope.sortconfiguration.LocationID = data.LocationID;
                        }
                        else {
                        $scope.sortconfiguration.LocationName = '';
                        $scope.sortconfiguration.MaximumAssets = '';
                        $scope.sortconfiguration.LocationID = '';
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(5000)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    }, error : function (data) {
                        $scope.data = data;
                        $scope.$apply();
                    }
                });
            } else {
                $scope.locations = {};
            }
        };


        $scope.SortConfigurationSave = function () {
            $scope.sortconfiguration.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/admin_extended_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SortConfigurationSave&'+$.param($scope.sortconfiguration),
                success: function(data){
                    /*console.log('sssssss');
                    console.log( data.Result);
                    console.log('sssssss');*/
                    $rootScope.$broadcast('preloader:hide');
                    $scope.sortconfiguration.busy = false;
                    if(data.Success) {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        location.reload();
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.sortconfiguration.busy = false;
                    alert('error');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.EditSortConfiguration = function (sortconfiguration) {
            $scope.sortconfiguration = sortconfiguration;
            jQuery.ajax({
                url: host + 'administration/includes/admin_extended_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetsortconfigurationDetails&sortconfigurationid=' + sortconfiguration.sortconfigurationid,
                success: function (data) {
                    if (data.Success == true) {
                        $scope.sortconfiguration = data.Result;
                        $scope.GetSortDisposition();
                        $scope.SelectSortBin();
                        $scope.GetSortLocation();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.sortconfiguration = [];
                    }
                    $scope.$apply();
                }, error: function (data) {
                    $scope.$apply();
                }
            });
        }

        $scope.CancelConfiguration = function () {
        $scope.sortconfiguration = {};
        window.location = "#!/SortConfiguration";
        };

        $scope.busy = false;
        $scope.sortconfigurationList = [];
        $scope.pagedItems = [];
        //Start Pagination Logic
        //$scope.itemsPerPage = 20;

        // Pagination variables for Table 1
        $scope.currentPage1 = 0;
        $scope.itemsPerPage1 = 10;
        //$scope.totalPages1 = Math.ceil($scope.table1Data.length / $scope.itemsPerPage1);

        // Pagination variables for Table 2
        $scope.currentPage2 = 0;
        $scope.itemsPerPage2 = 10;
        //$scope.totalPages2 = Math.ceil($scope.table2Data.length / $scope.itemsPerPage2);

        //$scope.currentPage = 0;
        $scope.OrderBy1 = '';
        $scope.OrderByType1 = '';
        $scope.filter_text1 = [{}];

        $scope.OrderBy2 = '';
        $scope.OrderByType2 = '';
        $scope.filter_text2 = [{}];

        $scope.range = function(table) {
        if(table == 1){
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage1;
            if ( start > $scope.pageCount(table)-rangeSize ) {
                start = $scope.pageCount(table)-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        }
        if(table == 2){
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage2;
            if ( start > $scope.pageCount(table)-rangeSize ) {
                start = $scope.pageCount(table)-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        }
        };
        $scope.prevPage = function(table) {
            /*if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }*/
            //console.log('table = '+table);
            //console.log('currentPage1 = '+$scope.currentPage1);
            if (table == 1 && $scope.currentPage1 > 0) $scope.currentPage1--;
            if (table == 2 && $scope.currentPage2 > 0) $scope.currentPage2--;
            //console.log('currentPage1 = '+$scope.currentPage1);
        };
        $scope.firstPage = function (table) {
            if(table == 1) $scope.currentPage1 = 0;
            if(table == 2) $scope.currentPage2 = 0;
        };
        $scope.prevPageDisabled = function(table) {
        if(table == 1){
            return $scope.currentPage1 === 0 ? "disabled" : "";
        }
        if(table == 2){
            return $scope.currentPage2 === 0 ? "disabled" : "";
        }
        };
        $scope.nextPage = function(table) {
            /*if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }*/
            if (table == 1 && $scope.currentPage1 < $scope.pageCount(table)) $scope.currentPage1++;
            if (table == 2 && $scope.currentPage2 < $scope.pageCount(table)) $scope.currentPage2++;
        };
        $scope.lastPage = function(table) {
        if(table == 1)  $scope.currentPage1 =  $scope.pageCount(table) - 1;
        if(table == 2)  $scope.currentPage2 =  $scope.pageCount(table) - 1;
        };
        $scope.nextPageDisabled = function(table) {
        if(table == 1){
            return $scope.currentPage1 === $scope.pageCount(table) - 1 ? "disabled" : "";
        }
        if(table == 2){
            return $scope.currentPage2 === $scope.pageCount(table) - 1 ? "disabled" : "";
        }
        };
        $scope.pageCount = function(table) {
        if(table == 1){
            return Math.ceil($scope.total1/$scope.itemsPerPage1);
        }
        if(table == 2){
            return Math.ceil($scope.total2/$scope.itemsPerPage2);
        }
        };

        $scope.setPage = function(n,table) {
            if (n >= 0 && n < $scope.pageCount(table)) {
            if (table == 1) $scope.currentPage1 = n;
            if (table == 2) $scope.currentPage2 = n;
                //$scope.currentPage = n;
            }
        };
        $scope.CallServerFunction1 = function (newValue) {
            //if($scope.CurrentStatus != '' )  {
            //console.log('in call server2 = '+newValue);
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/admin_extended_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetsortconfigurationList&limit='+$scope.itemsPerPage1+'&skip='+$scope.currentPage1*$scope.itemsPerPage1+'&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=1'+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems1 = data.Result;
                            if(data.total) {
                                $scope.total1 = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            //}
        };

        $scope.CallServerFunction2 = function (newValue) {
            //if($scope.CurrentStatus != '' )  {
            //console.log('in call server2 = '+newValue);
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'administration/includes/admin_extended_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetsortconfigurationList&limit='+$scope.itemsPerPage1+'&skip='+$scope.currentPage1*$scope.itemsPerPage1+'&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=0'+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems2 = data.Result;
                            if(data.total) {
                                $scope.total2 = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            //}
        };

        $scope.$watch("currentPage1", function(newValue, oldValue) {
            //console.log('newvalue, oldValue = '+ newValue +','+ oldValue);
            $scope.CallServerFunction1(newValue);
        });
        $scope.$watch("currentPage2", function(newValue, oldValue) {
            $scope.CallServerFunction2(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy1 = function (orderby) {
            $scope.OrderBy1 = orderby;
            if($scope.OrderByType1 == 'asc') {
                $scope.OrderByType1 = 'desc';
            } else {
                $scope.OrderByType1 = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'administration/includes/admin_extended_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetsortconfigurationList&limit='+$scope.itemsPerPage1+'&skip='+$scope.currentPage1*$scope.itemsPerPage1+'&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=1'+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems1 = data.Result;
                        if(data.total) {
                            $scope.total1 = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.MakeOrderBy2 = function (orderby) {
            $scope.OrderBy2 = orderby;
            if($scope.OrderByType2 == 'asc') {
                $scope.OrderByType2 = 'desc';
            } else {
                $scope.OrderByType2 = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'administration/includes/admin_extended_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetsortconfigurationList&limit='+$scope.itemsPerPage1+'&skip='+$scope.currentPage1*$scope.itemsPerPage1+'&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=0'+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems2 = data.Result;
                        if(data.total) {
                            $scope.total2 = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.MakeFilter1 = function () {
            if($scope.currentPage1 == 0) {
                $scope.CallServerFunction1($scope.currentPage1);
            } else {
                $scope.currentPage1 = 0;
            }
        };

        $scope.MakeFilter2 = function () {
            if($scope.currentPage2 == 0) {
                $scope.CallServerFunction2($scope.currentPage2);
            } else {
                $scope.currentPage2 = 0;
            }
        };

        $scope.binSearchTerm = '';
        $scope.clearBinSearchTerm = function () {
            $scope.binSearchTerm = '';
        };

        $scope.deselectBin = function () {
            $scope.binSearchTerm = ''; // Clear the search text
            $scope.sortconfiguration.CustomPalletID = ''; // deselect the selected items
        };

        $scope.sortconfigurationListxls1 = function () {
            $scope.showProgressDialog();
            jQuery.ajax({
                url: host+'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GeneratesortconfigurationListxls&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=1'+'&'+$.param($scope.convertSingle($scope.filter_text1)),

                success: function(data) {
                    if(data.Success) {
                        $scope.searchComplete();
                        window.location="templates/sortconfigurationListxls.php";
                        /*$http.post(host+'administration/templates/sortconfigurationListxls.php').then(function(response) {
                        // On success, download the file
                        var blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                        var downloadLink = angular.element('<a></a>');
                        downloadLink.attr('href', window.URL.createObjectURL(blob));
                        downloadLink.attr('download', 'SortConfigurationActiveList.xlsx');
                        downloadLink[0].click();

                        // Close the modal
                        //document.getElementById('progressModal').style.display = 'none';
                        $scope.searchComplete();
                        }, function(error) {
                            console.error('Oops, Something went wrong, please try again', error);
                            // Handle error
                        });*/
                    } else {
                    // alert("4");
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.sortconfigurationListxls2 = function () {
            $scope.showProgressDialog();
            jQuery.ajax({
                url: host+'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GeneratesortconfigurationListxls&OrderBy='+$scope.OrderBy2+'&OrderByType='+$scope.OrderByType2+'&Status=0'+'&'+$.param($scope.convertSingle($scope.filter_text2)),

                success: function(data) {
                    if(data.Success) {
                    $scope.searchComplete();
                    window.location="templates/sortconfigurationListxls.php";
                        /*$http.post(host+'administration/templates/sortconfigurationListxls.php').then(function(response) {
                        // On success, download the file
                        var blob = new Blob([response.data], { type: 'text/csv' });
                        var downloadLink = angular.element('<a></a>');
                        downloadLink.attr('href', window.URL.createObjectURL(blob));
                        downloadLink.attr('download', 'SortConfigurationArchiveList.csv');
                        downloadLink[0].click();

                        // Close the modal
                        //document.getElementById('progressModal').style.display = 'none';
                        $scope.searchComplete();
                        }, function(error) {
                            console.error('Oops, Something went wrong, please try again', error);
                            // Handle error
                        });*/
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.Deletesortconfiguration = function(product, ev) {
                var con = confirm('Are you sure ?');
                if(con) {
                    jQuery.ajax({
                        url: host+'administration/includes/admin_extended_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=Deletesortconfiguration&sortconfigurationid=' + product.sortconfigurationid,
                        success: function(data){
                            if(data.Success) {
                                //alert("Deleted Successfully");
                                $mdToast.show (
                                    $mdToast.simple()
                                    .content("Deleted Successfully")
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(5000)
                                    .toastClass('md-toast-success md-block')
                                );
                                location.reload();
                            } else {
                                $scope.deleted = data.Result;
                                //alert(data.Result);
                                $mdToast.show (
                                    $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(5000)
                                    .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.$apply();
                        }, error : function (data) {
                            $scope.data = data;
                            $scope.$apply();
                        }
                    });
                }
            };

            $scope.Archivesortconfiguration = function(product,ev){
            $scope.updatesortconfigurationstatus(product,0);
            $scope.setPage(1);
            }

            $scope.ChangeSortconfigurationStatus = function(product){
            $scope.updatesortconfigurationstatus(product,product.Status);
            $scope.setPage(2);
            }

            $scope.updatesortconfigurationstatus = function(product, status) {
                var con = confirm('Are you sure ?');
                if(con) {
                    jQuery.ajax({
                        url: host+'administration/includes/admin_extended_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=updatesortconfigurationstatus&sortconfigurationid=' + product.sortconfigurationid + '&status=' + status,
                        success: function(data){
                            if(data.Success) {
                                //alert("Deleted Successfully");
                                $mdToast.show (
                                    $mdToast.simple()
                                    .content("Deleted Successfully")
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(5000)
                                    .toastClass('md-toast-success md-block')
                                );
                                location.reload();
                            } else {
                                $scope.deleted = data.Result;
                                //alert(data.Result);
                                $mdToast.show (
                                    $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(5000)
                                    .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.$apply();
                        }, error : function (data) {
                            $scope.data = data;
                            $scope.$apply();
                        }
                    });
                }
            };

        $scope.onFileSelect = function($files) {
            if($("#SortConfigurationFile").val() != '') {
                //$files: an array of files selected, each file has name, size, and type.
                $rootScope.$broadcast('preloader:active');
                $scope.busy = true;
                for (var i = 0; i < $files.length; i++) {
                    var file = $files[i];
                    $scope.upload = $upload.upload({
                    url: host+'administration/includes/admin_extended_submit.php',
                    data: {ajax: 'UploadSortConfigurationFile'},
                    file: file, // or list of files ($files) for html5 only
                    }).success(function(data, status, headers, config) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    if(data.Success) {
                        $("#SortConfigurationFile").val('');
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $scope.CallServerFunction1(0);
                        $scope.CallServerFunction2(0);
                         window.location = "templates/UploadDataExport.php";
                        //location.reload();
                    } else {
                        $("#SortConfigurationFile").val('');
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                    // file is uploaded successfully
                    }).error(function(data, status, headers, config) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data);
                    });
                }
            }
        };
    });
})(); 