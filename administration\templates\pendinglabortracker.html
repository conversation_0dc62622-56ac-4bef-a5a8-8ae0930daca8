<div class="page" data-ng-controller="pendinglabortracker">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">
                <!--List Start-->
                <md-card class="no-margin-h pt-0">

                    <md-toolbar class="md-table-toolbar md-default" ng-init="TrackedLaborDetailsPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;">

                            <i ng-click="TrackedLaborDetailsPanel = !TrackedLaborDetailsPanel"
                                class="material-icons md-primary"
                                ng-show="TrackedLaborDetailsPanel">keyboard_arrow_up</i>
                            <i ng-click="TrackedLaborDetailsPanel = !TrackedLaborDetailsPanel"
                                class="material-icons md-primary"
                                ng-show="! TrackedLaborDetailsPanel">keyboard_arrow_down</i>
                            <span ng-click="TrackedLaborDetailsPanel = !TrackedLaborDetailsPanel">Pending Labor Tracker
                                Report</span>
                            <div flex></div>
                        </div>
                    </md-toolbar>

                    <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                        <p>No Tracking Labor Details</p>
                    </div>
                    <form name="pendingLaborTrackerForm">
                        <div class="row" ng-show="TrackedLaborDetailsPanel">
                            <div class="col-md-12">
                                <!--  <div class="col-md-3 col-lg-2" style="min-width:270px;">
                                  <md-input-container class="md-block">
                                      <label>Shift Date</label>
                                      <md-datepicker
                                        name = "dateField"
                                        ng-model = "ShiftDate"
                                        md-placeholder = "Enter date"
                                        required
                                        md-max-date = "today" ng-change="CallServerFunction(0)"></md-datepicker>

                                     <div class = "validation-messages" ng-messages = "material_signup_form.dateField.$error">
                                        <div ng-message = "valid">The entered value is not a date!</div>
                                        <div ng-message = "required">This date is required!</div>
                                        <div ng-message = "mindate">Date is too early!</div>
                                        <div ng-message = "maxdate">Date is too late!</div>
                                     </div>
                                  </md-input-container>
                              </div> -->
                                <div class="col-md-4 col-sm-6 col-md-offset-8 col-sm-offset-6">
                                    <md-input-container class="md-block">
                                        <label>Override Reason</label>
                                        <md-select name="OverrideReason" ng-model="OverrideReason" required
                                            aria-label="select">
                                            <md-option value="OOTO"> OOTO </md-option>
                                            <md-option value="Offsite Event"> Offsite Event </md-option>
                                            <md-option value="Onboarding"> Onboarding </md-option>
                                            <md-option value="Extended Training"> Extended Training </md-option>
                                            <md-option value="Holiday"> Holiday </md-option>
                                            <md-option value="System Admin"> System Admin </md-option>
                                        </md-select>
                                        <div ng-messages="pendingLaborTrackerForm.OverrideReason.$error" multiple
                                            ng-if='pendingLaborTrackerForm.OverrideReason.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </md-input-container>
                                </div>
                            </div>
                        </div>

                        <div class="row" ng-show="TrackedLaborDetailsPanel">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                        <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage *
                                                itemsPerPage) + 1}}</span>
                                            to <span style="font-weight:bold;"
                                                ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage
                                                * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;"
                                                ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                            of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-responsive" style="overflow: auto;">


                                        

                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;"></th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('EventDate')"
                                                        ng-class="{'orderby' : OrderBy == 'EventDate'}">
                                                        <div style="min-width: 120px;">
                                                            Date
                                                            <i class="fa fa-sort pull-right"
                                                                ng-show="OrderBy != 'EventDate'"></i>
                                                            <span ng-show="OrderBy == 'EventDate'">
                                                                <i class="fa fa-sort-asc pull-right"
                                                                    ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right"
                                                                    ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')"
                                                        ng-class="{'orderby' : OrderBy == 'FacilityName'}">
                                                        <div style="min-width: 100px;">
                                                            Facility<i class="fa fa-sort pull-right"
                                                                ng-show="OrderBy != 'FacilityName'"></i>
                                                            <span ng-show="OrderBy == 'FacilityName'">
                                                                <i class="fa fa-sort-asc pull-right"
                                                                    ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right"
                                                                    ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FirstName')"
                                                        ng-class="{'orderby' : OrderBy == 'FirstName'}">
                                                        <div style="min-width: 80px;">
                                                            First Name <i class="fa fa-sort pull-right"
                                                                ng-show="OrderBy != 'FirstName'"></i>
                                                            <span ng-show="OrderBy == 'FirstName'">
                                                                <i class="fa fa-sort-asc pull-right"
                                                                    ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right"
                                                                    ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LastName')"
                                                        ng-class="{'orderby' : OrderBy == 'LastName'}">
                                                        <div style="min-width: 80px;">
                                                            Last Name <i class="fa fa-sort pull-right"
                                                                ng-show="OrderBy != 'LastName'"></i>
                                                            <span ng-show="OrderBy == 'LastName'">
                                                                <i class="fa fa-sort-asc pull-right"
                                                                    ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right"
                                                                    ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('UserName')"
                                                        ng-class="{'orderby' : OrderBy == 'UserName'}">
                                                        <div style="min-width: 80px;">
                                                            User Name <i class="fa fa-sort pull-right"
                                                                ng-show="OrderBy != 'UserName'"></i>
                                                            <span ng-show="OrderBy == 'UserName'">
                                                                <i class="fa fa-sort-asc pull-right"
                                                                    ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right"
                                                                    ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ProfileName')"
                                                        ng-class="{'orderby' : OrderBy == 'ProfileName'}">
                                                        <div style="min-width: 80px;">
                                                            Profile <i class="fa fa-sort pull-right"
                                                                ng-show="OrderBy != 'ProfileName'"></i>
                                                            <span ng-show="OrderBy == 'ProfileName'">
                                                                <i class="fa fa-sort-asc pull-right"
                                                                    ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right"
                                                                    ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>
                                                </tr>

                                                <tr class="errornone">
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <!--<td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="EventDate" ng-model="filter_text[0].EventDate" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>-->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName"
                                                                ng-model="filter_text[0].FacilityName"
                                                                ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FirstName"
                                                                ng-model="filter_text[0].FirstName"
                                                                ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LastName"
                                                                ng-model="filter_text[0].LastName"
                                                                ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="UserName"
                                                                ng-model="filter_text[0].UserName"
                                                                ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ProfileName"
                                                                ng-model="filter_text[0].ProfileName"
                                                                ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>

                                            <tbody>
                                                <tr ng-repeat="product in pagedItems">
                                                    <td>
                                                        <md-input-container class="md-block">
                                                            <md-checkbox ng-model="product.checked"
                                                                aria-label="AcceptAllDisposition" 
                                                                ng-change="addUser(product,product.checked)"></md-checkbox>
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        {{product.EventDate}}
                                                    </td>
                                                    <td>
                                                        {{product.FacilityName}}
                                                    </td>
                                                    <td>
                                                        {{product.FirstName}}
                                                    </td>
                                                    <td>
                                                        {{product.LastName}}
                                                    </td>
                                                    <td>
                                                        {{product.UserName}}
                                                    </td>
                                                    <td>
                                                        {{product.ProfileName}}
                                                    </td>

                                                </tr>
                                            </tbody>

                                            <tfoot>
                                                <tr>
                                                    <td colspan="7">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()">
                                                                        << First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()">
                                                                        << Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()"
                                                                    ng-class="{active: n == currentPage}"
                                                                    ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" ng-show="TrackedLaborDetailsPanel">
                            <div class="col-md-12 btns-row">
                                <md-button class="md-raised btn-w-md md-default btn-w-md"
                                    ng-click="CancelPendingLaborTrackingList()">Cancel</md-button>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    ng-click="SubmitPendingLaborTracking()"
                                    data-ng-disabled="pendingLaborTrackerForm.$invalid || submitted.length==0">
                                    <span ng-show="! labortracking.busy">Submit</span>
                                    <span ng-show="labortracking.busy"><md-progress-circular class="md-hue-2"
                                            md-mode="indeterminate" md-diameter="20px"
                                            style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>
                        </div>
                    </form>
                </md-card>

                <!--List Close-->
            </article>
        </div>
    </div>
</div>