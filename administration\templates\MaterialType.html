
<div class="row page" data-ng-controller="MaterialType">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Material Type</span>
                        <div flex></div>
                            <a href="#!/MaterialTypeList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a> 
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="MaterialType_Form" class="form-validation" data-ng-submit="submitForm()">                            
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Material Type Name</label>
                                    <input type="text" name="MaterialType"  ng-model="MaterialType['MaterialType']"  required ng-maxlength="100" />
                                    <div class="error-space"> 
                                        <div ng-messages="MaterialType_Form.MaterialType.$error" multiple ng-if='MaterialType_Form.MaterialType.$dirty'>
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="MaterialTypeDescription"  ng-model="MaterialType['MaterialTypeDescription']"  required ng-maxlength="250" />
                                    <div class="error-space"> 
                                        <div ng-messages="MaterialType_Form.MaterialTypeDescription.$error" multiple ng-if='MaterialType_Form.MaterialTypeDescription.$dirty'>
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 250.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">       
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="MaterialType.Status" required aria-label="select">
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="Inactive"> Inactive </md-option>
                                    </md-select>   
                                    <div class="error-space"> 
                                        <div ng-messages="MaterialType_Form.Status.$error" multiple ng-if='MaterialType_Form.Status.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>    
                                    </div>                                         
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="MaterialType.SpeedMaterialType" aria-label="Speed Material Type" ng-true-value="'1'" ng-false-value="'0'"> Speed Material Type</md-switch>
                            </div>

                            <div class="col-md-3">
                                <md-switch class="mt-10" ng-model="MaterialType.BulkRecoveryEditEligable" aria-label="Bulk Recovery Edit Eligible" ng-true-value="'1'" ng-false-value="'0'"> Bulk Recovery Edit Eligible</md-switch>
                            </div>

                            <div class="col-md-12 btns-row">
                                <a href="#!/MaterialTypeList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="MaterialType_Form.$invalid || MaterialType.busy" ng-click="SaveMaterialType()">
                                <span ng-show="! MaterialType.busy">Save</span>
                                <span ng-show="MaterialType.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>