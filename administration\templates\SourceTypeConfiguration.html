<div class="row page" data-ng-controller="SourceTypeConfiguration">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Source Type Configuration</span>
                        <div flex></div>
                           <a href="#!/SourceTypeConfigurationList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                                <div class="col-md-3">
                                    <md-input-container class="md-block">                                            
                                        <label>Facility</label>
                                        <md-select name="FacilityID" ng-model="configuration.FacilityID" required aria-label="select">
                                            <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facility"> {{fac.FacilityName}} </md-option>                                            
                                        </md-select>  
                                        <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                        </div>                                            
                                    </md-input-container>
                                </div>

                                <div class="col-md-3" >
                                    <md-input-container class="md-block">
                                        <label>Customer ID</label>   
                                        <md-select name="AWSCustomerID" ng-model="configuration.AWSCustomerID" required aria-label="select" >
                                            <md-option ng-repeat="cus in AwsCustomers" value="{{cus.AWSCustomerID}}"> {{cus.Customer}} </md-option>
                                        </md-select>
                                        <div class="error-space"> 
                                            <div ng-messages="material_signup_form.AWSCustomerID.$error" multiple ng-if='material_signup_form.AWSCustomerID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>  
                                    </md-input-container>
                                </div>

                                <div class="col-md-3" >
                                    <md-input-container class="md-block">
                                        <label>Material Type</label>   
                                        <md-select name="MaterialTypeID" ng-model="configuration.MaterialTypeID" required aria-label="select" >
                                            <md-option ng-repeat="cus in MaterialTypes" value="{{cus.MaterialTypeID}}"> {{cus.MaterialType}} </md-option>
                                        </md-select>
                                        <div class="error-space"> 
                                            <div ng-messages="material_signup_form.MaterialTypeID.$error" multiple ng-if='material_signup_form.MaterialTypeID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>  
                                    </md-input-container>
                                </div>

                                <div class="col-md-3" >
                                    <md-input-container class="md-block">
                                        <label>Source Type</label>   
                                        <md-select name="idCustomertype" ng-model="configuration.idCustomertype" required aria-label="select" >
                                            <md-option ng-repeat="cus in CustomerTypes" value="{{cus.idCustomertype}}"> {{cus.Cumstomertype}} </md-option>
                                        </md-select>
                                        <div class="error-space"> 
                                            <div ng-messages="material_signup_form.idCustomertype.$error" multiple ng-if='material_signup_form.idCustomertype.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>  
                                    </md-input-container>
                                </div>                                
                                                    
                                <div class="col-md-3">
                                    <md-input-container class="md-block">                                            
                                        <label>Status</label>
                                        <md-select name="Status" ng-model="configuration.Status" required aria-label="select">
                                            <md-option value="Active"> Active </md-option>
                                            <md-option value="Inactive"> Inactive </md-option>
                                        </md-select>  
                                         <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                        </div>                                            
                                    </md-input-container>
                                </div>                                

                                <div class="col-md-12 btns-row">
                                    <a href="#!/SourceTypeConfigurationList" style="text-decoration: none;">
                                        <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                    </a>
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="material_signup_form.$invalid" ng-click="ManageSourceTypeConfiguration()">
                                    <span ng-show="! configuration.busy">Save</span>
                                    <span ng-show="configuration.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                                </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>