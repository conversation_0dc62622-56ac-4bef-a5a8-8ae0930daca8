<?php
	session_start();
	include_once("../database/bin_to_container_consolidation.class.php");
	$obj = new BinContainerConsolidationClass();
	
	if($_POST['ajax'] == "GetCustomPalletDetails") {
		$result = $obj->GetCustomPalletDetails($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "GetShipmentContainerDetails") {
		$result = $obj->GetShipmentContainerDetails($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "ConsolidateBINToContainer") {
		$result = $obj->ConsolidateBINToContainer($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "GetPalletDetails") {
		$result = $obj->GetPalletDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ConsolidateContainer") {
		$result = $obj->ConsolidateContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddSerialToContainer") {
		$result = $obj->AddSerialToContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCustomPalletDetailsForServerSwitch") {
		$result = $obj->GetCustomPalletDetailsForServerSwitch($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddServerSwitchToBin") {
		$result = $obj->AddServerSwitchToBin($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ConsolidateServerSwitchBIN") {
		$result = $obj->ConsolidateServerSwitchBIN($_POST);
		echo $result;
	}



	if($_POST['ajax'] == "GetCustomPalletDetailsForMedia") {
		$result = $obj->GetCustomPalletDetailsForMedia($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddMediaToBin") {
		$result = $obj->AddMediaToBin($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ConsolidateMediaBIN") {
		$result = $obj->ConsolidateMediaBIN($_POST);
		echo $result;
	}
?>