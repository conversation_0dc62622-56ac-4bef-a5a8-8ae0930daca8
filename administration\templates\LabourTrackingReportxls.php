<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['LabourTrackingReportxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "LabourTrackingReport.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Labour Tracking Report');
$header = array('Shift Date From','Shift Data To','Workflow','Duration Per Day','Program','Manager','Tracked By');

//$sql = "select L.*,F.FacilityName from location L, facility F where F.FacilityID = L.FacilityID  AND F.AccountID = '".$_SESSION['user']['AccountID']."'";
$sql = "select l.*,wl.workflowlabortrackingname,u.UserName,p.ProgramName,ptm.UserName as MangerName,u.OtherManager,u.Manager 
			from workflow_labor_tracking wl,labor_tracking l,users u
			left join Program p on u.Program = p.ProgramID
			left join users ptm on u.Manager = ptm.UserId
			where wl.workflow_labor_tracking_id = l.workflow_id AND u.UserId = l.user_id";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if ($value != '') {
                    if ($key == 'event_date') {
                        $value = date('Y-m-d', strtotime($value));
                        $query = $query . " AND l.event_date like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'event_dateto') {
                        $value = date('Y-m-d', strtotime($value));
                        $query = $query . " AND l.event_dateto like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'workflowlabortrackingname') {
                        $query = $query . " AND wl.workflowlabortrackingname like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'UserName') {
                        $query = $query . " AND u.UserName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'MangerName') {
                        $query = $query . " AND (u.OtherManager like '" . mysqli_real_escape_string($connectionlink, $value) . "%' OR ptm.UserName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%')  ";
                    }
                    if ($key == 'ProgramName') {
                        $query = $query . " AND p.ProgramName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }
            if ($data['OrderBy'] == 'event_date') {
                $query = $query . " order by l.event_date " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'event_dateto') {
                $query = $query . " order by l.event_dateto " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'workflowlabortrackingname') {
                $query = $query . " order by wl.workflowlabortrackingname " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'UserName') {
                $query = $query . " order by u.UserName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'ProgramName') {
                $query = $query . " order by p.ProgramName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'MangerName') {
                $query = $query . " order by ptm.UserName " . $order_by_type . " ";
            }
        } else {
            $sql = $sql . " order by l.event_date desc ";
        }
$query = mysqli_query($connectionlink,$sql);
while($row = mysqli_fetch_assoc($query))
{
    $totalHours = $totalHours + $row['event_duration'];
    $row['event_date'] = date('M d, Y', strtotime($row['event_date']));
    $row['event_dateto'] = date('M d, Y', strtotime($row['event_dateto']));
    if($row['Manager'] == '0')
    {
        $row['MangerName'] = $row['OtherManager'];
    }
    $row2  = array($row['event_date'],$row['event_dateto'],$row['workflowlabortrackingname'],$row['event_duration'],$row['ProgramName'],$row['MangerName'],$row['UserName']);
    $rows[] = $row2;
}
$sheet_name = 'Source List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 