<?php
	session_start();
	include_once("../database/failure_analysis.class.php");
	$obj = new FailureAnalysisClass();
	
	if($_POST['ajax'] == "GetCustomPalletDetails") {
		$result = $obj->GetCustomPalletDetails($_POST);
		echo $result;
	}
	
	if($_POST['ajax'] == "GetMPNFromSerialFA") {
		$result = $obj->GetMPNFromSerialFA($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateAssetFailureAnalysis") {
		$result = $obj->UpdateAssetFailureAnalysis($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetFAAssets") {
		$result = $obj->GetFAAssets($_POST);
		echo $result;
	}
    if($_POST['ajax'] == "GetStationRigs") {
		$result = $obj->GetStationRigs($_POST);
		echo $result;
	}

	// Actions Functions for Bin Management
	if($_POST['ajax'] == "CreateBin") {
		$result = $obj->CreateBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CloseBin") {
		$result = $obj->CloseBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "NestToBin") {
		$result = $obj->NestToBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAvailableParentBins") {
		$result = $obj->GetAvailableParentBins($_POST);
		echo $result;
	}
?>