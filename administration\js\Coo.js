(function () {
    'use strict';
    angular.module('app').controller("COO", function ($scope, $location, $http, $rootScope, $mdToast, $stateParams, facilityinformation, UserFacility) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=COO',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.COO = {};
        $scope.Facility = {};
        facilityinformation.async().then(function (d) { //2. so you can use .then()
            $scope.Facility = d['data']['Result'];
        });

        UserFacility.async().then(function (d) { //2. so you can use .then()
            $scope.UserFacility = d.data;
            //alert($scope.UserFacility);
            $scope.COO.FacilityID = $scope.UserFacility;
        });
        $scope.COOSave = function () {
            $scope.COO.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'administration/includes/Coo_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=COO&' + $.param($scope.COO),
                success: function (data) {
                    /*console.log('sssssss');
                    console.log( data.Result);
                    console.log('sssssss');*/
                    $rootScope.$broadcast('preloader:hide');
                    $scope.COO.busy = false;
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        window.location = "#!/COOList";
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );

                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.COO.busy = false;
                    alert('error');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        if ($stateParams.COOID) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'administration/includes/Coo_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCOODetails&COOID=' + $stateParams.COOID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    if (data.Success) {
                        $scope.COO = data.Result;
                    } else {

                        $scope.COO = {};
                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }
    });

    angular.module('app').controller("COOList", function ($scope,$location,$http,$rootScope,$mdToast) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=COO',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

           $scope.busy = false;
           $scope.COOList = [];
           $scope.pagedItems = [];

           //Start Pagination Logic
           $scope.itemsPerPage = 20;
           $scope.currentPage = 0;
           $scope.OrderBy = '';
           $scope.OrderByType = '';
           $scope.filter_text = [{}];
           $scope.range = function() {
               var rangeSize = 10;
               var ret = [];
               var start;
               start = $scope.currentPage;
               if ( start > $scope.pageCount()-rangeSize ) {
                   start = $scope.pageCount()-rangeSize;
               }
               for (var i=start; i<start+rangeSize; i++) {
                   ret.push(i);
               }
               return ret;
           };
           $scope.prevPage = function() {
               if ($scope.currentPage > 0) {
                   $scope.currentPage--;
               }
           };
           $scope.firstPage = function () {
               $scope.currentPage = 0;
           };
           $scope.prevPageDisabled = function() {
               return $scope.currentPage === 0 ? "disabled" : "";
           };
           $scope.nextPage = function() {
               if ($scope.currentPage < $scope.pageCount() - 1) {
                   $scope.currentPage++;
               }
           };
           $scope.lastPage = function() {
               $scope.currentPage =  $scope.pageCount() - 1;
           };
           $scope.nextPageDisabled = function() {
               return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
           };
           $scope.pageCount = function() {
               return Math.ceil($scope.total/$scope.itemsPerPage);
           };
           $scope.setPage = function(n) {
               if (n >= 0 && n < $scope.pageCount()) {
                   $scope.currentPage = n;
               }
           };
           $scope.CallServerFunction = function (newValue) {
               if($scope.CurrentStatus != '' )  {
                   $scope.busy = true;
                   $rootScope.$broadcast('preloader:active');
                   jQuery.ajax({
                       url: host+'administration/includes/Coo_submit.php',
                       dataType: 'json',
                       type: 'post',
                       data: 'ajax=GetCOOList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                       success: function(data) {
                           $scope.busy = false;
                           $rootScope.$broadcast('preloader:hide');
                           if(data.Success) {
                               $scope.pagedItems = data.Result;
                               if(data.total) {
                                   $scope.total = data.total;
                               }
                           } else {
                               $mdToast.show(
                                   $mdToast.simple()
                                       .content(data.Result)
                                       .position('right')
                                       .hideDelay(3000)
                               );

                               var op = data.Result.split(' ');                            
                                if( op[0] == "No" && op[1] == 'Access') {
                                    window.location = host;
                                }
                           }
                           initSessionTime(); $scope.$apply();
                       }, error : function (data) {
                           $scope.busy = false;
                           $rootScope.$broadcast('preloader:hide');
                           alert(data.Result);
                           $scope.error = data;
                           initSessionTime(); $scope.$apply();
                       }
                   });
               }
           };
           $scope.$watch("currentPage", function(newValue, oldValue) {
               $scope.CallServerFunction(newValue);
           });
           $scope.convertSingle = function (multiarray) {
               var result = {};
               for(var i=0;i<multiarray.length;i++) {
                   result[i] = multiarray[i];
               }
               //alert(result);
               return result;
           };
           $scope.MakeOrderBy = function (orderby) {
               $scope.OrderBy = orderby;
               if($scope.OrderByType == 'asc') {
                   $scope.OrderByType = 'desc';
               } else {
                   $scope.OrderByType = 'asc';
               }
               $scope.busy = true;
               $rootScope.$broadcast('preloader:active');

               jQuery.ajax({
                   url: host+'administration/includes/Coo_submit.php',
                   dataType: 'json',
                   type: 'post',
                   data: 'ajax=GetCOOList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                   success: function(data) {
                       $scope.busy = false;
                       $rootScope.$broadcast('preloader:hide');
                       if(data.Success) {
                           $scope.pagedItems = data.Result;
                           if(data.total) {
                               $scope.total = data.total;
                           }
                       } else {
                           $mdToast.show(
                               $mdToast.simple()
                                   .content(data.Result)
                                   .position('right')
                                   .hideDelay(3000)
                           );

                           var op = data.Result.split(' ');                            
                            if( op[0] == "No" && op[1] == 'Access') {
                                window.location = host;
                            }
                       }
                       initSessionTime(); $scope.$apply();
                   }, error : function (data) {
                       $scope.busy = false;
                       $rootScope.$broadcast('preloader:hide');
                       $scope.error = data;
                       initSessionTime(); $scope.$apply();
                   }
               });
           };
           $scope.MakeFilter = function () {
               if($scope.currentPage == 0) {
                   $scope.CallServerFunction($scope.currentPage);
               } else {
                   $scope.currentPage = 0;
               }
           };
            $scope.COOListxls = function () {
           //alert("1");
           jQuery.ajax({
               url: host+'administration/includes/Coo_submit.php',
               dataType: 'json',
               type: 'post',
               data: 'ajax=GenerateCOOListxls&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),

               success: function(data) {
                   if(data.Success) {
                       //alert("2");
                       //console.log(data.Result);
                       window.location="templates/COOListxls.php";
                   } else {
                      // alert("4");
                       $mdToast.show (
                           $mdToast.simple()
                           .content(data.Result)
                           .action('OK')
                           .position('right')
                           .hideDelay(0)
                           .toastClass('md-toast-danger md-block')
                       );

                       var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                   }
                   
                   initSessionTime(); $scope.$apply();
               }, error : function (data) {
                   //alert(data.Result);
                   //alert("3");
                   $scope.error = data;
                   initSessionTime(); $scope.$apply();
               }
           });
       };
           //End Pagination Logic
       });
})();
