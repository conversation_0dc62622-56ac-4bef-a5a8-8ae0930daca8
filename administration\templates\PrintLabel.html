
<div class="row page" data-ng-controller="print_label">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Print Label</span>
                        <div flex></div>                        
                    </div>
                </md-toolbar>
                                
                <div class="col-md-12">                    
                    <form name="material_signup_form" class="form-validation">
                        <fieldset>
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[0]"  required ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[1]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[2]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[3]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[4]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[5]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[6]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[7]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[8]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Value</label>
                                    <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                    <input type="text" name="value"  ng-model="value[9]" ng-maxlength="250"  />
                                    <div ng-messages="material_signup_form.value.$error" multiple ng-if='material_signup_form.value.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-12 btns-row">                               
                                <md-button 
                                class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || busy" ng-click="PrintLabel()">
                                <span ng-show="! busy">Print</span>
                                <span ng-show="busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                            
                        </fieldset>
                    </form>


                </div>

            </md-card>
        </article>        
    </div>

</div>