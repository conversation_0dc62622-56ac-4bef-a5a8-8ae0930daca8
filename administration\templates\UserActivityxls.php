<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['UseActivityxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "UserActivityList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('User Activity List');
$header = array('User','Profile','Transaction Type','Facility Name','Description','IP Address','Time');

$sql = "select t.*,u.FirstName,u.LastName,F.FacilityName,UPF.ProfileName from user_transactions t 
left join users u on t.UserId = u.UserId 
LEFT JOIN facility F ON F.FacilityID = u.FacilityID
LEFT JOIN profile_type UPF ON UPF.ProfileID = u.ProfileID
where  1 ";
if($data[0] && count($data[0]) > 0) {
    foreach ($data[0] as $key => $value) {
        if($value != '') {

            if($key == 'TransactionTime') {
                $sql = $sql . " AND t.TransactionTime like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'ProfileName') {
                $sql = $sql . " AND UPF.ProfileName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'TransactionIPAddress') {
                $sql = $sql . " AND t.TransactionIPAddress like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'TransactionType') {
                $sql = $sql . " AND t.TransactionType like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'Description') {
                $sql = $sql . " AND t.Description like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }
            if($key == 'FirstName') {
                $sql = $sql . " AND (u.FirstName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' OR u.LastName like '%".mysqli_real_escape_string($connectionlink1,$value)."%') ";
            }
            if($key == 'FacilityName') {
                $sql = $sql . " AND F.FacilityName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
            }									
        }
    }
}

if($data['OrderBy'] != '') {
    if($data['OrderByType'] == 'asc') {
        $order_by_type = 'asc';
    } else {
        $order_by_type = 'desc';
    }

    if($data['OrderBy'] == 'TransactionTime') {
        $sql = $sql . " order by t.TransactionTime ".$order_by_type." ";
    }
    else if($data['OrderBy'] == 'ProfileName') {
        $sql = $sql . " order by UPF.ProfileName ".$order_by_type." ";
    }  
    else if($data['OrderBy'] == 'TransactionIPAddress') {
        $sql = $sql . " order by t.TransactionIPAddress ".$order_by_type." ";
    }  
    else if($data['OrderBy'] == 'TransactionType') {
        $sql = $sql . " order by t.TransactionType ".$order_by_type." ";
    } 
    else if($data['OrderBy'] == 'Description') {
        $sql = $sql . " order by t.Description ".$order_by_type." ";
    }
    elseif($data['OrderBy'] == 'FirstName') {
        $sql = $sql . " order by u.FirstName ".$order_by_type." ";
    } 	
    elseif($data['OrderBy'] == 'FacilityName') {
        $sql = $sql . " order by F.FacilityName ".$order_by_type." ";
    } 			
} else {
    $sql = $sql . " order by t.TransactionTime desc ";
}
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
            while($row = mysqli_fetch_assoc($query))
            {
                $row2  = array($row['FirstName'].' '.$row['LastName'],$row['ProfileName'],$row['TransactionType'],$row['FacilityName'],$row['Description'],$row['TransactionIPAddress'],$row['TransactionTime']);
                $rows[] = $row2;
            }

$sheet_name = 'User Activity List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 