<?php
	session_start();
	include_once("../database/consolidation.class.php");
	$obj = new ConsolidationClass();
	
	if($_POST['ajax'] == "GetCustomPalletDetails") {
		$result = $obj->GetCustomPalletDetails($_POST);
		echo $result;
	}
    
    if($_POST['ajax'] == "AddSerialToBin") {
		$result = $obj->AddSerialToBin($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "CloseBIN") {
		$result = $obj->CloseBIN($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "ConsolidateBIN") {
		$result = $obj->ConsolidateBIN($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "GetPalletDetails") {
		$result = $obj->GetPalletDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ConsolidateContainer") {
		$result = $obj->ConsolidateContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddSerialToContainer") {
		$result = $obj->AddSerialToContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCustomPalletDetailsForServerSwitch") {
		$result = $obj->GetCustomPalletDetailsForServerSwitch($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddServerSwitchToBin") {
		$result = $obj->AddServerSwitchToBin($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ConsolidateServerSwitchBIN") {
		$result = $obj->ConsolidateServerSwitchBIN($_POST);
		echo $result;
	}



	if($_POST['ajax'] == "GetCustomPalletDetailsForMedia") {
		$result = $obj->GetCustomPalletDetailsForMedia($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddMediaToBin") {
		$result = $obj->AddMediaToBin($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ConsolidateMediaBIN") {
		$result = $obj->ConsolidateMediaBIN($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "GetShipmentContainerDetails") {
		$result = $obj->GetShipmentContainerDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ConsolidateShipmentContainer") {
		$result = $obj->ConsolidateShipmentContainer($_POST);
		echo $result;
	}
?>