
<div class="row page" data-ng-controller="ContainerType">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Container Type Creation</span>
                        <div flex></div>
                           <a href="#!/ContainerTypeList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                             <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Facility</label>
                                        <md-select name="FacilityID" ng-model="ContainerType.FacilityID" required ng-disabled="ContainerType.idPackage">
                                            <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities">{{fac.FacilityName}}</md-option>
                                        </md-select>
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </md-input-container>
                             </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Container Type</label>
                                    <input type="text" name="packageName"  ng-model="ContainerType['packageName']"  required ng-maxlength="55" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.packageName.$error" multiple ng-if='material_signup_form.packageName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="maxlength">Max length 55.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Weight</label>
                                    <input type="text" name="packageWeight"  ng-model="ContainerType['packageWeight']"  required ng-maxlength="45" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.packageWeight.$error" multiple ng-if='material_signup_form.packageWeight.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Dimensions </label>
                                    <input type="text" name="dimensions "  ng-model="ContainerType['dimensions']"  required ng-maxlength="45" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.dimensions.$error" multiple ng-if='material_signup_form.dimensions.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Provider ID</label>
                                    <input type="text" name="providerid"  ng-model="ContainerType['providerid']"  required ng-maxlength="45" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.providerid.$error" multiple ng-if='material_signup_form.providerid.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Cost</label>
                                    <input type="text" name="containercost"  ng-model="ContainerType['containercost']"  required ng-maxlength="45" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.containercost.$error" multiple ng-if='material_signup_form.containercost.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Usage</label>
                                    <md-select name="Usage" ng-model="ContainerType.Usage" required aria-label="select">
                                        <md-option value="SingleUse"> SingleUse </md-option>
                                        <md-option value="Reuse"> Reuse </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                    <div ng-messages="material_signup_form.Active.$error" multiple ng-if='material_signup_form.Active.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="packageDescription"  ng-model="ContainerType['packageDescription']"  required ng-maxlength="250" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.packageDescription.$error" multiple ng-if='material_signup_form.packageDescription.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Container Classification</label>
                                    <md-select name="ContainerClassification" ng-model="ContainerType.ContainerClassification" required aria-label="select">
                                        <md-option value="Inbound"> Inbound </md-option>
                                        <md-option value="Outbound"> Outbound </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                    <div ng-messages="material_signup_form.ContainerClassification.$error" multiple ng-if='material_signup_form.ContainerClassification.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="ContainerType.OptionalLocation" aria-label="OptionalLocation" ng-true-value="'1'" ng-false-value="'0'"> Optional Location </md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="ContainerType.PrefixRequiredForBinName" aria-label="PrefixRequiredForBinName" ng-true-value="'1'" ng-false-value="'0'"> Prefix Required For Bin Name </md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="ContainerType.Active" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In Active </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                    <div ng-messages="material_signup_form.Active.$error" multiple ng-if='material_signup_form.Active.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Classification Type</label>
                                    <md-select name="WasteClassificationType" ng-model="ContainerType.WasteClassificationType" required aria-label="select">
                                        <md-option value="UEEE"> UEEE </md-option>
                                        <md-option value="WEEE"> WEEE </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                    <div ng-messages="material_signup_form.WasteClassificationType.$error" multiple ng-if='material_signup_form.WasteClassificationType.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>

                            <div class="col-md-12 btns-row">
                                <a href="#!/ContainerTypeList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="PackageTypeSave()">
                                <span ng-show="! ContainerType.busy">Save</span>
                                <span ng-show="ContainerType.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>