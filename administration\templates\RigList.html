<div ng-controller = "RigList" class="page">

    <div ng-show="loading" class="loading" style="text-align:center;"><img src="../images/loading2.gif" /> LOADING...</div>

    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="RigList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="RigList = !RigList" class="material-icons md-primary" ng-show="RigList">keyboard_arrow_up</i>
                                <i ng-click="RigList = !RigList" class="material-icons md-primary" ng-show="! RigList">keyboard_arrow_down</i>
                                <span ng-click="RigList = !RigList">Rig List</span>
                                <div flex></div>
                                <a href="#!/RigList" ng-click="ExportRigList()" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>
                                <div class="upload-btn-wrapper text-center mt-5">
                                    <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                                    <input type="file" ng-file-select="onFileSelect($files)" id="RigFile">  
                                    <a href="../../sample_files/upload_Rig_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                                </div> 
                               
                                <a href="#!/Rig" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Rig
                                </a>
                            </div>
                        </md-toolbar>

                         <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">                            
                            <p>No Rigs available </p>
                        </div>

                        <div class="row"  ng-show="pagedItems.length > 0">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div> 
                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Rigname')" ng-class="{'orderby' : OrderBy == 'Rigname'}">
                                                        <div>                               
                                                            Rig Name<i class="fa fa-sort pull-right" ng-show="OrderBy != 'Rigname'"></i>                                 
                                                            <span ng-show="OrderBy == 'Rigname'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">                           
                                                        <div>                               
                                                            Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>                                    
                                                            <span ng-show="OrderBy == 'Description'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SiteName')" ng-class="{'orderby' : OrderBy == 'SiteName'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Work Station Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'SiteName'"></i>                                  
                                                            <span ng-show="OrderBy == 'SiteName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('RigLimit')" ng-class="{'orderby' : OrderBy == 'RigLimit'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Rig Limit <i class="fa fa-sort pull-right" ng-show="OrderBy != 'RigLimit'"></i>                                  
                                                            <span ng-show="OrderBy == 'RigLimit'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>                                  
                                                            <span ng-show="OrderBy == 'StatusName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Rigname" ng-model="filter_text[0].Rigname" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Description" ng-model="filter_text[0].Description" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SiteName" ng-model="filter_text[0].SiteName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="RigLimit" ng-model="filter_text[0].RigLimit" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                   
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/Rig/{{product.RigID}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a></td>
                                                    <td>
                                                        {{product.Rigname}}                            
                                                    </td>                       
                                                    <td>
                                                        {{product.Description}}
                                                    </td>
                                                    <td>
                                                        {{product.SiteName}}
                                                    </td>

                                                    <td>
                                                        {{product.RigLimit}}
                                                    </td>

                                                    <td>
                                                        {{product.StatusName}}
                                                    </td>                 
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="7">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>