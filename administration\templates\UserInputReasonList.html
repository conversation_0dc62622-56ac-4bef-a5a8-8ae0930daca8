<div ng-controller = "DispositionOverrideUserInputReasonsList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="RemovalCodeList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="RemovalCodeList = !RemovalCodeList" class="material-icons md-primary" ng-show="RemovalCodeList">keyboard_arrow_up</i>
                                <i ng-click="RemovalCodeList = !RemovalCodeList" class="material-icons md-primary" ng-show="! RemovalCodeList">keyboard_arrow_down</i>
                                <span ng-click="RemovalCodeList = !RemovalCodeList">User Input Reason List</span>
                                <div flex></div> 
                                <a href="#!/UserInputReasonList" ng-click="ExportUserInputReasonList()" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>
                                <!-- <a href="#!/UserInputReason" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New User Input Reason
                                </a> -->
                            </div>
                        </md-toolbar>
                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">                            
                            <p>No User Input Reason Available </p>
                        </div>
                        <div class="row"  ng-show="RemovalCodeList">
                            <div class="col-md-12">
                                <div class="col-md-12">                            
                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <!-- <th style="min-width: 40px;">Edit</th> -->

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('UserInput')" ng-class="{'orderby' : OrderBy == 'UserInput'}">
                                                        <div>                               
                                                            User Input<i class="fa fa-sort pull-right" ng-show="OrderBy != 'UserInput'"></i>                                 
                                                            <span ng-show="OrderBy == 'UserInput'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>  
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ActivateReasonCode')" ng-class="{'orderby' : OrderBy == 'ActivateReasonCode'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Activate Reason Code <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ActivateReasonCode'"></i>                                  
                                                            <span ng-show="OrderBy == 'ActivateReasonCode'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('NotesRequired')" ng-class="{'orderby' : OrderBy == 'NotesRequired'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Notes Required <i class="fa fa-sort pull-right" ng-show="OrderBy != 'NotesRequired'"></i>                                  
                                                            <span ng-show="OrderBy == 'NotesRequired'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->
                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <!-- <td>&nbsp;</td> -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="UserInput" ng-model="filter_text[0].UserInput" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <!-- <td></td> -->
                                                    <td></td>                                                                    
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <!-- <td><a href="#!/UserInputReason/{{product.UserInputID}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a></td> -->
                                                    <td>
                                                        {{product.UserInput}}                            
                                                    </td> 
                                                    <td >
                                                        <md-switch ng-model="product.ActivateReasonCode" aria-label="Activate Reason Code" ng-true-value="'1'" ng-false-value="'0'" ng-change="RecordNewReasonCode(product,$event)"></md-switch>
                                                    </td>                      
                                                    <!-- <td >
                                                        <md-switch ng-model="product.NotesRequired" aria-label="Notes Required" ng-true-value="'1'" ng-false-value="'0'" ng-disabled="true"></md-switch>
                                                    </td> -->
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="5">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>