<div ng-controller = "SourceTypeConfigurationList" class="page">
    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="ContainerTypeList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="ContainerTypeList = !ContainerTypeList" class="material-icons md-primary" ng-show="ContainerTypeList">keyboard_arrow_up</i>
                                <i ng-click="ContainerTypeList = !ContainerTypeList" class="material-icons md-primary" ng-show="! ContainerTypeList">keyboard_arrow_down</i>
                                <span ng-click="ContainerTypeList = !ContainerTypeList">Source Type Configuration List</span>
                                <div flex></div> 
                                 <a href="#!/SourceTypeConfigurationList" ng-click="ExportSourceTypeConfigurationList()" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a> 
                                <a href="#!/SourceTypeConfiguration" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create Configuration
                                </a>
                            </div>
                        </md-toolbar>
                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">                            
                            <p>No Source Type Configuration Available </p>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div class="tablemovebtns">
                                        <a class="md-button md-raised md-default" id="left-button"><i class="material-icons">keyboard_arrow_left</i></a>
                                    </div>
                                    <div class="tablemovebtns">
                                        <a class="md-button md-raised md-default" id="right-button"><i class="material-icons">keyboard_arrow_right</i></a>
                                    </div>

                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">
                                                        <div>                               
                                                            Facility<i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                 
                                                            <span ng-show="OrderBy == 'FacilityName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Customer')" ng-class="{'orderby' : OrderBy == 'Customer'}">
                                                        <div>                               
                                                            Customer ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Customer'"></i>                                 
                                                            <span ng-show="OrderBy == 'Customer'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('MaterialType')" ng-class="{'orderby' : OrderBy == 'MaterialType'}">                           
                                                        <div>                               
                                                            Material Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'MaterialType'"></i>                                    
                                                            <span ng-show="OrderBy == 'MaterialType'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Cumstomertype')" ng-class="{'orderby' : OrderBy == 'Cumstomertype'}">                           
                                                        <div>                               
                                                            Source Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Cumstomertype'"></i>                                    
                                                            <span ng-show="OrderBy == 'Cumstomertype'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                   
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">                           
                                                        <div>                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>                                    
                                                            <span ng-show="OrderBy == 'Status'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Customer" ng-model="filter_text[0].Customer" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="MaterialType" ng-model="filter_text[0].MaterialType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Cumstomertype" ng-model="filter_text[0].Cumstomertype" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                                                    
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/SourceTypeConfiguration/{{product.ConfigurationID}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a>
                                                    </td>

                                                    <td>
                                                        {{product.FacilityName}}                            
                                                    </td>

                                                    <td>
                                                        {{product.Customer}}                            
                                                    </td>  
                                                    
                                                    <td>
                                                        {{product.MaterialType}}
                                                    </td>
                                                    
                                                    <td>
                                                        {{product.Cumstomertype}}
                                                    </td>                                                   

                                                    <td>
                                                        {{product.Status}}
                                                    </td>                                                    
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="7">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>