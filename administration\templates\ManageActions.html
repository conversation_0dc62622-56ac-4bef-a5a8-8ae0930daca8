<div class="row page" data-ng-controller="ManageActions">
    <div class="col-md-12">
        <article class="article">
            <form name="material_signup_form" class="form-validation" >
                <md-card class="no-margin-h">
                    
                    <md-toolbar class="md-table-toolbar md-default">
                        <div class="md-toolbar-tools">
                            <span>Manage Actions</span>
                            <div flex></div>                            
                        </div>
                    </md-toolbar>
                                    
                    <div class="col-md-4 col-md-offset-4">
                        <fieldset>
                            <md-input-container class="md-block">                                            
                                <label>Request Type</label>
                                <md-select name="CategoryID" ng-model="newAction.CategoryID" required aria-label="select" ng-change="GetCategoryActions()">
                                    <md-option ng-repeat="cat in Categories" value="{{cat.CategoryID}}"> {{cat.CategoryName}} </md-option>
                                </md-select>   
                                <div ng-messages="material_signup_form.CategoryID.$error" multiple ng-if='material_signup_form.CategoryID.$dirty'>
                                    <div ng-message="required">This is required.</div>                                           
                                </div>                                             
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Actions</label>
                                <md-select name="ActionID" ng-model="newAction.ActionID" required aria-label="select" >
                                    <md-option ng-repeat="act in AllActions" value="{{act.ActionID}}"> {{act.Action}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.ActionID.$error" multiple ng-if='material_signup_form.ActionID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <div class="col-md-12 btns-row">
                                <md-button 
                                class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || newAction.busy" ng-click="CreateCategoryAction('Submit')">
                                    <span ng-show="! newAction.busy">Add</span>
                                    <span ng-show="newAction.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>
                            
                        </fieldset>
                    </div>
                </md-card>                
            </form>     
            


            <div class="panel panel-default" ng-show="newAction.CategoryID">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        Existing Actions                        
                    </h3>                                        
                </div>
                <div class="panel-body">
                    <table class="table table-striped no-margin" style="min-width: 1500px;">
                        <thead>
                            <tr>
                                <th>Action</th>
                                <th>Status Changes to</th>
                                <th>Ticket Closes</th>
                                <th>Delete</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="act in CategoryActions">
                                <td>{{act.Action}}</td>
                                <td>{{act.TicketStatus}}</td>
                                <td>
                                    <span ng-show="act.ClosedAction == '1'">Ticket will be Closed</span>                                    
                                </td>
                                <td>                                    
                                    <md-button class="md-icon md-warn" ng-click="DeleteCategoryAction($event,$index,act)">
                                        <md-icon>delete_forever</md-icon> 
                                    </md-button>
                                </td>
                            </tr>
                        </tbody>
                    </table>                    
                </div>                
            </div>



            

        </article>                            
    </div>

</div>