<md-dialog aria-label="Load Verification" style="max-width:1000px; max-height: 88%; min-width: 400px;">
  <form name="LoadVerification" class="form-validation" >
    <md-toolbar>
      <div class="md-toolbar-tools">
        <h2>Move Bin & Scan New Bin</h2>
        <span flex></span>
        <md-button class="md-icon-button" ng-click="cancel()">
          <md-icon class="material-icons" aria-label="Close dialog">close</md-icon>
        </md-button>
      </div>
    </md-toolbar>


    <md-dialog-content>
        <div class="md-dialog-content">
          <div class="row">
            <div class="col-md-12">
                <div class="col-md-8">
                  Current Bin:<p style="font-weight: bold;"> {{CurrentBin.BinName}}</p>
                </div>
                <div class="col-md-4" >
                  <div class="autocomplete insideuse">
                       <md-input-container class="md-block">
                           <md-autocomplete flex  required style="margin-bottom:0px !important; margin-top:0px !important; padding-top: 0px !important;"
                               md-input-name="group"
                               md-input-maxlength="100"
                               md-no-cache="noCache"
                               md-search-text-change="LocationChange(CurrentNewBin.group)"
                               md-search-text="CurrentNewBin.group"
                               md-items="item in queryLocationSearch(CurrentNewBin.group)"
                               md-item-text="item.GroupName"
                               md-selected-item-change="selectedLocationChange(item)"
                               md-min-length="0"
                               ng-model-options='{ debounce: 1000 }'
                               placeholder="New Location Group">
                               <md-item-template>
                                   <span md-highlight-text="CurrentNewBin.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                               </md-item-template>
                               <md-not-found>
                                   No Records matching "{{CurrentNewBin.group}}" were found.
                               </md-not-found>
                               <div ng-messages="material_signup_form.group.$error" ng-if="material_signup_form.group.$touched">
                                   <div ng-message="required">No Records matching.</div>
                               </div>
                           </md-autocomplete>
                       </md-input-container>

                   </div>
               </div>
                <div class="col-md-4">
                    Current Location:<p style="font-weight: bold;"> {{CurrentBin.groupname}}</p>
                </div>
                <div class="col-md-8">
                    <md-input-container class="md-block">
                        <label>New Bin</label>
                        <input type="text" name="NewBinName" ng-model="CurrentNewBin.NewBinName" ng-min="1" ng-max="100" />
                    </md-input-container>
                </div>
            </div>
          </div>
        </div>
    </md-dialog-content>

    <md-dialog-actions layout="row" style="justify-content: center;">
      <md-button class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Close</md-button>
       <md-button class="md-button md-raised btn-w-md md-primary" ng-disabled="isValidBinSelected()" ng-click="CreateMoveBin()">Continue</md-button>
    </md-dialog-actions>

  </form>
</md-dialog>
