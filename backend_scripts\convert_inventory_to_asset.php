<?php
session_start();
include_once("../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();


$query= "select * from upload_inventory_to_asset where InventoryStatusID = 1 AND isnull(AssetScanID) Limit 1000";
$q = mysqli_query($connectionlink,$query);
if(mysqli_affected_rows($connectionlink) > 0) {
    $i = 0;
    while($row = mysqli_fetch_assoc($q)) {

        while(1) {
			// generate unique random number
			$randomNumber = rand(10000000000, 99999999999);
			//$randomNumber = PRE.$randomNumber;
			$randomNumber = $randomNumber;
			// check if it exists in database
			$query = "SELECT * FROM `asset` WHERE AssetScanID = '".mysqli_real_escape_string($connectionlink,$randomNumber)."'";
			$res = mysqli_query($connectionlink,$query);
			$rowCount = mysqli_num_rows($res);
			/*if(mysqli_affected_rows($connectionlink) > 0){	
				$row = mysqli_fetch_assoc($res);
				$rowCount = $row['count(*)'];
			} else {
				$rowCount = 1;
			}*/
			if($rowCount < 1) {
				break;
			}
		}

        $HarvestAssetScanID = $randomNumber;

        $query12 = "insert into asset (AssetScanID,SerialNumber,Quantity,FacilityID,DateCreated,StatusID,AccountID,CustomPalletID,UniversalModelNumber,AssetLevel,disposition_id) values ('".mysqli_real_escape_string($connectionlink,$HarvestAssetScanID)."','".mysqli_real_escape_string($connectionlink,$row['SerialNumber'])."','1','".$row['Facility']."',NOW(),'1','1','".$row['Bin']."','".$row['MPN']."','1','".$row['Disposition']."')";
        $q12 = mysqli_query($connectionlink,$query12);
        if(mysqli_error($connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($connectionlink);
            return json_encode($json);
        }
        $inventory_id = mysqli_insert_id($connectionlink);
        //Start inserting into custompallet
        $query13 = "insert into custompallet_items (AssetScanID,DateCreated,status,CreatedBy,CustomPalletID) values ('".mysqli_real_escape_string($connectionlink,$HarvestAssetScanID)."',NOW(),'1','".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($connectionlink,$row['Bin'])."')";
        $q13 = mysqli_query($connectionlink,$query13);
        if(mysqli_error($connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($connectionlink);
            return json_encode($json);
        }

        $query9 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($connectionlink,$row['Bin'])."'";
        $q9 = mysqli_query($connectionlink,$query9);
        if(mysqli_error($connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($connectionlink);
            return json_encode($json);
        }
        //End inserting into Custompallet


        $query22 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate) values ('".mysqli_real_escape_string($connectionlink,$HarvestAssetScanID)."','Copied from SubComponent to Regular','','',NOW())";
        $q22 = mysqli_query($connectionlink,$query22);
        if(mysqli_error($connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($connectionlink);
            return json_encode($json);
        }

        //End create asset

        $query10 = "update upload_inventory_to_asset set AssetScanID = '".$HarvestAssetScanID."' where id = '".$row['id']."'";
        $q10 = mysqli_query($connectionlink,$query10);
        if(mysqli_error($connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($connectionlink);
            return json_encode($json);
        }


        $i = $i + 1;
    }
    echo $i." Records Completed";
} else {
    echo "No Records";
}
?>