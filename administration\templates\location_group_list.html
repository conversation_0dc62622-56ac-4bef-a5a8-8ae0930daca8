<div class="row page  page-invoice" data-ng-controller="LocationGroupList" >  
    <div class="col-md-12">
        <article class="article">
            
            <md-card class="no-margin-h">
                    
                <md-toolbar class="md-table-toolbar md-default" ng-hide="options.rowSelection && selected.length">
                    <div class="md-toolbar-tools">
                        <span>Location Group List</span>
                        <div flex></div>
                        <a href="#!/LocationGroupList" ng-click="Exportlocationgrouplist()" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>
                            <div class="upload-btn-wrapper text-center mt-5">
                                    <button class="md-button md-raised btn-w-md md-primary mr-5" style="display: flex; cursor: pointer; float: right;"><i class="material-icons mr-5" style="margin-top: 2px;">file_upload</i>Upload File</button>                           
                                    <input type="file" ng-file-select="onFileSelect($files)" id="locationgroupFile">  
                                    <a href="../../sample_files/upload_locationgroup_File.xlsx" target="_blank" class="md-button btn-w-md text-warning mr-5" style="float: right; line-height: 34px;display: flex;"><i class="material-icons mr-5 text-warning" style="margin-top: 2px;">file_download</i><span class="text-warning">Sample File</span></a> 
                                </div>     
                            <a href="#!/LocationGroup" class="md-button md-raised btn-w-md" style="display: flex;">
                            <i class="material-icons">add</i> New Group
                        </a>
                    </div>
                </md-toolbar>
                <div class="row">
                    <div class="col-md-12">
                        <div class="col-md-12">

                            <div class="tablemovebtns">
                                <a class="md-button md-raised md-default" id="left-button"><i class="material-icons">keyboard_arrow_left</i></a>
                            </div>
                            <div class="tablemovebtns">
                                <a class="md-button md-raised md-default" id="right-button"><i class="material-icons">keyboard_arrow_right</i></a>
                            </div>

                            <div ng-show="pagedItems" class="pull-right pageditems">
                                <small>
                                    Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                    to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                        <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                    of <span style="font-weight:bold;">{{total}}</span>
                                </small>
                            </div>
                            <div style="clear:both;"></div>

                            <div class="table-container table-responsive" style="overflow: auto;">
                                
                                <table class="table table-striped">
                                    <thead>
                                        <tr class="th_sorting">
                                            <th style="min-width: 40px;">Edit</th>
                                            <th style="cursor:pointer;min-width:180px;" ng-click="MakeOrderBy('GroupName')" ng-class="{'orderby' : OrderBy == 'GroupName'}">
                                                <div>                               
                                                    Group Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'GroupName'"></i>                                 
                                                    <span ng-show="OrderBy == 'GroupName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>
                                            </th>
                                                                                                                                    
                                            <th style="cursor:pointer; min-width:180px;" ng-click="MakeOrderBy('GroupDescription')" ng-class="{'orderby' : OrderBy == 'GroupDescription'}">                           
                                                <div>                               
                                                    Group Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'GroupDescription'"></i>                                    
                                                    <span ng-show="OrderBy == 'GroupDescription'">                                 
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>

                                            <th style="cursor:pointer; min-width:180px;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                           
                                                <div>                               
                                                    Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                    
                                                    <span ng-show="OrderBy == 'FacilityName'">                                 
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>
                
                                            <th style="cursor:pointer;  min-width:180px;" ng-click="MakeOrderBy('TotalLocations')" ng-class="{'orderby' : OrderBy == 'TotalLocations'}">                          
                                                <div>                               
                                                    Total Locations<i class="fa fa-sort pull-right" ng-show="OrderBy != 'TotalLocations'"></i>                                  
                                                    <span ng-show="OrderBy == 'TotalLocations'">                                  
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>
                                            <th style="min-width: 180px;">Unlocked Locations</th>
                                             <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('UnlockedLocations')" ng-class="{'orderby' : OrderBy == 'UnlockedLocations'}">                          
                                                <div>                               
                                                    Unlocked Locations<i class="fa fa-sort pull-right" ng-show="OrderBy != 'UnlockedLocations'"></i>                                  
                                                    <span ng-show="OrderBy == 'UnlockedLocations'">                                  
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th> -->
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('LocationType')" ng-class="{'orderby' : OrderBy == 'LocationType'}">                          
                                                <div>                               
                                                    Location Type<i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationType'"></i>                                  
                                                    <span ng-show="OrderBy == 'LocationType'">                                  
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">                          
                                                <div>                               
                                                    Status<i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>                                  
                                                    <span ng-show="OrderBy == 'StatusName'">                                  
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                    </span>                                                                                                                                                             
                                                </div>                                                                                  
                                            </th>

                                        </tr>
                                        
                                        <tr class="errornone">                        
                                            <td>&nbsp;</td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="GroupName" ng-model="filter_text[0].GroupName" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
    
                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="GroupDescription" ng-model="filter_text[0].GroupDescription" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td> 

                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td>

                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="TotalLocations" ng-model="filter_text[0].TotalLocations" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td>
                                            <td>
                                               <!--  <md-input-container class="md-block mt-0"><input type="text" name="UnlockedLocations" ng-model="filter_text[0].UnlockedLocations" ng-change="MakeFilter()" aria-label="text" /></md-input-container> -->
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="LocationType" ng-model="filter_text[0].LocationType" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0"><input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                            </td>                   
                                        </tr>
                                    </thead>
                                    
                                    <tbody ng-show="pagedItems.length > 0">
                                        <tr ng-repeat="product in pagedItems">
                                            <td><a href="#!/LocationGroup/{{product.GroupID}}"><md-icon class="material-icons text-danger">edit</md-icon></a></td>
                                           <!--  <td><a href="../label/master/examples/locationgrouplabel.php?id={{product.GroupID}}"><md-icon class="material-icons text-danger">print</md-icon></a></td> -->
    
                                            <td>
                                                {{product.GroupName}}                            
                                            </td>                       
                                            <td>
                                                {{product.GroupDescription}}
                                            </td>
                                            
                                            <td>
                                                {{product.FacilityName}}
                                            </td>
                                            
                                            <td>
                                                {{product.TotalLocations}}
                                            </td>
                                            <td>
                                                {{product.UnlockedLocations}}
                                            </td>    
                                            <td>
                                                {{product.LocationType}}
                                            </td>
                                            <td>
                                                {{product.StatusName}}
                                            </td>
                                            
                                        </tr>
                                    </tbody>
                                    
                                    <tfoot>
                                        <tr>
                                            <td colspan="8">
                                                <div>
                                                    <ul class="pagination">
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="firstPage()"><< First</a>
                                                        </li>
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="prevPage()"><< Prev</a>
                                                        </li>
                                                        <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                            <a style="cursor:pointer;">{{n+1}}</a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="nextPage()">Next >></a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="lastPage()">Last >></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>   
                                        </tr>             
                                    </tfoot>
                                </table>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </md-card>
        </article>                            
    </div>
</div>