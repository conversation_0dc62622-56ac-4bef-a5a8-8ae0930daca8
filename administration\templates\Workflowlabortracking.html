
<div class="row page" data-ng-controller="Workflowlabortracking">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Labor Tracking Workflow</span>
                        <div flex></div>
                            <a href="#!/WorkflowlabortrackingList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                    </div>
                </md-toolbar>
                <form name="Workflowlabortracking_form" class="form-validation" data-ng-submit="submitForm()">
                <div class="row">
                    <div class="col-md-12">
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Workflow Labor Tracking Name</label>
                                    <input type="text" name="workflowlabortrackingname"  ng-model="Workflowlabortracking['workflowlabortrackingname']"  required ng-maxlength="45" />
                                    <div ng-messages="Workflowlabortracking_form.workflowlabortrackingname.$error" multiple ng-if='Workflowlabortracking_form.workflowlabortrackingname.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="workflow_labor_trackingdescription"  ng-model="Workflowlabortracking['workflow_labor_trackingdescription']"  required ng-maxlength="250" />
                                    <div ng-messages="Workflowlabortracking_form.workflow_labor_trackingdescription.$error" multiple ng-if='Workflowlabortracking_form.workflow_labor_trackingdescription.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>
                                    </div>

                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="Workflowlabortracking.Status" required aria-label="select">
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="In Active"> In Active </md-option>
                                    </md-select>
                                    <div ng-messages="Workflowlabortracking_form.Status.$error" multiple ng-if='Workflowlabortracking_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                </md-input-container>
                            </div>
                          </div>
                          <div class="col-md-12">
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Labor Type</label>
                                    <md-select name="laborType" ng-model="Workflowlabortracking.labor_type" required aria-label="select">
                                        <md-option value="Direct"> Direct </md-option>
                                        <md-option value="Indirect"> Indirect </md-option>
                                    </md-select>
                                    <div ng-messages="material_signup_form.laborType.$error" multiple ng-if='Workflowlabortracking_form.laborType.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                </md-input-container>
                            </div>
                              <div class="col-md-4">
                                  <md-input-container class="md-block">
                                      <label>Min Workflow Time Hours</label>
                                      <input type="number" string-to-number name="minHours" min="0" max="11" ng-model="Workflowlabortracking.minHours" aria-label="Enter Hours" required>
                                  </md-input-container>
                              </div>

                              <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Min Workflow Time Minutes</label>
                                    <input type="number" string-to-number min="0" max="0.75" step="0.25" name="minMinutes" ng-model="Workflowlabortracking.minMinutes" aria-label="Enter Minutes" required>
                                </md-input-container>
                              </div>
                            </div>
                            <div class="col-md-12">
                              <div class="col-md-4">
                                  <md-input-container class="md-block">
                                      <label>Max Workflow Time Hours</label>
                                      <input type="number" string-to-number name="maxHours" min="0" max="11" ng-model="Workflowlabortracking.maxHours" aria-label="Enter Hours" required>
                                  </md-input-container>
                              </div>

                              <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Max Workflow Time Minutes</label>
                                    <input type="number" string-to-number min="0" max="0.75" step="0.25" name="maxMinutes" ng-model="Workflowlabortracking.maxMinutes" aria-label="Enter Minutes" required>
                                </md-input-container>
                              </div>
                            </div>
                              <div class="col-md-12 btns-row">
                                  <a href="#!/WorkflowlabortrackingList" style="text-decoration: none;">
                                  <md-button class="md-button md-raised btn-w-md  md-default">
                                      Cancel  `
                                  </md-button>
                              </a>
                                  <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                  data-ng-disabled="Workflowlabortracking_form.$invalid || Workflowlabortracking.busy" ng-click="SaveWorkflowlabortracking()">
                                  <span ng-show="! Workflowlabortracking.busy">Save</span>
                                  <span ng-show="Workflowlabortracking.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                              </div>

                </div>
              </form>
            </md-card>
        </article>
    </div>
</div>
