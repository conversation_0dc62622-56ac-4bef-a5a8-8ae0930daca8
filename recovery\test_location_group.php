<?php
session_start();
include_once("database/recovery.class.php");

// Set a test session for testing
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = array(
        'UserId' => 1,
        'FacilityID' => 1
    );
}

$obj = new RecoveryClass();

// Test with a sample SiteID - you can change this to a real SiteID from your database
$testSiteID = 1;

echo "<h2>Testing Location Group Population</h2>";
echo "<p>Testing with SiteID: $testSiteID</p>";

// Test the GetStationLocationGroupAndAssignLocation function
$testData = array('SiteID' => $testSiteID);
$result = $obj->GetStationLocationGroupAndAssignLocation($testData);

echo "<h3>Result:</h3>";
echo "<pre>" . $result . "</pre>";

// Also test the basic GetStationLocationGroup function
echo "<h3>Basic GetStationLocationGroup Result:</h3>";
$basicResult = $obj->GetStationLocationGroup($testData);
echo "<pre>" . $basicResult . "</pre>";

// Let's also check what sites exist in the database
echo "<h3>Available Sites in Database:</h3>";
$connection = Connection::DBConnect();
$sitesQuery = "SELECT SiteID, SiteName, GroupID FROM site LIMIT 10";
$sitesResult = mysqli_query($connection, $sitesQuery);

if ($sitesResult) {
    echo "<table border='1'>";
    echo "<tr><th>SiteID</th><th>SiteName</th><th>GroupID</th></tr>";
    while ($row = mysqli_fetch_assoc($sitesResult)) {
        echo "<tr>";
        echo "<td>" . $row['SiteID'] . "</td>";
        echo "<td>" . $row['SiteName'] . "</td>";
        echo "<td>" . $row['GroupID'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error querying sites: " . mysqli_error($connection);
}

// Let's also check location groups
echo "<h3>Available Location Groups:</h3>";
$groupsQuery = "SELECT GroupID, GroupName, LocationType FROM location_group LIMIT 10";
$groupsResult = mysqli_query($connection, $groupsQuery);

if ($groupsResult) {
    echo "<table border='1'>";
    echo "<tr><th>GroupID</th><th>GroupName</th><th>LocationType</th></tr>";
    while ($row = mysqli_fetch_assoc($groupsResult)) {
        echo "<tr>";
        echo "<td>" . $row['GroupID'] . "</td>";
        echo "<td>" . $row['GroupName'] . "</td>";
        echo "<td>" . $row['LocationType'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error querying location groups: " . mysqli_error($connection);
}
?>
