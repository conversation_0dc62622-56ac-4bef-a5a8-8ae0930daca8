<?php
session_start();
include_once("../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();

$SequenceNumber = 0;
$query= "select * from upload_custompallet1 where isnull(CustomPalletID) and Disposition = 96 ";
$q = mysqli_query($connectionlink,$query);
if(mysqli_affected_rows($connectionlink) > 0) {
    $i = 0;
    while($row = mysqli_fetch_assoc($q)) {
        
        $query111 = "insert into `custompallet` (`CustomPalletID`, `FacilityID`,`LocationID`, `Room`,`Description`,`disposition_id`,`StatusID`,BinType,MaximumAssets) VALUES (NULL, '" . $row['Facility'] . "', '".mysqli_real_escape_string($connectionlink,$row['Location'])."', '" . mysqli_real_escape_string($connectionlink, $row['Room']) . "','" . mysqli_real_escape_string($connectionlink, $row['Description']) . "','" . mysqli_real_escape_string($connectionlink, $row['Disposition']) . "', '1', '" . mysqli_real_escape_string($connectionlink, $row['BinType']) . "','" . mysqli_real_escape_string($connectionlink, $row['MaximumAssets']) . "' )";
        $q111 = mysqli_query($connectionlink,$query111);
        if(mysqli_error($connectionlink)) {
            $json['Success'] = false;
            echo mysqli_error($connectionlink)."<br>";
            break;            
        }
        $insert_id = mysqli_insert_id($connectionlink);

        $query1 = "select f.FacilityName,d.disposition,c.Room from facility f,disposition d,custompallet c where f.FacilityID = c.FacilityID AND d.disposition_id = c.disposition_id AND c.CustomPalletID = '" . $insert_id . "'";
        $q1 = mysqli_query($connectionlink, $query1);
        if (mysqli_error($connectionlink)) {
            echo mysqli_error($connectionlink).$query1;
        }
        if (mysqli_affected_rows($connectionlink) > 0) {
            $row1 = mysqli_fetch_assoc($q1);

            $disposition_sequence = $SequenceNumber + $i + 1;
            //$binname = $row1['FacilityName'].".".$row1['Room'].".".$row1['disposition']."-".$insert_id;
            $binname = $row1['FacilityName'] . "." . $row1['Room'] . "." . $row1['disposition'] . "-" . sprintf('%03d', $disposition_sequence);
            $query2 = "update custompallet set BinName='" . mysqli_real_escape_string($connectionlink, $binname) . "',disposition_sequence_number = '" . $disposition_sequence . "' WHERE CustomPalletID= '" . $insert_id . "'";
            $q2 = mysqli_query($connectionlink, $query2);
            if (mysqli_error($connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($connectionlink);
                return json_encode($json);
            }
        }

        $query3 = "update location set Locked = '1', currentItemType = 'BIN',currentItemID = '".$binname."' where LocationID = '".mysqli_real_escape_string($connectionlink,$row['Location'])."'";
        $q3 = mysqli_query($connectionlink,$query3);
        if(mysqli_error($connectionlink)) {
            $json['Success'] = false;
            $json['Result'] = mysqli_error($connectionlink);
            return json_encode($json);
        }

        $query4 = "update upload_custompallet1 set CustomPalletID = '".$insert_id."' where id = '".$row['id']."'";
        $q4 = mysqli_query($connectionlink,$query4);
        if(mysqli_error($connectionlink)) {
            echo mysqli_error($connectionlink) .$query4;
        }

        $i = $i + 1;
    }
    echo $i." Records Completed";
} else {
    echo "No Records";
}
?>