<div class="page" data-ng-controller="outbound_container_consolidation">
    <div class="row ui-section">            
        <div class="col-md-12">
            <article class="article">

                <h5 class="custom_heading"><i class="material-icons mr-5">view_module</i> Outbound Container Consolidation</h5>

                <!--BIN Start-->
                <md-card class="no-margin-h">  
                    <md-toolbar class="md-table-toolbar md-default" ng-init="frombin = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="frombin = !frombin">                            
                            <i class="material-icons md-primary" ng-show="frombin">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! frombin">keyboard_arrow_down</i>
                            <span>From Container</span>
                        </div>
                    </md-toolbar>
                    <md-card-content class="pt-0" ng-show="frombin">

                        <div class="col-md-12">
                                                            
                                <div class="col-md-6 col-md-offset-3">
                                    <md-input-container class="md-block">
                                        <label>From Container</label>
                                        <input required name="FromBinName" ng-model="FromBinName" required style="padding-right: 35px;" ng-enter="GetCustomPalletDetails('From',FromBinName)">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="GetCustomPalletDetails('From',FromBinName)" ng-disabled="!FromBinName">
                                            Go
                                        </md-button>
                                    </md-input-container>
                                </div>
                                <div style="clear: both;"></div>
                            
                        </div>

                        <div class="row" ng-show="FromCustomPallet.ShippingContainerID">
                            <div class="col-md-12">
                                    <md-card-content class="pt-0">
                                        <div class="table-responsive">
                                            <table class="table mb-0">
                                                <thead>
                                                    <tr class="bg-grey">
                                                        <th>Container ID</th>
                                                        <th>Removal Type</th>
                                                        <th style="min-width:180px;">Current Count</th>
                                                        <!-- <th style="min-width:220px;">Add Serial</th>-->
                                                        <!-- <th>Close Container</th> -->
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr >
                                                        <td>
                                                            {{FromCustomPallet.ShippingContainerID}}
                                                        </td> 
                                                        <td>
                                                            {{FromCustomPallet.disposition}}
                                                        </td>                                                      
                                                        <td>{{FromCustomPallet.AssetsCount}}</td>
                                                        <!-- <td>
                                                            <md-input-container class="md-block md-no-float includedsearch tdinput" style="margin-top:12px;">
                                                                <input required name="SerialNumber" ng-model="SerialNumber" required placeholder="Serial Number">
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="AddSerialToBin(SerialNumber,FromCustomPallet,'From')" ng-disabled="!SerialNumber">
                                                                    <i class="material-icons" style="margin-top:3px;">
                                                                        add
                                                                    </i>
                                                                </md-button>
                                                            </md-input-container>
                                                        </td>-->
                                                        <!-- <td>
                                                            <md-checkbox ng-model="FromCustomPallet.CloseContainer" aria-label="CloseContainer" ng-true-value="'1'" ng-false-value="'0'" class="md-primary"> Close Container </md-checkbox>
                                                        </td> -->
                                                    </tr>                                        
                                                </tbody>
                                            </table>
                                        </div>
                                    </md-card-content>        
                            </div>
                        </div>
                    </md-card-content>
                </md-card>
                <!--BIN Close-->

                <!--BIN Start-->
                <md-card class="no-margin-h">  
                    <md-toolbar class="md-table-toolbar md-default" ng-init="tobin = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="tobin = !tobin">                            
                            <i class="material-icons md-primary" ng-show="tobin">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! tobin">keyboard_arrow_down</i>
                            <span>To Container</span>
                        </div>
                    </md-toolbar>
                    <md-card-content class="pt-0" ng-show="tobin">

                        <div class="col-md-12">
                                                           
                                <div class="col-md-6 col-md-offset-3">
                                    <md-input-container class="md-block">
                                        <label>To Container</label>
                                        <input required name="ToBinName" ng-model="ToBinName" required style="padding-right: 35px;" ng-enter="GetCustomPalletDetails('To',ToBinName)">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="GetCustomPalletDetails('To',ToBinName)" ng-disabled="!ToBinName">
                                            Go
                                        </md-button>
                                    </md-input-container>
                                </div>
                                <div style="clear: both;"></div>
                            
                        </div>

                        <div class="row" ng-show="ToCustomPallet.ShippingContainerID">
                            <div class="col-md-12">
                                
                                    <md-card-content class="pt-0">
                                        <div class="table-responsive">
                                            <table class="table mb-0">
                                                <thead>
                                                    <tr class="bg-grey">
                                                        <th>Container ID</th>
                                                        <th>Removal Type</th>
                                                        <th style="min-width:180px;">Current Count</th>
                                                        <!-- <th style="min-width:220px;">Add Serial</th> -->
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr >
                                                        <td>
                                                            {{ToCustomPallet.ShippingContainerID}}
                                                        </td>
                                                        <td>
                                                            {{ToCustomPallet.disposition}}
                                                        </td>                                                        
                                                        <td>{{ToCustomPallet.AssetsCount}}</td>
                                                        <!-- <td>
                                                            <md-input-container class="md-block md-no-float includedsearch tdinput" style="margin-top:12px;">
                                                                <input required name="ToSerialNumber" ng-model="ToSerialNumber" required placeholder="Serial Number">
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="AddSerialToBin(ToSerialNumber,ToCustomPallet,'To')" ng-disabled="!ToSerialNumber">
                                                                    <i class="material-icons" style="margin-top:3px;">
                                                                        add
                                                                    </i>
                                                                </md-button>
                                                            </md-input-container>
                                                        </td>                                                         -->
                                                    </tr>                                        
                                                </tbody>
                                            </table>
                                        </div>
                                    </md-card-content>       
                            </div>
                        </div>

                    </md-card-content>                    
                </md-card>
                <!--BIN Close-->


                <div class="col-md-12 btns-row">
                    <!-- <a href="#!/catlogcreationlist" style="text-decoration: none;">
                        <md-button class="md-button md-raised btn-w-md  md-default">
                            Cancel
                        </md-button>
                    </a> -->
                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                    data-ng-disabled="!FromCustomPallet.ShippingContainerID || !ToCustomPallet.ShippingContainerID || busy" ng-click="ConsolidateBIN($event)">
                        <span ng-show="! busy">Consolidate</span>
                        <span ng-show="busy">
                            <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular>
                        </span>
                    </md-button>
                </div>            
            </article>
        </div>
    </div>
</div>