(function () {
    'use strict';
    angular.module('app').controller("container_consolidation", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams) {
        $scope.FromCustomPallet = {};
        $scope.ToCustomPallet = {};
        $scope.SearchCustomPallet = {};
        $scope.GetCustomPalletDetails = function (type,BinName) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/consolidation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPalletDetails&BinName='+BinName,                    
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        if(type == 'From') {
                            $scope.FromCustomPallet = data.Result;
                        } else if(type == 'Search') {
                            $scope.SearchCustomPallet = data.Result;
                        }else {
                            $scope.ToCustomPallet = data.Result;
                        }                        
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        if(type == 'From') {
                            $scope.FromCustomPallet = {};
                        } else if(type == 'Search') {
                            $scope.SearchCustomPallet = {};
                        } else {
                            $scope.ToCustomPallet = {};
                        }
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {          
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.AddSerialToBin = function (SerialNumber,CustomPallet,type) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/consolidation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=AddSerialToContainer&SerialNumber='+SerialNumber+'&CustomPalletID='+CustomPallet.idPallet,
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        if(type == 'From') {                            
                            $scope.GetCustomPalletDetails('From',CustomPallet.idPallet);
                            $scope.SerialNumber = '';
                        } else if(type == 'Search') {
                            $scope.GetCustomPalletDetails('Search',CustomPallet.idPallet);
                            $scope.SearchSerialNumber = '';
                        } else {
                            $scope.GetCustomPalletDetails('To',CustomPallet.idPallet);
                        }    
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );               
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                        
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {          
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.ConsolidateBIN = function (ev) {
            if($scope.FromCustomPallet.idPallet && $scope.ToCustomPallet.idPallet) {
                
                if($scope.FromCustomPallet.idPallet == $scope.ToCustomPallet.idPallet) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content("From and To Containers can't be same")
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {
                    
                    var confirm = $mdDialog.confirm()
                    .title('Are you sure, You want to Consolidate Containers ?')
                    .content('')
                    .ariaLabel('Lucky day')
                    .targetEvent(ev)
                    .ok('Yes')
                    .cancel('Cancel');
                    $mdDialog.show(confirm).then(function() {
                        $rootScope.$broadcast('preloader:active');
                        $scope.busy = true;
                        jQuery.ajax({
                            url: host+'administration/includes/consolidation_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=ConsolidateContainer&FromCustomPalletID='+$scope.FromCustomPallet.idPallet+'&ToCustomPalletID='+$scope.ToCustomPallet.idPallet+'&CloseContainer='+$scope.FromCustomPallet.CloseContainer,
                            success: function(data) {
                                $scope.busy = false;
                                $rootScope.$broadcast('preloader:hide');
                                if(data.Success) {                                
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );               
                                    location.reload();
                                } else {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );                        
                                }                                
                                initSessionTime(); $scope.$apply();
                            }, error : function (data) {     
                                $scope.busy = false;     
                                $rootScope.$broadcast('preloader:hide');     
                                initSessionTime(); $scope.$apply();
                            }
                        });
        
                    }, function() {
                    });
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Select From and To BINs')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        
    });



    angular.module('app').controller("outbound_container_consolidation", function ($scope,$http,$filter,$rootScope,$mdToast,$mdDialog,$stateParams) {
        $scope.FromCustomPallet = {};
        $scope.ToCustomPallet = {};
        $scope.SearchCustomPallet = {};
        $scope.GetCustomPalletDetails = function (type,BinName) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/consolidation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetShipmentContainerDetails&BinName='+BinName,                    
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        if(type == 'From') {
                            $scope.FromCustomPallet = data.Result;
                        } else if(type == 'Search') {
                            $scope.SearchCustomPallet = data.Result;
                        }else {
                            $scope.ToCustomPallet = data.Result;
                        }                        
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        if(type == 'From') {
                            $scope.FromCustomPallet = {};
                        } else if(type == 'Search') {
                            $scope.SearchCustomPallet = {};
                        } else {
                            $scope.ToCustomPallet = {};
                        }
                    }                        
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {          
                    $rootScope.$broadcast('preloader:hide');     
                    initSessionTime(); $scope.$apply();
                }
            });
        };       

        $scope.ConsolidateBIN = function (ev) {
            if($scope.FromCustomPallet.ShippingContainerID && $scope.ToCustomPallet.ShippingContainerID) {
                
                if($scope.FromCustomPallet.ShippingContainerID == $scope.ToCustomPallet.ShippingContainerID) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content("From and To Containers can't be same")
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else if($scope.FromCustomPallet.disposition_id != $scope.ToCustomPallet.disposition_id) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content("Removal Types of From and To Containers should be same")
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else if($scope.FromCustomPallet.AssetsCount == 0) {
                    $mdToast.show (
                        $mdToast.simple()
                            .content("No items available in From Container")
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );

                }else {
                    
                    var confirm = $mdDialog.confirm()
                    .title('Are you sure, You want to Consolidate Containers ?')
                    .content('')
                    .ariaLabel('Lucky day')
                    .targetEvent(ev)
                    .ok('Yes')
                    .cancel('Cancel');
                    $mdDialog.show(confirm).then(function() {
                        $rootScope.$broadcast('preloader:active');
                        $scope.busy = true;
                        jQuery.ajax({
                            url: host+'administration/includes/consolidation_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=ConsolidateShipmentContainer&FromCustomPalletID='+$scope.FromCustomPallet.ShippingContainerID+'&ToCustomPalletID='+$scope.ToCustomPallet.ShippingContainerID,
                            success: function(data) {
                                $scope.busy = false;
                                $rootScope.$broadcast('preloader:hide');
                                if(data.Success) {                                
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );               
                                    location.reload();
                                } else {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );                        
                                }                                
                                initSessionTime(); $scope.$apply();
                            }, error : function (data) {     
                                $scope.busy = false;     
                                $rootScope.$broadcast('preloader:hide');     
                                initSessionTime(); $scope.$apply();
                            }
                        });
        
                    }, function() {
                    });
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Select From and To Containers')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        
    });


})(); 