<?php
session_start();
$data = $_SESSION['workflowsearchreport_data'];
include_once("../../config.php");
include_once("../../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect1();
$filname = 'WorkflowSearchReport.csv';
header('Content-Type: text/csv; charset=utf-8');
//header('Content-Disposition: attachment; filename=MaterialStatus.csv');
header("Content-Type: application/force-download");
header("Content-Type: application/octet-stream");
header("Content-Type: application/download");

// disposition / encoding on response body
header("Content-Disposition: attachment;filename={$filename}");
header("Content-Transfer-Encoding: binary");

// Query formation
$CurrentDate = date('Y-m-d');
if($data['CRDatefrom'] != '')
{
	$chkdt = $data['CRDatefrom'];
	$chkdtarr=explode("GMT",$chkdt);
	$newdt= strtotime($chkdtarr[0]);
	$data['CRDatefrom'] = date("Y-m-d H:i",$newdt);
}
if($data['CRDateto'] != '')
{
	$chkdt = $data['CRDateto'];
	$chkdtarr=explode("GMT",$chkdt);
	$newdt= strtotime($chkdtarr[0]);
	$data['CRDateto'] = date("Y-m-d H:i",$newdt);
}
if($data['WCDatefrom'] != '')
{
	$chkdt = $data['WCDatefrom'];
	$chkdtarr=explode("GMT",$chkdt);
	$newdt= strtotime($chkdtarr[0]);
	$data['WCDatefrom'] = date("Y-m-d H:i",$newdt);
}
if($data['WCDateto'] != '')
{
	$chkdt = $data['WCDateto'];
	$chkdtarr=explode("GMT",$chkdt);
	$newdt= strtotime($chkdtarr[0]);
	$data['WCDateto'] = date("Y-m-d H:i",$newdt);
}
if($data['CRDateto'] == '')
{
	//$data['ARDateto1'] = date('Y-m-d H:i', strtotime($data['ARDatefrom'] . ' + 1 day'));
	$data['CRDateto1'] = date('Y-m-d',strtotime($CurrentDate .' + 1 day'));
}
else
{
	$data['CRDateto1'] = date('Y-m-d H:i', strtotime($data['CRDateto'] . ' + 1 day'));
}
if($data['WCDateto'] == '')
{
	//$data['ASDateto1'] = date('Y-m-d H:i', strtotime($data['ASDatefrom'] . ' + 1 day'));
	$data['WCDateto1'] = date('Y-m-d',strtotime($CurrentDate .' + 1 day'));
}
else
{
	$data['WCDateto1'] = date('Y-m-d H:i', strtotime($data['WCDateto'] . ' + 1 day'));
}


//prepare select items for query
$SELECT = "";
$SELECT1 = "";
/*if($data['assetsnstatus'] == 'true')
{
	$SELECT = $SELECT.", s.StatusName as `SN Status`";
}*/
if($data['assetbintype'] == 'true')
{
	$SELECT = $SELECT.", CP.BinType as `Bin Type`";
	$SELECT1 = $SELECT1.", CP.BinType as `Bin Type`";
}
if($data['assetbinid'] == 'true')
{
	$SELECT = $SELECT.", CP.BinName as `Bin ID`";
	$SELECT1 = $SELECT1.", CP.BinName as `Bin ID`";
}
if($data['assetlocationid'] == 'true')
{
	$SELECT = $SELECT.", CP.BinName as `Bin ID`";
	$SELECT1 = $SELECT1.", CP.BinName as `Bin ID`";
}
if($data['assetsourcecus'] == 'true')
{
	$SELECT = $SELECT.", SC.CustomerName as `Source`";
	$SELECT1 = $SELECT1.", SC.CustomerName as `Source`";
}
if($data['assetsourcetype'] == 'true')
{
	$SELECT = $SELECT.", SCT.Cumstomertype as `Source Type`";
	$SELECT1 = $SELECT1.", SCT.Cumstomertype as `Source Type`";
}
/*if($data['assetparttype'] == 'true')
{
	$sql1 = $sql1.", pt.parttype as `Part Type`";
}*/
if($data['assetTicketid'] == 'true')
{
	$SELECT = $SELECT.", L.LoadId as `Inbound Ticket ID`";
	$SELECT1 = $SELECT1.", L.LoadId as `Inbound Ticket ID`";
}
if($data['assetcid'] == 'true')
{
	$SELECT = $SELECT.", P.idPallet as `Inbound Container ID`";
	$SELECT1 = $SELECT1.", P.idPallet as `Inbound Container ID`";
}
/*if($data['assetinboundremovalcode'] == 'true')
{
	$sql1 = $sql1.", P.idPallet as `Inbound Container ID`";
}*/
if($data['assetouboundticket'] == 'true')
{
	$SELECT = $SELECT.", SCO.ShippingID as `Outbound Ticket ID`";
	$SELECT1 = $SELECT1.", SCO.ShippingID as `Outbound Ticket ID`";
}
if($data['assetouboundcontainer'] == 'true')
{
	$SELECT = $SELECT.", SCO.ShippingContainerID as `Outbound Container ID`";
	$SELECT1 = $SELECT1.", SCO.ShippingContainerID as `Outbound Container ID`";
}
if($data['assetouboundRemovalType'] == 'true')
{
	$SELECT = $SELECT.", DI.disposition as `Outbound Removal Type`";
	$SELECT1 = $SELECT1.", DI.disposition as `Outbound Removal Type`";
}
if($data['assetobcstatus'] == 'true')
{
	$SELECT = $SELECT.", ShS.Status as `Outbound Container Status`";
	$SELECT1 = $SELECT1.", ShS.Status as `Outbound Container Status`";
}
if($data['assetmpn'] == 'true')
{
	$SELECT = $SELECT.", A.UniversalModelNumber as `MPN`";
	$SELECT1 = $SELECT1.", A.MediaMPN as `MPN`";
}
if($data['assetdisposition'] == 'true')
{
	$SELECT = $SELECT.", DI.disposition as `Disposition`";
	$SELECT1 = $SELECT1.", DI.disposition as `Disposition`";
}
if($data['assetfacility'] == 'true')
{
	$SELECT = $SELECT.", F.FacilityName as `Facility`";
	$SELECT1 = $SELECT1.", F.FacilityName as `Facility`";
}
/*if($data['assetcreateddate'] == 'true')
{
	$sql1 = $sql1.", A.CommittedDate as `Created Date`";
}*/
if($data['assetcreatedby'] == 'true')
{
	$SELECT = $SELECT.", U.FirstName as `CreatedBy FirstName`, U.LastName as `CreatedBy LastName`";
	$SELECT1 = $SELECT1.", U.FirstName as `CreatedBy FirstName`, U.LastName as `CreatedBy LastName`";
}
if($data['assetlastdate'] == 'true')
{
	$SELECT = $SELECT.", A.DateUpdated as `Last Touch Date`";
	$SELECT1 = $SELECT1.", A.UpdatedDate as `Last Touch Date`";
}
if($data['assetlasttouchby'] == 'true')
{
	$SELECT = $SELECT.", UM.FirstName as `LastTouchBy FirstName`, UM.LastName as `LastTouchBy LastName`";
	$SELECT1 = $SELECT1.", UM.FirstName as `LastTouchBy FirstName`, UM.LastName as `LastTouchBy LastName`";
}
if($data['assetshippingdate'] == 'true')
{
	$SELECT = $SELECT.", SH.ShippedDate as `Shipping Date`";
	$SELECT1 = $SELECT1.", SH.ShippedDate as `Shipping Date`";
}
if($data['assetshippingby'] == 'true')
{
	$SELECT = $SELECT.", su.FirstName as `ShippedBy FirstName`, su.LastName as `ShippedBy LastName`";
	$SELECT1 = $SELECT1.", su.FirstName as `ShippedBy FirstName`, su.LastName as `ShippedBy LastName`";
}
if($data['assetouboundDestination'] == 'true')
{
	$SELECT = $SELECT.", SHV.VendorName as `Outbound Destination`";
	$SELECT1 = $SELECT1.", SHV.VendorName as `Outbound Destination`";
}
if($data['assetouboundFacility'] == 'true')
{
	$SELECT = $SELECT.", SHF.FacilityName as `Outbound Facility`";
	$SELECT1 = $SELECT1.", SHF.FacilityName as `Outbound Facility`";
}
if($data['assetouboundSource'] == 'true')
{
	$SELECT = $SELECT.", SC.CustomerName as `Outbound Source`";
	$SELECT1 = $SELECT1.", SC.CustomerName as `Outbound Source`";
}
if($data['assetouboundContainerType'] == 'true')
{
	$SELECT = $SELECT.", SHP.packageName as `Outbound Container Type`";
	$SELECT1 = $SELECT1.", SHP.packageName as `Outbound Container Type`";
}
if($data['assetouboundMPN'] == 'true')
{
	$SELECT = $SELECT.", SCS.UniversalModelNumber as `Outbound MPN`";
	$SELECT1 = $SELECT1.", SCS.UniversalModelNumber as `Outbound MPN`";
}
if($data['WorkflowInput'] == 'true')
{
	$SELECT = $SELECT.", bwi.input as `Input`";
	$SELECT1 = $SELECT1.", bwi.input as `Input`";
}
if($data['WorkflowInputType'] == 'true')
{
	$SELECT = $SELECT.", bwi.input_type as `Input Type`";
	$SELECT1 = $SELECT1.", bwi.input_type as `Input Type`";
}

// prepare where condition for query
$WHERE = "";
$WHERE12 = "";
if($data['LoadID'] != '')
{
	$WHERE = $WHERE." AND L.LoadId = '".mysqli_real_escape_string($connectionlink,$data['LoadID'])."'";
	$WHERE12 = $WHERE12." AND L.LoadId = '".mysqli_real_escape_string($connectionlink,$data['LoadID'])."'";
}

if($data['idCustomer'] != '')
{
	$idCustomer = '';
	for($i=0;$i<count($data['idCustomer']);$i++) {
		$idCustomer = $idCustomer.$data['idCustomer'][$i].',';
	}
	$idCustomer = rtrim($idCustomer, ",");
	$WHERE = $WHERE." AND SC.CustomerID IN ( ".mysqli_real_escape_string($connectionlink,$idCustomer).")";
	$WHERE12 = $WHERE12." AND SC.CustomerID IN ( ".mysqli_real_escape_string($connectionlink,$idCustomer).")";
}

if($data['FacilityID'] != '')
{
	$FacilityID = '';
	for($i=0;$i<count($data['FacilityID']);$i++) {
		$FacilityID = $FacilityID.$data['FacilityID'][$i].',';
	}
	$FacilityID = rtrim($FacilityID, ",");
	$WHERE = $WHERE." AND A.FacilityID IN ( ".mysqli_real_escape_string($connectionlink,$FacilityID).")";
	$WHERE12 = $WHERE12." AND A.FacilityID IN ( ".mysqli_real_escape_string($connectionlink,$FacilityID).")";
}

if($data['ContainerID'] != '')
{
	$WHERE = $WHERE." AND P.idPallet = '".mysqli_real_escape_string($connectionlink,$data['ContainerID'])."'";
	$WHERE12 = $WHERE12." AND P.idPallet = '".mysqli_real_escape_string($connectionlink,$data['ContainerID'])."'";
}
if($data['ShippingID'] != '')
{
	$WHERE = $WHERE." AND SH.ShippingID = '".mysqli_real_escape_string($connectionlink,$data['ShippingID'])."'";
	$WHERE12 = $WHERE12." AND SH.ShippingID = '".mysqli_real_escape_string($connectionlink,$data['ShippingID'])."'";
}

if($data['OBContainerID'] != '')
{
	$WHERE = $WHERE." AND SCO.ShippingContainerID = '".mysqli_real_escape_string($connectionlink,$data['OBContainerID'])."'";
	$WHERE12 = $WHERE12." AND SCO.ShippingContainerID = '".mysqli_real_escape_string($connectionlink,$data['OBContainerID'])."'";
}

if($data['locationid'] != '')
{
	$WHERE = $WHERE." AND LOC.LocationID = '".mysqli_real_escape_string($connectionlink,$data['locationid'])."'";
	$WHERE12 = $WHERE12." AND LOC.LocationID = '".mysqli_real_escape_string($connectionlink,$data['locationid'])."'";
}

if($data['CRDatefrom'] != '')
{
	$WHERE = $WHERE." AND P.ReceivedDate between '".mysqli_real_escape_string($connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['CRDateto1'])."'";
	$WHERE12 = $WHERE12." AND P.ReceivedDate between '".mysqli_real_escape_string($connectionlink,$data['CRDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['CRDateto1'])."'";
}

if($data['Other'] == 'Transfer Assets')
{
	$WHERE = $WHERE." AND L.SourceFacilityID != ''";
	$WHERE12 = $WHERE12." AND L.SourceFacilityID != ''";
}

if($data['Other'] == 'Re-Receive Parts')
{
	$WHERE = $WHERE." AND P.FacilityTransferContainer = '1' AND P.ASNContainer = '0'";
	$WHERE12 = $WHERE12." AND P.FacilityTransferContainer = '1' AND P.ASNContainer = '0'";
}

if($data['SerialNumber'] != ''){
	// Split the comma-separated values into an array
	$SerialsArray = explode(' ', $data['SerialNumber']);
	// Escape each value and wrap it in quotes to prevent SQL injection
	$SerialValues = array_map(function($value) {
			return "'" . trim($value) . "'";
	}, $SerialsArray);
	// Join the array back into a string suitable for the IN clause
	$SerialNumbers = implode(',', $SerialValues);
	$WHERE = $WHERE." AND A.SerialNumber IN ( ".$SerialNumbers.")";
	$WHERE12 = $WHERE12." AND A.MediaSerialNumber IN ( ".$SerialNumbers.")";
}

if($data['UniversalModelNumber'] != ''){
	// Split the comma-separated values into an array
	$MPNArray = explode(' ', $data['UniversalModelNumber']);
	// Escape each value and wrap it in quotes to prevent SQL injection
	$MPNValues = array_map(function($value) {
			return "'" . trim($value) . "'";
	}, $MPNArray);
	// Join the array back into a string suitable for the IN clause
	$MPNs = implode(',', $MPNValues);
	$WHERE = $WHERE." AND A.UniversalModelNumber IN ( ".$MPNs.")";
	$WHERE12 = $WHERE12." AND A.MediaMPN IN ( ".$MPNs.")";
}

if($data['DispositionID'] != '')
{
	$dispositionID = '';
	for($i=0;$i<count($data['DispositionID']);$i++) {
		$dispositionID = $dispositionID.$data['DispositionID'][$i].',';
	}
	$dispositionID = rtrim($dispositionID, ",");
	$WHERE = $WHERE." AND A.disposition_id IN ( ".mysqli_real_escape_string($connectionlink,$dispositionID).")";
	$WHERE12 = $WHERE12." AND A.disposition_id IN ( ".mysqli_real_escape_string($connectionlink,$dispositionID).")";
}


if($data['binid'] != ''){
	// Split the comma-separated values into an array
	$BinArray = explode(' ', $data['binid']);
	// Escape each value and wrap it in quotes to prevent SQL injection
	$BinValues = array_map(function($value) {
			return "'" . trim($value) . "'";
	}, $BinArray);
	// Join the array back into a string suitable for the IN clause
	$BinIDs = implode(',', $BinValues);
	$WHERE = $WHERE." AND CP.BinName IN ( ".$BinIDs.")";
	$WHERE12 = $WHERE12." AND CP.BinName IN ( ".$BinIDs.")";
}

if($data['CustomerType'] != '')
{
	$CustomerType = '';
	for($i=0;$i<count($data['CustomerType']);$i++) {
		$CustomerType = $CustomerType.$data['CustomerType'][$i].',';
	}
	$CustomerType = rtrim($CustomerType, ",");
	$WHERE = $WHERE." AND SCT.idCustomertype IN ( ".mysqli_real_escape_string($connectionlink,$CustomerType).")";
	$WHERE12 = $WHERE12." AND SCT.idCustomertype IN ( ".mysqli_real_escape_string($connectionlink,$CustomerType).")";
}

if($data['Parttype'] != '')
{
	$Parttype = '';
	for($i=0;$i<count($data['Parttype']);$i++) {
		$Parttype = $Parttype."'".$data['Parttype'][$i]."',";
	}
	$Parttype = rtrim($Parttype, ",");
	//$Parttype = str_replace($Parttype, '\'');
	//$Parttype = str_replace('\'',"'",$Parttype);
	$Parttype = str_replace( array( '\'', '"' , ';', '<', '>' ), "'", $Parttype);
	$WHERE = $WHERE." AND A.part_type IN ( ".$Parttype.")";
	$WHERE12 = $WHERE12." AND A.MediaType IN ( ".$Parttype.")";
}

if($data['Input'] != '')
{
	$WHERE = $WHERE." AND bwi.input = '".mysqli_real_escape_string($connectionlink,$data['Input'])."'";
	$WHERE12 = $WHERE12." AND bwi.input = '".mysqli_real_escape_string($connectionlink,$data['Input'])."'";
}

if($data['InputType'] != '')
{
	$WHERE = $WHERE." AND bwi.input_type = '".mysqli_real_escape_string($connectionlink,$data['InputType'])."'";
	$WHERE12 = $WHERE12." AND bwi.input_type = '".mysqli_real_escape_string($connectionlink,$data['InputType'])."'";
}
if($data['Other'] == 'Transfer Assets')
	$WHERE = $WHERE." AND L.SourceFacilityID != ''";

if($data['Other'] == 'Re-Receive Parts')
	$WHERE = $WHERE." AND P.FacilityTransferContainer = '1' AND P.ASNContainer = '0'";

for($i=0;$i<count($data['Workflow']);$i++) {
	//$workflow_id = $workflow_id.$data['Workflow'][$i].',';
	if($data['Workflow'][$i] == 3) { // Sanitization workflow
	// get data from Rack records
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Sanitization' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From asset_sanitization AA
				LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = AA.sanitization_rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = AA.sanitization_input_id
				where 1";
				if($data['WCDatefrom'] != '')
					$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

				if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
					$sql10 = $RackRecoverySQL;
				} else {
					$sql10 = $RackRecoverySQL;
				}

		}elseif($data['Workflow'][$i] == 2){ // Failure Analysis workflow
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'FALabTesting' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From asset_failure_analysis AA
				LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = AA.fa_rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = AA.fa_input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";

			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

				if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
					$sql20 = $RackRecoverySQL;
				} else {
					$sql20 = $RackRecoverySQL;
				}

		}
		elseif($data['Workflow'][$i] == 4){
			// get data from RMA
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'RMAInvestigation' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From asset_rma_investigation AA
				LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = AA.rma_rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = AA.rma_input_id
				where 1";

			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";

			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql21 = $RackRecoverySQL;
			} else {
				$sql21 = $RackRecoverySQL;
			}
		}
		elseif($data['Workflow'][$i] == 5){
			// get data from Harvest
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Harvest' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From asset_harvest AA
				LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = AA.harvest_rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = AA.harvest_input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql6 = $RackRecoverySQL;
			} else {
				$sql6 = $RackRecoverySQL;
			}
		}
		elseif($data['Workflow'][$i] == 7){
			// get data from Repair
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Repair' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From asset_repair AA
				LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = AA.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = AA.ToCustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = AA.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = AA.repair_rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = AA.repair_input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql65 = $RackRecoverySQL;
			} else {
				$sql65 = $RackRecoverySQL;
			}
		}
		elseif($data['Workflow'][$i] == 1){
			// get data from Rack records
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Receive' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", A.DateCreated as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From asset A 
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND A.DateCreated between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql66 = $RackRecoverySQL;
			} else {
				$sql66 = $RackRecoverySQL;
			}
		}
		elseif($data['Workflow'][$i] == 6){
			// get data from Rack records
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Removal' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AA.CreatedDate as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From shipping_container_serials AA 
				LEFT JOIN asset A ON A.AssetScanID = AA.AssetScanID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.FirstReceivedCustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND AA.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql67 = $RackRecoverySQL;
			} else {
				$sql67 = $RackRecoverySQL;
			}
		}
		elseif($data['Workflow'][$i] == 10){//Parts Recovery
			// get data from Rack records
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Parts Recovery' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", A.DateCreated as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From asset A 
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND A.DateCreated between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

			$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
			$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
			$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
			$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
			$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
			
			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql107 = $RackRecoverySQL;
				$PartTypeWidgetSql107 = $RackRecoveryPartTypeCountQuery;
				$MPNWidgetSql107 = $RackRecoveryListOfMpnsQuery;
				$EvaluationResultWidgetSql107 = $RackRecoveryEvaluationResultQuery;
				$DispositionWidgetSql107 = $RackRecoveryDispositionQuery;
				$SourcetypeWidgetSql107 = $RackRecoverySourcetypeQuery;
			} else {
				$sql107 = $RackRecoverySQL;
				$PartTypeWidgetSql107 = $RackRecoveryPartTypeCountQuery;
				$MPNWidgetSql107 = $RackRecoveryListOfMpnsQuery;
				$EvaluationResultWidgetSql107 = $RackRecoveryEvaluationResultQuery;
				$DispositionWidgetSql107 = $RackRecoveryDispositionQuery;
				$SourcetypeWidgetSql107 = $RackRecoverySourcetypeQuery;
			}

			$RackRecoverySQL1 = "select A.MediaSerialNumber as `SerialNumber`, A.MediaType as `Part Type`, A.CreatedDate as `Created Date`, 'Parts Recovery' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL1 = $RackRecoverySQL1.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL1 = $RackRecoverySQL1.", A.CreatedDate as `Transaction Date`";
			}
			$RackRecoverySQL1 = $RackRecoverySQL1.$SELECT1;
			$RackRecoveryMainBodyQuery1 = " From speed_media_recovery A 
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.MediaID = A.MediaID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND A.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL1 = $RackRecoverySQL1.$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By A.MediaSerialNumber";
			//echo $RackRecoverySQL1;exit;

			$RackRecoveryPartTypeCountQuery1 = "select count(*) as count,A.MediaType".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By A.MediaType";
			$RackRecoveryListOfMpnsQuery1 = "select count(*) as count,A.MediaMPN".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By A.MediaMPN";
			$RackRecoveryEvaluationResultQuery1 = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By BR.input_id";
			$RackRecoveryDispositionQuery1 = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By A.disposition_id";
			$RackRecoverySourcetypeQuery1 = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By SC.CustomerType";
			
			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql1071 = $RackRecoverySQL1;
				$PartTypeWidgetSql1071 = $RackRecoveryPartTypeCountQuery1;
				$MPNWidgetSql1071 = $RackRecoveryListOfMpnsQuery1;
				$EvaluationResultWidgetSql1071 = $RackRecoveryEvaluationResultQuery1;
				$DispositionWidgetSql1071 = $RackRecoveryDispositionQuery1;
				$SourcetypeWidgetSql1071 = $RackRecoverySourcetypeQuery1;
			} else {
				$sql1071 = $RackRecoverySQL1;
				$PartTypeWidgetSql1071 = $RackRecoveryPartTypeCountQuery1;
				$MPNWidgetSql1071 = $RackRecoveryListOfMpnsQuery1;
				$EvaluationResultWidgetSql1071 = $RackRecoveryEvaluationResultQuery1;
				$DispositionWidgetSql1071 = $RackRecoveryDispositionQuery1;
				$SourcetypeWidgetSql1071 = $RackRecoverySourcetypeQuery1;
			}
		}
		elseif($data['Workflow'][$i] == 20){//Parts Sort
			// get data from Rack records
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Parts Recovery' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", A.DateCreated as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From parts_sort_history PSH
				LEFT JOIN asset A ON A.AssetScanID = PSH.AssetScanID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = SC.CustomerType
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND A.DateCreated between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

			$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
			$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
			$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
			$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
			$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
			
			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql207 = $RackRecoverySQL;
				$PartTypeWidgetSql207 = $RackRecoveryPartTypeCountQuery;
				$MPNWidgetSql207 = $RackRecoveryListOfMpnsQuery;
				$EvaluationResultWidgetSql207 = $RackRecoveryEvaluationResultQuery;
				$DispositionWidgetSql207 = $RackRecoveryDispositionQuery;
				$SourcetypeWidgetSql207 = $RackRecoverySourcetypeQuery;
			} else {
				$sql207 = $RackRecoverySQL;
				$PartTypeWidgetSql207 = $RackRecoveryPartTypeCountQuery;
				$MPNWidgetSql207 = $RackRecoveryListOfMpnsQuery;
				$EvaluationResultWidgetSql207 = $RackRecoveryEvaluationResultQuery;
				$DispositionWidgetSql207 = $RackRecoveryDispositionQuery;
				$SourcetypeWidgetSql207 = $RackRecoverySourcetypeQuery;
			}
		}
		elseif($data['Workflow'][$i] == 21){//Parts Destruction
			// get data from Rack records
			
			$RackRecoverySQL = "select A.SerialNumber as `SerialNumber`, A.part_type as `Part Type`, A.DateCreated as `Created Date`, 'Parts Recovery' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL = $RackRecoverySQL.", A.DateCreated as `Transaction Date`";
			}
			$RackRecoverySQL = $RackRecoverySQL.$SELECT;
			$RackRecoveryMainBodyQuery = " From destruction_history PSH
				LEFT JOIN asset A ON A.AssetScanID = PSH.AssetScanID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.AssetScanID = A.AssetScanID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND PSH.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL = $RackRecoverySQL.$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.SerialNumber";
			//echo $RackRecoverySQL;exit;

			$RackRecoveryPartTypeCountQuery = "select count(*) as count,A.part_type".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.part_type";
			$RackRecoveryListOfMpnsQuery = "select count(*) as count,A.UniversalModelNumber".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.UniversalModelNumber";
			$RackRecoveryEvaluationResultQuery = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By BR.input_id";
			$RackRecoveryDispositionQuery = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By A.disposition_id";
			$RackRecoverySourcetypeQuery = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery.$WHERE.$WHERE1." Group By SC.CustomerType";
			
			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql217 = $RackRecoverySQL;
				$PartTypeWidgetSql217 = $RackRecoveryPartTypeCountQuery;
				$MPNWidgetSql217 = $RackRecoveryListOfMpnsQuery;
				$EvaluationResultWidgetSql217 = $RackRecoveryEvaluationResultQuery;
				$DispositionWidgetSql217 = $RackRecoveryDispositionQuery;
				$SourcetypeWidgetSql217 = $RackRecoverySourcetypeQuery;
			} else {
				$sql217 = $RackRecoverySQL;
				$PartTypeWidgetSql217 = $RackRecoveryPartTypeCountQuery;
				$MPNWidgetSql217 = $RackRecoveryListOfMpnsQuery;
				$EvaluationResultWidgetSql217 = $RackRecoveryEvaluationResultQuery;
				$DispositionWidgetSql217 = $RackRecoveryDispositionQuery;
				$SourcetypeWidgetSql217 = $RackRecoverySourcetypeQuery;
			}

			$RackRecoverySQL1 = "select A.MediaSerialNumber as `SerialNumber`, A.MediaType as `Part Type`, A.CreatedDate as `Created Date`, 'Parts Recovery' as Workflow";
			if($data['assetTransactor'] == 'true')
			{
				$RackRecoverySQL1 = $RackRecoverySQL1.", AAU.UserName as `Transaction By`";
			}
			if($data['assetTransactorDate'] == 'true')
			{
				$RackRecoverySQL1 = $RackRecoverySQL1.", A.CreatedDate as `Transaction Date`";
			}
			$RackRecoverySQL1 = $RackRecoverySQL1.$SELECT1;
			$RackRecoveryMainBodyQuery1 = " From destruction_history PSH 
				LEFT JOIN speed_media_recovery A ON A.MediaID = PSH.MediaID
				LEFT JOIN users U ON U.UserId = A.CreatedBy
				LEFT JOIN users AAU ON AAU.UserId = A.CreatedBy
				LEFT JOIN pallets P ON P.idPallet = A.idPallet
				LEFT JOIN facility F ON F.FacilityID = A.FacilityID
				LEFT JOIN loads L ON L.LoadId = P.LoadId
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer
				LEFT JOIN customertype SCT ON SCT.idCustomertype = P.idCustomertype
				LEFT JOIN custompallet CP ON CP.CustomPalletID = A.CustomPalletID
				LEFT JOIN disposition DI ON DI.disposition_id = A.disposition_id
				LEFT JOIN shipping_container_serials SCS ON SCS.MediaID = A.MediaID
				LEFT JOIN shipping_containers SCO ON SCO.ShippingContainerID = SCS.ShippingContainerID
				LEFT JOIN shipping SH ON SH.ShippingID = SCO.ShippingID
				LEFT JOIN vendor SHV ON SHV.VendorID = SH.VendorID
				LEFT JOIN facility SHF ON SHF.FacilityID = SH.FacilityID
				LEFT JOIN disposition SHDI ON SHDI.disposition_id = SH.disposition_id
				LEFT JOIN package SHP ON SHP.idPackage = SCO.idPackage
				LEFT JOIN users UM ON UM.UserId = A.UpdatedBy
				LEFT JOIN location LOC ON LOC.LocationID = CP.LocationID
				LEFT JOIN shipping_status ShS ON SCO.StatusID = ShS.ShipmentStatusID
				LEFT JOIN users su ON su.UserId = SH.UpdatedBy
				LEFT JOIN business_rule BR ON BR.rule_id = A.rule_id
				LEFT JOIN workflow wi ON wi.workflow_id = BR.workflow_id
				LEFT JOIN workflow_input bwi ON bwi.input_id = A.input_id
				where 1";
			if($data['WCDatefrom'] != '')
				$WHERE1 = " AND PSH.CreatedDate between '".mysqli_real_escape_string($connectionlink,$data['WCDatefrom'])."' and '".mysqli_real_escape_string($connectionlink,$data['WCDateto1'])."'";
			$RackRecoverySQL1 = $RackRecoverySQL1.$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By A.MediaSerialNumber";
			//echo $RackRecoverySQL;exit;

			$RackRecoveryPartTypeCountQuery1 = "select count(*) as count,A.MediaType".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By A.MediaType";
			$RackRecoveryListOfMpnsQuery1 = "select count(*) as count,A.MediaMPN".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By A.MediaMPN";
			$RackRecoveryEvaluationResultQuery1 = "select count(*) as count,bwi.input as EvaluationResultID,bwi.input as input".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By BR.input_id";
			$RackRecoveryDispositionQuery1 = "select count(*) as count,A.disposition_id,DI.disposition".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By A.disposition_id";
			$RackRecoverySourcetypeQuery1 = "select count(*) as count,SC.CustomerType,SCT.Cumstomertype".$RackRecoveryMainBodyQuery1.$WHERE12.$WHERE1." Group By SC.CustomerType";
			
			if($data['SerialNumber'] != '' || $data['UniversalModelNumber'] != ''){
				$sql2171 = $RackRecoverySQL1;
				$PartTypeWidgetSql2171 = $RackRecoveryPartTypeCountQuery1;
				$MPNWidgetSql2171 = $RackRecoveryListOfMpnsQuery1;
				$EvaluationResultWidgetSql2171 = $RackRecoveryEvaluationResultQuery1;
				$DispositionWidgetSql2171 = $RackRecoveryDispositionQuery1;
				$SourcetypeWidgetSql2171 = $RackRecoverySourcetypeQuery1;
			} else {
				$sql2171 = $RackRecoverySQL1;
				$PartTypeWidgetSql2171 = $RackRecoveryPartTypeCountQuery1;
				$MPNWidgetSql2171 = $RackRecoveryListOfMpnsQuery1;
				$EvaluationResultWidgetSql2171 = $RackRecoveryEvaluationResultQuery1;
				$DispositionWidgetSql2171 = $RackRecoveryDispositionQuery1;
				$SourcetypeWidgetSql2171 = $RackRecoverySourcetypeQuery1;
			}
		}

	}
	$sql = "select SQL_CALC_FOUND_ROWS * from (";
	if($sql10 != '')
	{
		$sql = $sql."(".$sql10.") UNION ALL ";
	}
	if($sql20 != '')
	{
		$sql = $sql."(".$sql20.") UNION ALL ";
	}
	if($sql21 != '')
	{
		$sql = $sql."(".$sql21.") UNION ALL ";
	}
	if($sql6 != '')
	{
		$sql = $sql."(".$sql6.") UNION ALL ";
	}
	if($sql65 != '')
	{
		$sql = $sql."(".$sql65.") UNION ALL ";
	}
	if($sql66 != '')
	{
		$sql = $sql."(".$sql66.") UNION ALL ";
	}
	if($sql67 != '')
	{
		$sql = $sql."(".$sql67.") UNION ALL ";
	}
	if($sql107 != '')
	{
		$sql = $sql."(".$sql107.") UNION ALL ";
	}
	if($sql1071 != '')
	{
		$sql = $sql."(".$sql1071.") UNION ALL ";
	}
	if($sql207 != '')
	{
		$sql = $sql."(".$sql207.") UNION ALL ";
	}
	if($sql217 != '')
	{
		$sql = $sql."(".$sql217.") UNION ALL ";
	}
	if($sql2171 != '')
	{
		$sql = $sql."(".$sql2171.") UNION ALL ";
	}
	if($sql10 == '' && $sql20 == '' && $sql21 == '' && $sql6 == '' && $sql65 == '' && $sql66 == '' && $sql67 == '' && $sql107 == '' && $sql207 == '' && $sql1071 == '' && $sql217 == '' && $sql2171 == '')
	{
		$json['Success'] = false;
		$json['Result'] = 'Please Select atleast one Service Workflow.';
		return json_encode($json);
	}
	$sql = substr($sql, 0, -11);
	$sql = $sql.") as result order by `Created Date` DESC";
$sql = $sql." LIMIT 0,1000000";
$query = mysqli_query($connectionlink,$sql);
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=SearchResult.csv');
$output = fopen('php://output', 'w');
$first = true;
while($row = mysqli_fetch_assoc($query))
{
	if($data['assetscanid'] != 'true')
	{
		unset($row['Scan ID']);
	}
	if($data['assetcreateddate'] == 'true')
	{
		$date1 = explode(" ",$row['Created Date']);
		$date2 = explode("-",$date1[0]);
		$date = $date2[1]."/".$date2[2]."/".$date2[0];
		$date = date("M j, Y g:i a", strtotime($row['Created Date']));
		//$time = date("g:i a", strtotime($row['DateCreated']));
		//$row['Created Date'] = $date." ".$time;
		$row['Created Date'] = $date;
		//unset($row['DateCreated']);
	}
	if($data['assetcreatedby'] == 'true')
	{
		$row['Created By'] = $row['CreatedBy FirstName']." ".$row['CreatedBy LastName'];
		$row['Created By'] = strtolower($row['Created By']);
		unset($row['CreatedBy FirstName']);
		unset($row['CreatedBy LastName']);
	}
	if($data['assetlasttouchby'] == 'true')
	{
		$row['Last Touch By'] = $row['LastTouchBy FirstName']." ".$row['LastTouchBy LastName'];
		$row['Last Touch By'] = strtolower($row['Last Touch By']);
		unset($row['LastTouchBy FirstName']);
		unset($row['LastTouchBy LastName']);
	}
	if($data['assetshippingby'] == 'true')
	{
		$row['Shipping By'] = $row['ShippedBy FirstName']." ".$row['ShippedBy LastName'];
		$row['Shipping By'] = strtolower($row['Shipping By']);
		unset($row['ShippedBy FirstName']);
		unset($row['ShippedBy LastName']);
	}
	if ($first) {
        fputcsv($output, array_keys($row));
        $first = false;
    }
	$rows[] = $row;
}

if (count($rows) > 0) {
    foreach ($rows as $row) {
        fputcsv($output, $row);
    }
}
