<?php
session_start();
include_once("../database/Coo.class.php");
$obj = new COOClass();

if($_POST['ajax'] == "COO"){
		$result = $obj->COO($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCOODetails"){
  		$result = $obj->GetCOODetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCOOList"){
  		$result = $obj->GetCOOList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateCOOListxls") {
		$result = $obj->GenerateCOOListxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAllCOOs") {
		$result = $obj->GetAllCOOs($_POST);
		echo $result;
  	}
