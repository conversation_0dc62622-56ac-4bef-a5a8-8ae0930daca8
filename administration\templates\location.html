
<div class="row page" data-ng-controller="location">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Location</span>
                        <div flex></div>
                            <a href="#!/LocationList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i> Back To List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                    <label>Facility</label>   
                                    <md-select name="FacilityID" ng-model="location.FacilityID" required aria-label="select" ng-change="GetLocations()">
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>

                                    </md-select> 
                                    <div class="error-space"> 
                                     <div class="md-errors-spacer" ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>      
                                    </div> 
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Location Name</label>
                                    <input type="text" name="Location"  ng-model="location['Location']"  required ng-maxlength="45" />
                                    <div class="error-space"> 
                                    <div class="md-errors-spacer" ng-messages="material_signup_form.Location.$error" multiple ng-if='material_signup_form.Location.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="Description"  ng-model="location['Description']"  required ng-maxlength="250" />
                                    <div class="error-space"> 
                                    <div class="md-errors-spacer" ng-messages="material_signup_form.Description.$error" multiple ng-if='material_signup_form.Description.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>
                                    </div>

                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="location.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In active </md-option>
                                    </md-select>   
                                    <div class="error-space"> 
                                    <div class="md-errors-spacer" ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>    
                                    </div>                                         
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Lock</label>
                                    <md-select name="Locked" ng-model="location.Locked" required aria-label="select">
                                        <md-option value="2"> Unlocked </md-option>
                                        <md-option value="1"> Locked </md-option>
                                    </md-select>  
                                    <div class="error-space">  
                                    <div class="md-errors-spacer" ng-messages="material_signup_form.Locked.$error" multiple ng-if='material_signup_form.Locked.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>  
                                    </div>                                           
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                    <label>Label</label>   
                                    <md-select name="LableID" ng-model="location.lable_name" required aria-label="select" ng-change="GetLabel()">
                                        <md-option ng-repeat="lable in lables" value="{{lable.id}}"> {{lable.lable_name}} </md-option>
                                    </md-select> 
                                    <div class="error-space"> 
                                    <div class="md-errors-spacer" ng-messages="material_signup_form.LableID.$error" multiple ng-if='material_signup_form.LableID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>  
                                    </div>     
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Location Type</label>
                                    <md-select name="LocationType" ng-model="location.LocationType" required aria-label="select">                                        
                                        <md-option value="Outbound Storage"> Outbound Storage </md-option>
                                        <md-option value="Inbound Storage"> Inbound Storage </md-option>
                                        <md-option value="WIP"> WIP </md-option>
                                        <!-- <md-option value="B2D"> B2D </md-option> -->
                                    </md-select>   
                                    <div class="error-space"> 
                                    <div class="md-errors-spacer" ng-messages="material_signup_form.LocationType.$error" multiple ng-if='material_signup_form.LocationType.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>

                           <!--  <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Process Location</label>
                                    <label class="checkbox-container">
                                    <input type="checkbox" name="ProcessLocation"  ng-model="location['ProcessLocation']"  />
                                    <span class="checkmark"></span>
                                    <div ng-messages="material_signup_form.ProcessLocation.$error" multiple ng-if='material_signup_form.ProcessLocation.$dirty'>                                                      
                                    </div>
                                </label>

                                </md-input-container>
                            </div> -->
                            
                            <div class="col-md-12 btns-row">
                                <a href="#!/LocationList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel  `
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || location.busy" ng-click="saveLocation()">
                                <span ng-show="! location.busy">Save</span>
                                <span ng-show="location.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>