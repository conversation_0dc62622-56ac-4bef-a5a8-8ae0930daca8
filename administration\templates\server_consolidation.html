<div class="page" data-ng-controller="server_consolidation">
    <div class="row ui-section">            
        <div class="col-md-12">
            <article class="article">

                <h5 class="custom_heading"><i class="material-icons mr-5">view_module</i> Server/Switch Consolidation</h5>

                <!--BIN Start-->
                <md-card class="no-margin-h">  
                    <md-toolbar class="md-table-toolbar md-default" ng-init="frombin = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="frombin = !frombin">                            
                            <i class="material-icons md-primary" ng-show="frombin">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! frombin">keyboard_arrow_down</i>
                            <span>From BIN</span>
                        </div>
                    </md-toolbar>
                    <md-card-content class="pt-0" ng-show="frombin">

                        <div class="col-md-12">
                                                            
                                <div class="col-md-6 col-md-offset-3">
                                    <md-input-container class="md-block">
                                        <label>From Bin</label>
                                        <input required name="FromBinName" ng-model="FromBinName" required style="padding-right: 35px;" ng-enter="GetCustomPalletDetails('From',FromBinName)">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="GetCustomPalletDetails('From',FromBinName)" ng-disabled="!FromBinName">
                                            Go
                                        </md-button>
                                    </md-input-container>
                                </div>
                                <div style="clear: both;"></div>
                            
                        </div>

                        <div class="row" ng-show="FromCustomPallet.CustomPalletID">
                            <div class="col-md-12">
                                    <md-card-content class="pt-0">
                                        <div class="table-responsive">
                                            <table class="table mb-0">
                                                <thead>
                                                    <tr class="bg-grey">
                                                        <th>Bin</th>
                                                        <th>Disposition</th>                                                
                                                        <th style="min-width:180px;">Current Count</th>
                                                        <!-- <th style="min-width:220px;">Add Serial</th> -->
                                                        <!-- <th style="min-width:180px; text-align: center;">Close BIN</th> -->
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr >
                                                        <td>
                                                            {{FromCustomPallet.BinName}}
                                                        </td>
                                                        <td>
                                                            <span ng-show="FromCustomPallet.AcceptAllDisposition == '0'">{{FromCustomPallet.disposition}}</span>
                                                            <span ng-show="FromCustomPallet.AcceptAllDisposition == '1'">All Disposition</span>
                                                        </td>
                                                        <td>{{FromCustomPallet.AssetsCount}}</td>
                                                        <!-- <td>
                                                            <md-input-container class="md-block md-no-float includedsearch tdinput" style="margin-top:12px;">
                                                                <input required name="SerialNumber" ng-model="SerialNumber" required placeholder="Serial Number">
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="AddSerialToBin(SerialNumber,FromCustomPallet,'From')" ng-disabled="!SerialNumber">
                                                                    <i class="material-icons" style="margin-top:3px;">
                                                                        add
                                                                    </i>
                                                                </md-button>
                                                            </md-input-container>
                                                        </td> -->
                                                        <!-- <td style="text-align:center;">
                                                            <i class="material-icons text-danger" style="cursor:pointer;" ng-click="CloseBIN(FromCustomPallet,'From',$event)">
                                                                close
                                                            </i>
                                                        </td> -->
                                                    </tr>                                        
                                                </tbody>
                                            </table>
                                        </div>
                                    </md-card-content>        
                            </div>
                        </div>
                    </md-card-content>
                </md-card>
                <!--BIN Close-->

                <!--BIN Start-->
                <md-card class="no-margin-h">  
                    <md-toolbar class="md-table-toolbar md-default" ng-init="tobin = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="tobin = !tobin">                            
                            <i class="material-icons md-primary" ng-show="tobin">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! tobin">keyboard_arrow_down</i>
                            <span>To BIN</span>
                        </div>
                    </md-toolbar>
                    <md-card-content class="pt-0" ng-show="tobin">

                        <div class="col-md-12">
                                                           
                                <div class="col-md-6 col-md-offset-3">
                                    <md-input-container class="md-block">
                                        <label>To Bin</label>
                                        <input required name="ToBinName" ng-model="ToBinName" required style="padding-right: 35px;" ng-enter="GetCustomPalletDetails('To',ToBinName)">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="GetCustomPalletDetails('To',ToBinName)" ng-disabled="!ToBinName">
                                            Go
                                        </md-button>
                                    </md-input-container>
                                </div>
                                <div style="clear: both;"></div>
                            
                        </div>

                        <div class="row" ng-show="ToCustomPallet.CustomPalletID">
                            <div class="col-md-12">
                                
                                    <md-card-content class="pt-0">
                                        <div class="table-responsive">
                                            <table class="table mb-0">
                                                <thead>
                                                    <tr class="bg-grey">
                                                        <th>Bin</th>
                                                        <th>Disposition</th>                                                
                                                        <th style="min-width:180px;">Current Count</th>
                                                        <!-- <th style="min-width:220px;">Add Serial</th> -->
                                                        <!-- <th style="min-width:180px; text-align: center;">Close BIN</th> -->
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr >
                                                        <td>
                                                            {{ToCustomPallet.BinName}}
                                                        </td>
                                                        <td>
                                                            <span ng-show="ToCustomPallet.AcceptAllDisposition == '0'">{{ToCustomPallet.disposition}}</span>
                                                            <span ng-show="ToCustomPallet.AcceptAllDisposition == '1'">All Disposition</span>
                                                        </td>
                                                        <td>{{ToCustomPallet.AssetsCount}}</td>
                                                        <!-- <td>
                                                            <md-input-container class="md-block md-no-float includedsearch tdinput" style="margin-top:12px;">
                                                                <input required name="ToSerialNumber" ng-model="ToSerialNumber" required placeholder="Serial Number">
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="AddSerialToBin(ToSerialNumber,ToCustomPallet,'To')" ng-disabled="!ToSerialNumber">
                                                                    <i class="material-icons" style="margin-top:3px;">
                                                                        add
                                                                    </i>
                                                                </md-button>
                                                            </md-input-container>
                                                        </td> -->
                                                        <!-- <td style="text-align:center;">
                                                            <i class="material-icons text-danger" style="cursor:pointer;" ng-click="CloseBIN(ToCustomPallet,'To',$event)">
                                                                close
                                                            </i>
                                                        </td> -->
                                                    </tr>                                        
                                                </tbody>
                                            </table>
                                        </div>
                                    </md-card-content>       
                            </div>
                        </div>

                    </md-card-content>                    
                </md-card>
                <!--BIN Close-->


                <div class="col-md-12 btns-row">
                    <!-- <a href="#!/catlogcreationlist" style="text-decoration: none;">
                        <md-button class="md-button md-raised btn-w-md  md-default">
                            Cancel
                        </md-button>
                    </a> -->
                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                    data-ng-disabled="!FromCustomPallet.CustomPalletID || !ToCustomPallet.CustomPalletID || busy" ng-click="ConsolidateBIN($event)">
                        <span ng-show="! busy">Consolidate</span>
                        <span ng-show="busy">
                            <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular>
                        </span>
                    </md-button>
                </div>

                <div style="clear:both;"></div>

                <div>                
                    <h5 class="custom_heading"><i class="material-icons mr-5">open_with</i> Move Server/Switch</h5>

                    <!--BIN Start-->
                    <md-card class="no-margin-h">  
                        <md-toolbar class="md-table-toolbar md-default" ng-init="searchbin = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="searchbin = !searchbin">                            
                                <i class="material-icons md-primary" ng-show="searchbin">keyboard_arrow_up</i>
                                <i class="material-icons md-primary" ng-show="! searchbin">keyboard_arrow_down</i>
                                <span>To BIN</span>
                            </div>
                        </md-toolbar>
                        <md-card-content class="pt-0" ng-show="searchbin">

                            <div class="col-md-12">
                                                                
                                    <div class="col-md-6 col-md-offset-3">
                                        <md-input-container class="md-block">
                                            <label>To Bin</label>
                                            <input required name="SearchBinName" ng-model="SearchBinName" required style="padding-right: 35px;" ng-enter="GetCustomPalletDetails('Search',SearchBinName)">
                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="GetCustomPalletDetails('Search',SearchBinName)" ng-disabled="!SearchBinName">
                                                Go
                                            </md-button>
                                        </md-input-container>
                                    </div>
                                    <div style="clear: both;"></div>
                                
                            </div>

                            <div class="row" ng-show="SearchCustomPallet.CustomPalletID">
                                <div class="col-md-12">
                                        <md-card-content class="pt-0">
                                            <div class="table-responsive">
                                                <table class="table mb-0">
                                                    <thead>
                                                        <tr class="bg-grey">
                                                            <th>Bin</th>
                                                            <th>Disposition</th>                                                
                                                            <th style="min-width:180px;">Current Count</th>
                                                            <th style="min-width:220px;">Add Server/Switch</th>
                                                            <!-- <th style="min-width:180px; text-align: center;">Close BIN</th> -->
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr >
                                                            <td>
                                                                {{SearchCustomPallet.BinName}}
                                                            </td>
                                                            <td>
                                                                <span ng-show="SearchCustomPallet.AcceptAllDisposition == '0'">{{SearchCustomPallet.disposition}}</span>
                                                                <span ng-show="SearchCustomPallet.AcceptAllDisposition == '1'">All Disposition</span>
                                                            </td>
                                                            <td>{{SearchCustomPallet.AssetsCount}}</td>
                                                            <td>
                                                                <md-input-container class="md-block md-no-float includedsearch tdinput" style="margin-top:12px;">
                                                                    <input required name="SearchSerialNumber" ng-model="SearchSerialNumber" required placeholder="Server/Switch">
                                                                    <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="AddSerialToBin(SearchSerialNumber,SearchCustomPallet,'Search')" ng-disabled="!SearchSerialNumber">
                                                                        <i class="material-icons" style="margin-top:3px;">
                                                                            add
                                                                        </i>
                                                                    </md-button>
                                                                </md-input-container>
                                                            </td>
                                                            <!-- <td style="text-align:center;">
                                                                <i class="material-icons text-danger" style="cursor:pointer;" ng-click="CloseBIN(FromCustomPallet,'From',$event)">
                                                                    close
                                                                </i>
                                                            </td> -->
                                                        </tr>                                        
                                                    </tbody>
                                                </table>
                                            </div>
                                        </md-card-content>        
                                </div>
                            </div>
                        </md-card-content>
                    </md-card>
                    <!--BIN Close-->
                </div>

            </article>
        </div>
    </div>
</div>