<div class="row page" data-ng-controller="Section">
    <div class="col-md-12">
        <article class="article">
            <form name="material_signup_form" class="form-validation" >
                <md-card class="no-margin-h">
                    
                    <md-toolbar class="md-table-toolbar md-default">
                        <div class="md-toolbar-tools">
                            <span>Section</span>
                            <div flex></div>
                            <a href="#!/SectionList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                        </div>
                    </md-toolbar>
                                    
                    <div class="col-md-4 col-md-offset-4">
                        <fieldset>                        
                            <md-input-container class="md-block">                                          
                                <label>Jabil Site</label>
                                <md-select name="CustomerID" ng-model="benefit.CustomerID" required aria-label="select" ng-disabled="benefit.SectionID" ng-change="GetCustomerApplications()">
                                    <md-option ng-repeat="cus in Customer" value="{{cus.CustomerID}}"> {{cus.CustomerName}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.CustomerID.$error" multiple ng-if='material_signup_form.CustomerID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Application</label>
                                <md-select name="ApplicationID" ng-model="benefit.ApplicationID" required aria-label="select" ng-disabled="benefit.SectionID" ng-change="GetApplicationModules()">
                                    <md-option ng-repeat="app in Applications" value="{{app.ApplicationID}}"> {{app.ApplicationName}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.ApplicationID.$error" multiple ng-if='material_signup_form.ApplicationID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">                                          
                                <label>Module</label>
                                <md-select name="ModuleID" ng-model="benefit.ModuleID" required aria-label="select" ng-disabled="benefit.SectionID" >
                                    <md-option ng-repeat="mod in Modules" value="{{mod.ModuleID}}"> {{mod.ModuleName}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.ModuleID.$error" multiple ng-if='material_signup_form.ModuleID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Section Name</label>
                                <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                <input type="text" name="SectionName"  ng-model="benefit['SectionName']"  required ng-maxlength="100" />
                                <div ng-messages="material_signup_form.ModuleName.$error" multiple ng-if='material_signup_form.SectionName.$dirty'>                            
                                    <div ng-message="required">This is required.</div> 
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 100.</div>                           
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Description</label>
                                <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                <textarea name="SectionDescription" ng-minlength="3"  ng-model="benefit['SectionDescription']"  required ng-maxlength="1000" ></textarea>
                                
                                <div ng-messages="material_signup_form.SectionDescription.$error" multiple ng-if='material_signup_form.SectionDescription.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 1000.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">                                            
                                <label>Status</label>
                                <md-select name="Status" ng-model="benefit.Status" required aria-label="select">
                                    <md-option value="Active"> Active </md-option>
                                    <md-option value="In active"> In active </md-option>
                                </md-select>   
                                <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                    <div ng-message="required">This is required.</div>                                           
                                </div>                                             
                            </md-input-container>
                                                        
                            <div class="col-md-12 btns-row">
                                <md-button 
                                class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || benefit.busy" ng-click="CreateSection('Submit')">
                                    <span ng-show="! benefit.busy">Submit</span>
                                    <span ng-show="benefit.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>
                            
                        </fieldset>
                    </div>
                </md-card>                
            </form>            
        </article>                            
    </div>

</div>