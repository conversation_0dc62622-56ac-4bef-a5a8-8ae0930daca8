
<div class="row page" data-ng-controller="Rig">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Rigs</span>
                        <div flex></div>
                            <a href="#!/RigList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="Rig_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                            <md-input-container class="md-block">
                                    <label>Work Station</label>   
                                    <md-select name="SiteID" ng-model="Rig.SiteID" required aria-label="select" ng-change="GetSite()">
                                        <md-option ng-repeat="siteinformation in site" value="{{siteinformation.SiteID}}"> {{siteinformation.SiteName}} </md-option>
                                    
                                    </md-select>
                                    <div ng-messages="site_form.SiteID.$error" multiple ng-if='site_form.SiteID.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>     
                                </md-input-container>
                            </div>


                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Rigs Name</label>
                                    <input type="text" name="Rigname"  ng-model="Rig['Rig']"  required ng-maxlength="45" />
                                    <div ng-messages="Rig_form.Rigname.$error" multiple ng-if='Rig_form.Rigname.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Rigs Description</label>
                                    <input type="text" name="Description"  ng-model="Rig['Description']"  required ng-maxlength="250" />
                                    <div ng-messages="Rig_form.Description.$error" multiple ng-if='Rig_form.Description.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>

                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Rig Limit</label>
                                    <input type="number" name="RigLimit"  ng-model="Rig['RigLimit']"  required ng-min="1" ng-max="100" />
                                    <div ng-messages="Rig_form.RigLimit.$error" multiple ng-if='Rig_form.RigLimit.$dirty'>                            
                                        <div ng-message="required">This is required.</div>
                                                <div ng-message="min">Minimum 1.</div>
                                                <div ng-message="max">Maximul 100.</div>                         
                                    </div>
                                </md-input-container>
                            </div>
                           
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="Rig.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In active </md-option>
                                    </md-select>   
                                    <div ng-messages="Rig_form.Status.$error" multiple ng-if='Rig_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/RigList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel  `
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="Rig_form.$invalid || Rig.busy" ng-click="SaveRig()">
                                <span ng-show="! Rig.busy">Save</span>
                                <span ng-show="Rig.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>