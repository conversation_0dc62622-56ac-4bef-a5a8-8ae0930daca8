(function () {
    'use strict';
    angular.module('app').controller("ExceptionReason", function ($scope, $location, $http, $rootScope, $mdToast, $stateParams, facilityinformation, UserFacility) {
        $scope.ExceptionReason = {};
        $scope.Facility = {};
        facilityinformation.async().then(function (d) { //2. so you can use .then()
            $scope.Facility = d['data']['Result'];
        });

        UserFacility.async().then(function (d) { //2. so you can use .then()
            $scope.UserFacility = d.data;
            //alert($scope.UserFacility);
            $scope.ExceptionReason.FacilityID = $scope.UserFacility;
        });
        $scope.ExceptionReasonSave = function () {
            $scope.ExceptionReason.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'administration/includes/ExceptionReason_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ExceptionReason&' + $.param($scope.ExceptionReason),
                success: function (data) {
                    /*console.log('sssssss');
                    console.log( data.Result);
                    console.log('sssssss');*/
                    $rootScope.$broadcast('preloader:hide');
                    $scope.ExceptionReason.busy = false;
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        window.location = "#!/ExceptionReasonList";
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.ExceptionReason.busy = false;
                    alert('error');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        if ($stateParams.ExceptionReasonID) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'administration/includes/ExceptionReason_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetExceptionReasonDetails&ExceptionReasonID=' + $stateParams.ExceptionReasonID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    if (data.Success) {
                        $scope.ExceptionReason = data.Result;
                    } else {

                        $scope.ExceptionReason = {};
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }
    });

    angular.module('app').controller("ExceptionReasonList", function ($scope,$location,$http,$rootScope,$mdToast) {
           $scope.busy = false;
           $scope.ExceptionReasonList = [];
           $scope.pagedItems = [];

           //Start Pagination Logic
           $scope.itemsPerPage = 20;
           $scope.currentPage = 0;
           $scope.OrderBy = '';
           $scope.OrderByType = '';
           $scope.filter_text = [{}];
           $scope.range = function() {
               var rangeSize = 10;
               var ret = [];
               var start;
               start = $scope.currentPage;
               if ( start > $scope.pageCount()-rangeSize ) {
                   start = $scope.pageCount()-rangeSize;
               }
               for (var i=start; i<start+rangeSize; i++) {
                   ret.push(i);
               }
               return ret;
           };
           $scope.prevPage = function() {
               if ($scope.currentPage > 0) {
                   $scope.currentPage--;
               }
           };
           $scope.firstPage = function () {
               $scope.currentPage = 0;
           };
           $scope.prevPageDisabled = function() {
               return $scope.currentPage === 0 ? "disabled" : "";
           };
           $scope.nextPage = function() {
               if ($scope.currentPage < $scope.pageCount() - 1) {
                   $scope.currentPage++;
               }
           };
           $scope.lastPage = function() {
               $scope.currentPage =  $scope.pageCount() - 1;
           };
           $scope.nextPageDisabled = function() {
               return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
           };
           $scope.pageCount = function() {
               return Math.ceil($scope.total/$scope.itemsPerPage);
           };
           $scope.setPage = function(n) {
               if (n >= 0 && n < $scope.pageCount()) {
                   $scope.currentPage = n;
               }
           };
           $scope.CallServerFunction = function (newValue) {
               if($scope.CurrentStatus != '' )  {
                   $scope.busy = true;
                   $rootScope.$broadcast('preloader:active');
                   jQuery.ajax({
                       url: host+'administration/includes/ExceptionReason_submit.php',
                       dataType: 'json',
                       type: 'post',
                       data: 'ajax=GetExceptionReasonList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                       success: function(data) {
                           $scope.busy = false;
                           $rootScope.$broadcast('preloader:hide');
                           if(data.Success) {
                               $scope.pagedItems = data.Result;
                               if(data.total) {
                                   $scope.total = data.total;
                               }
                           } else {
                               $mdToast.show(
                                   $mdToast.simple()
                                       .content(data.Result)
                                       .position('right')
                                       .hideDelay(3000)
                               );
                           }
                           initSessionTime(); $scope.$apply();
                       }, error : function (data) {
                           $scope.busy = false;
                           $rootScope.$broadcast('preloader:hide');
                           alert(data.Result);
                           $scope.error = data;
                           initSessionTime(); $scope.$apply();
                       }
                   });
               }
           };
           $scope.$watch("currentPage", function(newValue, oldValue) {
               $scope.CallServerFunction(newValue);
           });
           $scope.convertSingle = function (multiarray) {
               var result = {};
               for(var i=0;i<multiarray.length;i++) {
                   result[i] = multiarray[i];
               }
               //alert(result);
               return result;
           };
           $scope.MakeOrderBy = function (orderby) {
               $scope.OrderBy = orderby;
               if($scope.OrderByType == 'asc') {
                   $scope.OrderByType = 'desc';
               } else {
                   $scope.OrderByType = 'asc';
               }
               $scope.busy = true;
               $rootScope.$broadcast('preloader:active');

               jQuery.ajax({
                   url: host+'administration/includes/ExceptionReason_submit.php',
                   dataType: 'json',
                   type: 'post',
                   data: 'ajax=GetExceptionReasonList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                   success: function(data) {
                       $scope.busy = false;
                       $rootScope.$broadcast('preloader:hide');
                       if(data.Success) {
                           $scope.pagedItems = data.Result;
                           if(data.total) {
                               $scope.total = data.total;
                           }
                       } else {
                           $mdToast.show(
                               $mdToast.simple()
                                   .content(data.Result)
                                   .position('right')
                                   .hideDelay(3000)
                           );
                       }
                       initSessionTime(); $scope.$apply();
                   }, error : function (data) {
                       $scope.busy = false;
                       $rootScope.$broadcast('preloader:hide');
                       $scope.error = data;
                       initSessionTime(); $scope.$apply();
                   }
               });
           };
           $scope.MakeFilter = function () {
               if($scope.currentPage == 0) {
                   $scope.CallServerFunction($scope.currentPage);
               } else {
                   $scope.currentPage = 0;
               }
           };
            $scope.ExceptionReasonListxls = function () {
           //alert("1");
           jQuery.ajax({
               url: host+'administration/includes/ExceptionReason_submit.php',
               dataType: 'json',
               type: 'post',
               data: 'ajax=GenerateExceptionReasonListxls&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),

               success: function(data) {
                   if(data.Success) {
                       //alert("2");
                       //console.log(data.Result);
                       window.location="templates/ExceptionReasonListxls.php";
                   } else {
                      // alert("4");
                       $mdToast.show (
                           $mdToast.simple()
                           .content(data.Result)
                           .action('OK')
                           .position('right')
                           .hideDelay(0)
                           .toastClass('md-toast-danger md-block')
                       );
                   }
                   initSessionTime(); $scope.$apply();
               }, error : function (data) {
                   //alert(data.Result);
                   //alert("3");
                   $scope.error = data;
                   initSessionTime(); $scope.$apply();
               }
           });
       };
           //End Pagination Logic
           $scope.UpdateDefault = function (id,defaultval,item) {
             console.log('item = '+JSON.stringify(item));
               if(item.Status == '1') {
                   jQuery.ajax({
                       url: host+'administration/includes/ExceptionReason_submit.php',
                       dataType: 'json',
                       type: 'post',
                       data: 'ajax=UpdateDefaultExceptionReason&'+$.param(item),
                       success: function(data){
                           if(data.Success == true) {
                               location.reload();
                           } else {
                               $mdToast.show(
                                   $mdToast.simple()
                                       .content(data.Result)
                                       .action('OK')
                                       .position('right')
                                       .hideDelay(0)
                                       .toastClass('md-toast-danger md-block')
                               );
                           }
                           initSessionTime(); $scope.$apply();
                       }, error : function (data) {
                           $scope.data = data;
                           initSessionTime(); $scope.$apply();
                       }
                   });
               } else {
                   $mdToast.show (
                       $mdToast.simple()
                           .content('Status should be active to make changes for Default Value')
                           .action('OK')
                           .position('right')
                           .hideDelay(0)
                           .toastClass('md-toast-danger md-block')
                   );
                   if(item.Default == 1) {
                       item.Default = 0
                   } else {
                       item.Default = 1
                   }
               }
           };
       });
})();
