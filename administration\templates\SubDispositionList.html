<div ng-controller = "SubDispositionList" class="page">

    <div ng-show="loading" class="loading" style="text-align:center;"><img src="../images/loading2.gif" /> LOADING...</div>

    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="DispositionList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="DispositionList = !DispositionList" class="material-icons md-primary" ng-show="DispositionList">keyboard_arrow_up</i>
                                <i ng-click="DispositionList = !DispositionList" class="material-icons md-primary" ng-show="! DispositionList">keyboard_arrow_down</i>
                                <span ng-click="DispositionList = !DispositionList">Sub Disposition List</span>
                                <div flex></div> 
                                <a href="#!/SubDisposition" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Sub Disposition
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="DispositionList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div> 
                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                        <div>                               
                                                            Sub Disposition<i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>                                 
                                                            <span ng-show="OrderBy == 'disposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('parentdispositionname')" ng-class="{'orderby' : OrderBy == 'parentdispositionname'}"> -->
                                                    <th >
                                                        <div>                               
                                                            Parent Disposition<!-- <i class="fa fa-sort pull-right" ng-show="OrderBy != 'parentdispositionname'"></i>-->
                                                            <span ng-show="OrderBy == 'parentdispositionname'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition_description')" ng-class="{'orderby' : OrderBy == 'disposition_description'}">                           
                                                        <div>                               
                                                            Disposition Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition_description'"></i>                                    
                                                            <span ng-show="OrderBy == 'disposition_description'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('status')" ng-class="{'orderby' : OrderBy == 'status'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'status'"></i>                                  
                                                            <span ng-show="OrderBy == 'status'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('color')" ng-class="{'orderby' : OrderBy == 'color'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Color <i class="fa fa-sort pull-right" ng-show="OrderBy != 'color'"></i>                                  
                                                            <span ng-show="OrderBy == 'color'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('WasteClassificationType')" ng-class="{'orderby' : OrderBy == 'WasteClassificationType'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Classification Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'WasteClassificationType'"></i>                                  
                                                            <span ng-show="OrderBy == 'WasteClassificationType'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <!-- <md-input-container class="md-block mt-0">
                                                            <input type="text" name="parentdispositionname" ng-model="filter_text[0].parentdispositionname" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container> -->
                                                    </td>                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition_description" ng-model="filter_text[0].disposition_description" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                   
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="status" ng-model="filter_text[0].status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="color" ng-model="filter_text[0].color" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="WasteClassificationType" ng-model="filter_text[0].WasteClassificationType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/SubDisposition/{{product.disposition_id}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a>
                                                    </td>
                                                    <td>
                                                        {{product.disposition}}                            
                                                    </td>
                                                    <td>
                                                        {{product.parentdispositionname}}
                                                    </td>                                                                           
                                                    <td>
                                                        {{product.disposition_description}}
                                                    </td>
                                                   
                                                    <td>
                                                        <span ng-if="product.status == 'Active'">Active</span>
                                                        <span ng-if="product.status == 'Inactive'">Inactive</span>
                                                    </td>    
                                                    
                                                    <td>
                                                        <span style="background-color: {{product.color_code}};">{{product.color}}</span>
                                                    </td>

                                                    <td>
                                                        {{product.WasteClassificationType}}
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="8">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>