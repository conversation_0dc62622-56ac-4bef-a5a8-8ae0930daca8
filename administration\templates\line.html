
<div class="row page" data-ng-controller="NewLine">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Line</span>
                        <div flex></div>
                            <a href="#!/NewLine" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">add</i> Create New Line
                            </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Line Name</label>
                                    <input type="text" name="LineName"  ng-model="Line['LineName']"  required ng-maxlength="50" />
                                    <div ng-messages="material_signup_form.LineName.$error" multiple ng-if='material_signup_form.LineName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Line Description</label>
                                    <input type="text" name="LineDescription"  ng-model="Line['LineDescription']"  required ng-maxlength="250" />
                                    <div ng-messages="material_signup_form.LineDescription.$error" multiple ng-if='material_signup_form.LineDescription.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>

                                </md-input-container>
                            </div>
                           
                            <div class="col-md-4">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="Line.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="0"> In active </md-option>
                                    </md-select>   
                                    <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/LineList" style="text-decoration: none;">
                                <md-button class="md-button md-raised btn-w-md  md-default">
                                    Cancel
                                </md-button>
                            </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || Line.busy" ng-click="ManageLines()">
                                <span ng-show="! Line.busy">Save</span>
                                <span ng-show="Line.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article> 

        <div class="panel panel-info" ng-if="Line.LineID">
            <div class="panel-heading">{{convert('Station Information')}}</div>             
            <div class="panel-body">
                <div class="form-group ng-scope">                                       
                    <div class="col-sm-2">                                                      
                        <input type="text" class="form-control" ng-model="StationName" name="StationName" placeholder="Station Name" required maxlength="50">
                    </div> 
                    <div class="col-sm-2">
                        <input type="button" value="Add" class="btn btn-primary" ng-disabled="!StationName" ng-click="AddStationToLine(StationName)">
                    </div>
                    <div class="col-md-12">
                        <div class="col-md-6 col-md-offset-3">
                            <table class="table" ng-show="Stations.length > 0">
                                <thead>
                                    <tr>
                                        <th>Station Name</th>
                                        <th>Delete</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="station in Stations">
                                        <td>{{station.StationName}}</td>
                                        <td>
                                            <span style="cursor: pointer;color:red;" ng-click="DeleteLineStation($index,station.LineStationID)">Delete</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>                                                                                          
                    </div>                  
                </div> 
            </div>
        </div>                  
               
    </div>
</div>