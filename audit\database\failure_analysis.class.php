<?php
session_start();
include_once("audit.class.php");
class FailureAnalysisClass extends AuditClass {
    
    public function GetCustomPalletDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}

			//Start Delete all locked source bins for logged in user for this workflow
			$query6 = "delete from source_bin_user_mapping where CreatedBy = '".$_SESSION['user']['UserId']."' and workflow_id = '2'";
			$q6 = mysqli_query($this->connectionlink,$query6);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Delete all locked source bins for logged in user for this workflow


			$query = "select c.*,s.Status from custompallet c 
			left join custompallet_status s on c.StatusID = s.StatusID 
			where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					//$json['Result'] = 'BIN Status is not Active';
					$json['Result'] = 'BIN Status is '.$row['Status'];
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				//Start check IF BIN is locked for Source BIN
				$query4 = "select count(*) from source_bin_user_mapping where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					if($row4['count(*)'] > 0) {
						$json['Success'] = false;
						$json['Result'] = 'BIN is locked as Source BIN';
						return json_encode($json);
					}
				}
				//End check IF BIN is locked for Source BIN

				//Start check IF Bin Disposition is mapped for the workflow or not
				$query1 = "select count(*) from workflow_disposition where disposition_id = '".$row['disposition_id']."' and workflow_id = '2'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['count(*)'] == 0) {
						$json['Success'] = false;
						$json['Result'] = 'BIN Disposition not mapped to Workflow';
						return json_encode($json);
					}
				}
				//End check IF Bin Disposition is mapped for the workflow or not

				//Start check If Source and any of the Destinations Bins are Same
				//if($data['SiteID'] > 0) {
				if(true) {
					//$query2 = "select m.*,d.disposition from station_custompallet_mapping m left join disposition d on m.disposition_id = d.disposition_id 
					//where m.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' ";

					$query2 = "select m.*,s.SiteName from station_custompallet_mapping m left join site s on m.SiteID = s.SiteID 
					where m.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' ";

					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row2 = mysqli_fetch_assoc($q2);
						$json['Success'] = false;
						$json['Result'] = 'BIN is defined as Output BIN for Station '.$row2['SiteName'];
						return json_encode($json);
					}
				}
				//End check If Source and any of the Destinations Bins are Same

				//Start Insert into Source Locking
				// $query3 = "insert into source_bin_user_mapping (CustomPalletID,workflow_id,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."',2,NOW(),'".$_SESSION['user']['UserId']."')";
				// $q3 = mysqli_query($this->connectionlink,$query3);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				//End Insert into Source Locking

				$json['Success'] = true;
				$json['CustomPalletID'] = $row['CustomPalletID'];
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetMPNFromSerialFA ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}

			//$query = "select a.* from asset a where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and a.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' ";
			$query = "select a.*,d.disposition from asset a 
			left join disposition d on a.disposition_id = d.disposition_id 
			 where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and a.FacilityID = '".$_SESSION['user']['FacilityID']."' order by a.StatusID";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);


				//Start check IF Bin Disposition is mapped for the workflow or not
				$query1 = "select count(*) from workflow_disposition where disposition_id = '".$row['disposition_id']."' and workflow_id = '2'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['count(*)'] == 0) {
						$json['Success'] = false;
						$json['Result'] = 'Serial Disposition ('.$row['disposition'].') not mapped to Workflow';
						return json_encode($json);
					}
				}
				//End check IF Bin Disposition is mapped for the workflow or not



				// if($row['CustomPalletID'] != $data['CustomPalletID']) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = "Serial Number not available in the Selected BIN";
				// 	return json_encode($json);
				// }
				if($row['StatusID'] != '1' && $row['StatusID'] != '9') {
					$json['Success'] = false;
					$json['Result'] = "Serial Number Status is not Active";
					return json_encode($json);
				}
				$json['Success'] = true;
				$json['Result'] = $row;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Serial Number details not available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function UpdateAssetFailureAnalysis ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);	
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Failure Analysis Page';
				return json_encode($json);
			}

			//Start check for Rig Limit exceeds
			$query22 = "select RigLimit from Rig where RigID = '".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."' ";
			$q22 = mysqli_query($this->connectionlink,$query22);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row22 = mysqli_fetch_assoc($q22);
				if($row22['RigLimit'] < count($data['Records'])) {
					$json['Success'] = false;
					$json['Result'] = 'Exceeds Rig Limit (Rig Limit is'.$row22['RigLimit'].')';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Rig';
				return json_encode($json);
			}
			//End check for Rig Limit exceeds


			//Start Validate all assets
			for($i=0;$i<count($data['Records']);$i++) {

				if($data['Records'][$i]['parttypeid'] == '') {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Part Type for Serial '.$data['Records'][$i]['SerialNumber'];
					return json_encode($json);
				}

				if($data['Records'][$i]['COOID'] == '') {
					$json['Success'] = false;
					$json['Result'] = 'Invalid COO Type for Serial '.$data['Records'][$i]['SerialNumber'];
					return json_encode($json);
				}
			
				//Start validate MPN
				$mpn_validate= $this->ValidateMPN($data['Records'][$i]['UniversalModelNumber']);
				if($mpn_validate['Success']) {              
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid MPN ('.$data['Records'][$i]['UniversalModelNumber'].') for Serial '.$data['Records'][$i]['SerialNumber'];
					return json_encode($json);
				}
				//End validate MPN
				


				//Start get asset Details
				// $query20 = "select a.* from asset a where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."'";
				// $q20 = mysqli_query($this->connectionlink,$query20);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$old = mysqli_fetch_assoc($q20);
				// 	if($old['StatusID'] != 1 && $old['StatusID'] != '9') {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Asset Status is not Active for Serial '.$data['Records'][$i]['SerialNumber'];
				// 		return json_encode($json);
				// 	}
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Invalid Serial '.$data['Records'][$i]['SerialNumber'];
				// 	return json_encode($json);
				// }
				//End get asset Details


				//Start validate Custom pallet
				$query = "Select MaximumAssets,AssetsCount,StatusID,BinName from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['CustomPalletID'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['StatusID'] != '1') {
						$json['Success'] = false;
						$json['Result'] = 'BIN Status is not Active for Serial '.$data['Records'][$i]['SerialNumber'];
						return json_encode($json);	
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid BIN '.$data['Records'][$i]['SerialNumber'];
					return json_encode($json);
				}			
				//End validate Custom pallet



				//Start validate From Custom pallet
				// $query20 = "Select MaximumAssets,AssetsCount,StatusID,BinName from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
				// $q20 = mysqli_query($this->connectionlink,$query20);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$FromCP = mysqli_fetch_assoc($q20);
				// 	if($FromCP['StatusID'] != '1') {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Source BIN Status is not Active';
				// 		return json_encode($json);	
				// 	}
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Invalid Source BIN';
				// 	return json_encode($json);
				// }			
				//End validate From Custom pallet	
			}
			//End Validate all assets

			
			//Start inserting all records
			$event_id = rand(1000000000, 9999999999);
			if(count($data['Records']) == 1) {
				$batch_event_flag = 'N';
			} else {
				$batch_event_flag = 'Y';
			}
			for($i=0;$i<count($data['Records']);$i++) {


				//Start get Part Type from parttypeid
				$query1 = "select parttype from parttype where parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['parttypeid'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);

					$PartType = $row1['parttype'];										
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Part Type';
					return json_encode($json);
				}

				$MPNPartTypeQ = "select part_type from catlog_creation where mpn_id='".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['UniversalModelNumber'])."' and lower(part_type)=lower('".mysqli_real_escape_string($this->connectionlink,$PartType)."') and FacilityID = '".$_SESSION['user']['FacilityID']."'";
				$MPNPartTypeQEx = mysqli_query($this->connectionlink,$MPNPartTypeQ);
				if(mysqli_affected_rows($this->connectionlink)==0){
					$json = array(
						'Success' => false,
						'Result' => "The combination of MPN and Part Type doesn't exist for the Serial ".$data['Records'][$i]['SerialNumber']
					);
					return json_encode($json);
				}

				//Start get asset Details
				$query20 = "select a.*,cp.BinName from asset a 
				left join custompallet cp on a.CustomPalletID = cp.CustomPalletID 
				where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."' order by  a.StatusID";
				$q20 = mysqli_query($this->connectionlink,$query20);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$old = mysqli_fetch_assoc($q20);
					if($old['StatusID'] != 1 && $old['StatusID'] != '9') {
						$json['Success'] = false;
						$json['Result'] = 'Asset Status is not Active for Serial '.$data['Records'][$i]['SerialNumber'];
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Serial '.$data['Records'][$i]['SerialNumber'];
					return json_encode($json);
				}
				//End get asset Details


				//Start validate Custom pallet
				$query = "Select MaximumAssets,AssetsCount,StatusID,BinName from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['CustomPalletID'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['StatusID'] != '1') {
						$json['Success'] = false;
						$json['Result'] = 'BIN Status is not Active for Serial '.$data['Records'][$i]['SerialNumber'];
						return json_encode($json);	
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid BIN '.$data['Records'][$i]['SerialNumber'];
					return json_encode($json);
				}			
				//End validate Custom pallet

				if(!$data['Records'][$i]['result_scan_time'] || $data['Records'][$i]['result_scan_time'] == '') {
					$data['Records'][$i]['result_scan_time'] = $data['Records'][$i]['rig_scan_time'];
				}

				$query1 = "insert into asset_failure_analysis (AssetScanID,SerialNumber,UniversalModelNumber,FromCustomPalletID,FromBinName,ToCustomPalletID,ToBinName,fa_input_id,RigID,fa_custom_id,fa_notes,fa_rule_id,disposition_id,CreatedDate,CreatedBy,SiteID,workstation_scan_time,rig_scan_time,serial_scan_time,mpn_scan_time,result_scan_time,bin_scan_time,event_id,batch_event_flag,parttypeid,COOID,part_type_scan_time,coo_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$old['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$old['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['fa_input_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['fa_custom_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['fa_notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['fa_rule_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$data['workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['rig_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['result_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['bin_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$event_id)."','".mysqli_real_escape_string($this->connectionlink,$batch_event_flag)."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['parttypeid'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['COOID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['part_type_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['coo_scan_time'])."')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$id = mysqli_insert_id($this->connectionlink);



				//Start update Asset
				$query1 = "update asset set UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['UniversalModelNumber'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['disposition_id'])."',CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentWorkflowID = '2',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."',part_type = '".mysqli_real_escape_string($this->connectionlink,$PartType)."',parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['parttypeid'])."',COOID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['COOID'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				//End update Asset

				if($old['FirstFailureAnalysisCustomPalletID'] > 0) {

				} else {//First time Failure Analysis screen
					$query102 = "update asset set FirstFailureAnalysisCustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['CustomPalletID'])."',FirstFailureAnalysisDateTime = NOW(),FirstFailureAnalysisBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."'";
					$q102 = mysqli_query($this->connectionlink,$query102);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				}

				//Start update Custom Pallet Items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['CustomPalletID'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Items

				//Start update Custom Pallet Counts
				$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['CustomPalletID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$old['CustomPalletID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				//Insert into Asset Tracking			
				// $query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."','Asset Processed in Failure Analysis Screen','','',NOW(),'".$_SESSION['user']['UserId']."')";
				// $q2 = mysqli_query($this->connectionlink,$query2);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }

				$desc = "Asset processed in Failure Analysis screen and moved to BIN, (BIN ID : ".$row['BinName'].")";

				if($data['Records'][$i]['fa_rule_id'] > 0) {
					//Write code for getting rule_id_text from business_rule table and add the rule_id_text to the description
					$query101 = "select rule_id_text from business_rule where rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['fa_rule_id'])."'";
					$q101 = mysqli_query($this->connectionlink,$query101);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row101 = mysqli_fetch_assoc($q101);
						$desc = $desc . " (Business Rule ID : ".$row101['rule_id_text'].")";
					}
				}
				
				$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Inserting into Asset Tracking

				//Start check If MPN is changed
				if($data['Records'][$i]['UniversalModelNumber'] != $old['UniversalModelNumber']) {
					$desc = "Asset MPN Changed from '".$old['UniversalModelNumber']."' to '".$data['Records'][$i]['UniversalModelNumber']."' in Failure Analysis Screen";
					$query4 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
					$q4 = mysqli_query($this->connectionlink,$query4);

					//Start get APN from MPN
					//$query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
					$query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
					$q191 = mysqli_query($this->connectionlink,$query191);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row191 = mysqli_fetch_assoc($q191);
						$APN = $row191['apn_id'];
						$PART_TYPE = $row191['part_type'];
						$ID_MANU = $row191['idManufacturer'];
					} else {
						$APN = '';
						$PART_TYPE = '';
						$ID_MANU = '';
					}
					$query192 = "update asset set apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."',part_type = '".mysqli_real_escape_string($this->connectionlink,$PART_TYPE)."',idManufacturer = '".mysqli_real_escape_string($this->connectionlink,$ID_MANU)."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."'";
					$q192 = mysqli_query($this->connectionlink,$query192);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//End get APN from MPN

				}			
				//End check IF MPN is changed


				//Start check If Disposition is changed
				if($data['Records'][$i]['disposition_id'] != $old['disposition_id']) {
					$desc = "Asset Disposition Changed in Failure Analysis Screen";
					if($data['Records'][$i]['fa_rule_id'] > 0) {
						//Write code for getting rule_id_text from business_rule table and add the rule_id_text to the description
						$query101 = "select rule_id_text from business_rule where rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['fa_rule_id'])."'";
						$q101 = mysqli_query($this->connectionlink,$query101);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row101 = mysqli_fetch_assoc($q101);
							$desc = $desc . " (Business Rule ID : ".$row101['rule_id_text'].")";
						}
					}
					$query5 = "insert into asset_tracking (AssetScanID,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."','".$desc."','','".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','disposition','disposition_id','disposition')";
					$q5 = mysqli_query($this->connectionlink,$query5);

					$query5 = "update asset set RecentDispositionDate = NOW(),RecentDispositionRuleID = '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['fa_rule_id'])."',RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Updated in Failure Analysis page' where AssetScanID =  '".mysqli_real_escape_string($this->connectionlink,$data['Records'][$i]['AssetScanID'])."' ";
					$q5 = mysqli_query($this->connectionlink,$query5);

				}			
				//End check IF Disposition is changed



			}
			//End inserting all records



			//Start Close Container
			if($data['CloseBin'] == '1') {

				$close_bin = $this->EmptyCustomPallet($data['FromCustomPalletID'],$data['AllDispositionBINName']);
				if($close_bin['Success']) {
					$json['BinClosed'] = '1';
					$json['CloseMessage'] = $close_bin['Error'];
					//Start Admin Tracking				
					$query13 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."','BIN Emptied in Failure Analysis Screen',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','','','','')";
					$q13 = mysqli_query($this->connectionlink,$query13);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//End Admin Trackin

				} else {
					$json['CloseMessage'] = $close_bin['Error'];
				}				
			}
			//End close BIN


			$json['Success'] = true;
			$json['Result'] = 'Success';
			return json_encode($json);






























			//Start validate MPN
			$mpn_validate= $this->ValidateMPN($data['UniversalModelNumber']);
			if($mpn_validate['Success']) {              
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid MPN';
				return json_encode($json);
			}
			//End validate MPN

			//Start get asset Details
			$query20 = "select * from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$old = mysqli_fetch_assoc($q20);
				if($old['StatusID'] != 1 && $old['StatusID'] != '9') {
					$json['Success'] = false;
					$json['Result'] = 'Asset Status is not Active';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Serial';
				return json_encode($json);
			}
			//End get asset Details



			//Start validate Custom pallet
			$query = "Select MaximumAssets,AssetsCount,StatusID,BinName from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);	
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid BIN';
				return json_encode($json);
			}			
			//End validate Custom pallet


			//Start validate From Custom pallet
			$query20 = "Select MaximumAssets,AssetsCount,StatusID,BinName from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$FromCP = mysqli_fetch_assoc($q20);
				if($FromCP['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'Source BIN Status is not Active';
					return json_encode($json);	
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Source BIN';
				return json_encode($json);
			}			
			//End validate From Custom pallet


			if($data['failure_analysis_id']) { // Update existins

			} else {
				$query1 = "insert into asset_failure_analysis (AssetScanID,SerialNumber,UniversalModelNumber,FromCustomPalletID,FromBinName,ToCustomPalletID,ToBinName,fa_input_id,RigID,fa_custom_id,fa_notes,fa_rule_id,disposition_id,CreatedDate,CreatedBy,SiteID) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$FromCP['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['fa_input_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['RigID'])."','".mysqli_real_escape_string($this->connectionlink,$data['fa_custom_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['fa_notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['fa_rule_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$id = mysqli_insert_id($this->connectionlink);
			}
			
			//Start update Asset
			$query1 = "update asset set UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentWorkflowID = '2',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

			if($old['FirstFailureAnalysisCustomPalletID'] > 0) {

			} else {//First time Failure Analysis screen
				$query102 = "update asset set FirstFailureAnalysisCustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',FirstFailureAnalysisDateTime = NOW(),FirstFailureAnalysisBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				$q102 = mysqli_query($this->connectionlink,$query102);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}

			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
			$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$old['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts

			//Insert into Asset Tracking			
			$query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','Asset Processed in Failure Analysis Screen','','',NOW(),'".$_SESSION['user']['UserId']."')";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$desc = "Asset Moved to BIN, (BIN ID : ".$row['BinName'].")";
			$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking

			//Start check If MPN is changed
			if($data['UniversalModelNumber'] != $old['UniversalModelNumber']) {
				$desc = "Asset MPN Changed from '".$old['UniversalModelNumber']."' to '".$data['UniversalModelNumber']."' in Failure Analysis Screen";
				$query4 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q4 = mysqli_query($this->connectionlink,$query4);

				//Start get APN from MPN
				//$query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
				$query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
				$q191 = mysqli_query($this->connectionlink,$query191);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row191 = mysqli_fetch_assoc($q191);
					$APN = $row191['apn_id'];
					$PART_TYPE = $row191['part_type'];
					$ID_MANU = $row191['idManufacturer'];
				} else {
					$APN = '';
					$PART_TYPE = '';
					$ID_MANU = '';
				}
				$query192 = "update asset set apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."',part_type = '".mysqli_real_escape_string($this->connectionlink,$PART_TYPE)."',idManufacturer = '".mysqli_real_escape_string($this->connectionlink,$ID_MANU)."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				$q192 = mysqli_query($this->connectionlink,$query192);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End get APN from MPN

			}			
			//End check IF MPN is changed


			//Start check If Disposition is changed
			if($data['disposition_id'] != $old['disposition_id']) {
				$desc = "Asset Disposition Changed in Failure Analysis Screen";
				$query5 = "insert into asset_tracking (AssetScanID,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".$desc."','','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','disposition','disposition_id','disposition')";
				$q5 = mysqli_query($this->connectionlink,$query5);

				$query5 = "update asset set RecentDispositionDate = NOW(),RecentDispositionRuleID = '".mysqli_real_escape_string($this->connectionlink,$data['fa_rule_id'])."',RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Updated in Failure Analysis page' where AssetScanID =  '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
				$q5 = mysqli_query($this->connectionlink,$query5);

			}			
			//End check IF Disposition is changed

			//Start Close Container
			if($data['CloseBin'] == '1') {

				$close_bin = $this->EmptyCustomPallet($data['FromCustomPalletID'],$data['AllDispositionBINName']);
				if($close_bin['Success']) {
					$json['BinClosed'] = '1';
					$json['CloseMessage'] = $close_bin['Error'];
					//Start Admin Tracking				
					$query13 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."','BIN Emptied in Failure Analysis Screen',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','','','','')";
					$q13 = mysqli_query($this->connectionlink,$query13);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//End Admin Trackin

				} else {
					$json['CloseMessage'] = $close_bin['Error'];
				}

				// $query11 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
				// $q11 = mysqli_query($this->connectionlink,$query11);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$row11 = mysqli_fetch_assoc($q11);
				// 	if($row11['count(*)'] == 0) {//Custom Pallet can be closed
				// 		$query12 = "update custompallet set StatusID = '3',LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
				// 		$q12 = mysqli_query($this->connectionlink,$query12);
				// 		if(mysqli_error($this->connectionlink)) {
				// 			$json['Success'] = false;
				// 			$json['Result'] = mysqli_error($this->connectionlink);
				// 			return json_encode($json);
				// 		}

				// 		//Start Admin Tracking				
				// 		$query13 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."','BIN closed in Failure Analysis Screen',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','','','','')";
				// 		$q13 = mysqli_query($this->connectionlink,$query13);
				// 		if(mysqli_error($this->connectionlink)) {
				// 			$json['Success'] = false;
				// 			$json['Result'] = mysqli_error($this->connectionlink);
				// 			return json_encode($json);
				// 		}
				// 		//End Admin Tracking

				// 	} else { //Items still exists, can't close
				// 		$json['CloseMessage'] = $row11['count(*)'].' Serials exists in the BIN, Not able to close BIN';
				// 	}
				// }
			}
			//End close BIN

			//Start get Asset Details			
			if($id > 0) {
				$query10 = "select a.*,d.disposition,i.input,i.input_type,cp.CustomPalletID,cp.BinName,cp.AssetsCount,r.Rigname from asset_failure_analysis a 
				left join custompallet cp on a.ToCustomPalletID = cp.CustomPalletID 
				left join disposition d on a.disposition_id = d.disposition_id  
				left join workflow_input i on a.fa_input_id = i.input_id 
                left join Rig r on a.RigID = r.RigID 
				where a.failure_analysis_id = '".mysqli_real_escape_string($this->connectionlink,$id)."'";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row10 = mysqli_fetch_assoc($q10);
					$json['Asset'] = $row10;
				}
			}			
			//End get Asset Details


			$json['Success'] = true;
			$json['Result'] = 'Success';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetFAAssets ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}
			// $query = "select a.*,d.disposition,i.input,i.input_type,r.Rigname from asset_failure_analysis a 
			// left join disposition d on a.disposition_id = d.disposition_id  
			// left join workflow_input i on a.fa_input_id = i.input_id 
            // left join Rig r on a.RigID = r.RigID 
			// where a.FromCustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";


			$query = "select a.*,d.disposition,i.input,i.input_type,r.Rigname,pt.parttype,co.COO from asset_failure_analysis a 
			left join disposition d on a.disposition_id = d.disposition_id  
			left join workflow_input i on a.fa_input_id = i.input_id 
            left join Rig r on a.RigID = r.RigID 
			left join parttype pt on a.parttypeid = pt.parttypeid 
			left join COO co on a.COOID = co.COOID 
			where a.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and a.CreatedBy = '".$_SESSION['user']['UserId']."' and  DATE(CreatedDate) = CURDATE()";

			/*$json['Success'] = false;
			$json['Result'] = $query;
			return json_encode($json);
*/
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {

						if($key == 'FromBinName') {
							$query = $query . " AND a.FromBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SerialNumber') {
							$query = $query . " AND a.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'UniversalModelNumber') {
							$query = $query . " AND a.UniversalModelNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'input') {
							$query = $query . " AND i.input like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'Rigname') {
							$query = $query . " AND r.Rigname like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ToBinName') {
							$query = $query . " AND a.ToBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'parttype') {
							$query = $query . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'COO') {
							$query = $query . " AND co.COO like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
									
					}
				}
			}
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				  if($data['OrderBy'] == 'FromBinName') {
					$query = $query . " order by a.FromBinName ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'SerialNumber') {
					$query = $query . " order by a.SerialNumber ".$order_by_type." ";
				}  
				else if($data['OrderBy'] == 'UniversalModelNumber') {
					$query = $query . " order by a.UniversalModelNumber ".$order_by_type." ";
				} 
				else if($data['OrderBy'] == 'input') {
					$query = $query . " order by i.input ".$order_by_type." ";
				}
				elseif($data['OrderBy'] == 'Rigname') {
					$query = $query . " order by r.Rigname ".$order_by_type." ";
				} 
				else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'ToBinName') {
					$query = $query . " order by a.ToBinName ".$order_by_type." ";
				}

				else if($data['OrderBy'] == 'parttype') {
					$query = $query . " order by pt.parttype ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'COO') {
					$query = $query . " order by co.COO ".$order_by_type." ";
				}
				
			} else {
				$query = $query . " order by CreatedDate desc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));				
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				//$assets = array();
				while($row = mysqli_fetch_assoc($q)) {
					$assets[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $assets;
				//return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Assets Available';
				
			}

			if($data['skip'] == 0) {
				$query1 = "select count(*) from asset_failure_analysis a 
				left join disposition d on a.disposition_id = d.disposition_id  
				left join workflow_input i on a.fa_input_id = i.input_id 
				left join Rig r on a.RigID = r.RigID 
				left join parttype pt on a.parttypeid = pt.parttypeid 
				left join COO co on a.COOID = co.COOID 
				where a.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and a.CreatedBy = '".$_SESSION['user']['UserId']."' and  DATE(CreatedDate) = CURDATE()";

				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							
							if($key == 'FromBinName') {
								$query1 = $query1 . " AND a.FromBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SerialNumber') {
								$query1 = $query1 . " AND a.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'UniversalModelNumber') {
								$query1 = $query1 . " AND a.UniversalModelNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'input') {
								$query1 = $query1 . " AND i.input like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'Rigname') {
								$query1 = $query1 . " AND r.Rigname like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'disposition') {
								$query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ToBinName') {
								$query1 = $query1 . " AND a.ToBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'parttype') {
								$query1 = $query1 . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'COO') {
								$query1 = $query1 . " AND co.COO like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function GetStationRigs ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['Workflow'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['Workflow'].' Page';
				return json_encode($json);
			}

            $query = "select * from Rig where Status = '1' and SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' order by Rigname";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$stations = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$stations[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $stations;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Test Rigs Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	// Actions Functions for Bin Management

	public function CreateBin($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}

			// Check if bin name already exists
			$query = "SELECT CustomPalletID FROM custompallet WHERE BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$json['Success'] = false;
				$json['Result'] = 'Bin name already exists. Please choose a different name.';
				return json_encode($json);
			}

			// Create new bin
			$query = "INSERT INTO custompallet (BinName, idPackage, FacilityID, LocationType, LocationGroup, disposition, Notes, StatusID, CreatedDate, CreatedBy)
					  VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."',
							  '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',
							  '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."',
							  '".mysqli_real_escape_string($this->connectionlink,$data['LocationType'])."',
							  '".mysqli_real_escape_string($this->connectionlink,$data['LocationGroup'])."',
							  '".mysqli_real_escape_string($this->connectionlink,$data['Disposition'])."',
							  '".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."',
							  '1', NOW(), '".$_SESSION['user']['UserId']."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$newBinID = mysqli_insert_id($this->connectionlink);

			// Add tracking record
			$trackingAction = "Bin created in Failure Analysis module";
			$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy)
							  VALUES ('$newBinID', '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."',
									  '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."')";
			mysqli_query($this->connectionlink, $trackingQuery);

			$json['Success'] = true;
			$json['Result'] = 'Bin created successfully: ' . $data['BinName'];
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function CloseBin($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}

			// Validate TPVR fields
			if(empty($data['AuditController']) || empty($data['Password']) || empty($data['NewSealID']) || empty($data['BinWeight'])) {
				$json['Success'] = false;
				$json['Result'] = 'All TPVR fields are required (Controller, Password, Seal ID, Weight).';
				return json_encode($json);
			}

			// Update bin status to closed with TPVR information
			$query = "UPDATE custompallet SET StatusID = '3', SealID = '".mysqli_real_escape_string($this->connectionlink,$data['NewSealID'])."',
					  Weight = '".mysqli_real_escape_string($this->connectionlink,$data['BinWeight'])."',
					  DateUpdated = NOW(), UpdatedBy = '".$_SESSION['user']['UserId']."'
					  WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Add tracking record with TPVR details
			$trackingAction = "Bin closed in Failure Analysis module. Controller: " . $data['AuditController'] .
							  ", Seal ID: " . $data['NewSealID'] . ", Weight: " . $data['BinWeight'];

			$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy)
							  VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',
									  '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."',
									  '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."')";
			mysqli_query($this->connectionlink, $trackingQuery);

			$json['Success'] = true;
			$json['Result'] = 'Bin closed successfully: ' . $data['BinName'];
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function NestToBin($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}

			// Update bin to nest under parent
			$query = "UPDATE custompallet SET ParentCustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['parentBin'])."',
					  DateUpdated = NOW(), UpdatedBy = '".$_SESSION['user']['UserId']."'
					  WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Get parent bin name for tracking
			$parentQuery = "SELECT BinName FROM custompallet WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['parentBin'])."'";
			$parentResult = mysqli_query($this->connectionlink, $parentQuery);
			$parentBinName = 'Unknown';
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$parentRow = mysqli_fetch_assoc($parentResult);
				$parentBinName = $parentRow['BinName'];
			}

			// Add tracking record
			$trackingAction = "Bin nested under parent bin: $parentBinName in Failure Analysis module";
			$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy)
							  VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',
									  '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."',
									  '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."')";
			mysqli_query($this->connectionlink, $trackingQuery);

			$json['Success'] = true;
			$json['Result'] = 'Bin nested successfully under: ' . $parentBinName;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAvailableParentBins($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}

			// Get available parent bins (active bins that are not the current bin and not already nested)
			$query = "SELECT CustomPalletID, BinName FROM custompallet
					  WHERE StatusID = '1'
					  AND FacilityID = '".$_SESSION['user']['FacilityID']."'
					  AND CustomPalletID != '".mysqli_real_escape_string($this->connectionlink,$data['CurrentBinID'])."'
					  AND ParentCustomPalletID IS NULL
					  ORDER BY BinName";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$bins = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$bins[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $bins;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No available parent bins found";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetSessionFacility($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}

			// Get facility information from session
			$json['Success'] = true;
			$json['FacilityID'] = $_SESSION['user']['FacilityID'];
			$json['FacilityName'] = $_SESSION['user']['FacilityName'];
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetBinPackageTypes($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Failure Analysis')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Failure Analysis Page';
				return json_encode($json);
			}

			// Get package types for bin creation
			$query = "SELECT idPackage, packageName FROM package WHERE Active = '1' AND FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' ORDER BY packageName";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$packages = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$packages[$i] = $row;
					$i = $i + 1;
				}
				$json['Success'] = true;
				$json['Result'] = $packages;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No package types found";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

}
?>