
<div class="row page" data-ng-controller="ProductClass">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">
                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Product Class Details</span>
                        <div flex></div>
                        <a href="#!/ProductClass" class="md-button md-raised btn-w-md" style="display: flex;">
                            <i class="material-icons">add</i> Create New Class
                        </a>
                    </div>
                </md-toolbar>
                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Class Name</label>
                                    <input type="text" name="ProductClassName"  ng-model="ProductClass['ProductClassName']"  required ng-maxlength="45" />
                                    <div ng-messages="material_signup_form.ProductClassName.$error" multiple ng-if='material_signup_form.ProductClassName.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 45.</div>                           
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="ProductClassDesc"  ng-model="ProductClass['ProductClassDesc']"  required ng-maxlength="250" />
                                    <div ng-messages="material_signup_form.ProductClassDesc.$error" multiple ng-if='material_signup_form.ProductClassDesc.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 250.</div>                           
                                    </div>

                                </md-input-container>
                            </div>
                            
                            <div class="col-md-4">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="ProductClassStatus" ng-model="ProductClass.ProductClassStatus" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="0"> In active </md-option>
                                    </md-select>   
                                    <div ng-messages="material_signup_form.ProductClassStatus.$error" multiple ng-if='material_signup_form.ProductClassStatus.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div>                                             
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/ProductClassList" style="text-decoration: none;">
                                <md-button 
                                class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                            </a>                            
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="Classcreation()">
                                <span ng-show="! ProductClass.busy">Save</span>
                                <span ng-show="ProductClass.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>
                        </form>
                    </div>
                </div>        

            </md-card>

            <md-card class="no-margin-h">
                    
                <md-toolbar class="md-table-toolbar md-default"  ng-init="attributesPanel = true;" ng-show="ProductClass.ProductClassID">
                    <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="ReceivePanel = !ReceivePanel">                            
                        <i class="material-icons" ng-click="attributesPanel = !attributesPanel" ng-show="attributesPanel">keyboard_arrow_up</i>
                        <i class="material-icons" ng-click="attributesPanel = !attributesPanel" ng-show="! attributesPanel">keyboard_arrow_down</i>
                        <span>Attributes</span>
                    </div>
                </md-toolbar>
                <div class="row" ng-show="attributesPanel">
                    <form>                            
                        <div class="col-md-12">
                            <div class="col-md-4 col-sm-8 col-xs-8" >
                                <md-input-container class="md-block">                                            
                                    <label>Add New Attribute</label>
                                    <md-select name="attribute" ng-model="attribute" aria-label="select">
                                        <md-option value="{{attribute.AttributeID}}" ng-repeat="attribute in AllAttributes"> {{attribute.AttributeName}} </md-option> 
                                    </md-select>                                
                                </md-input-container>
                            </div>
                            <div class="col-md-4 col-sm-4 col-xs-4">
                                <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px;" ng-disabled="!attribute" ng-click="AddAttribute(attribute)">
                                    Add
                                    <!--<i class="material-icons">add</i> Add-->
                                </button>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-12">
                                    <table md-table md-row-select  ng-show="attributes.length > 0">
                                        <thead md-head>
                                            <tr md-row>
                                                <th md-column>Attribute</th>
                                                <th md-column style="padding-left: 5px;">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody md-body>
                                            <tr md-row ng-repeat="attr in attributes">
                                                <td md-cell>{{attr.AttributeName}}</td>
                                                <td md-cell>
                                                    <span class="text-danger actionlinkicon" ng-click="DeleteAttribute(attr,$event)"><i class="material-icons">delete_forever</i> Delete  </span>                                  
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>  
                            </div>
                        </div>
                    </form>
                </div>

            </md-card>

        </article>
    </div>
</div>

                      