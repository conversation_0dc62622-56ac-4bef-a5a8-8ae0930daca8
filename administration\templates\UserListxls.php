<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['UserListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "UserList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('User List');
$header = array('Account Created DateTime','User Name','First Name','Last Name','Facility','Profile','Program','Manager','Shift','Accout Type','Status');

//$sql = "select u.*,pt.ProfileName,ss.StatusName from users u,profile_type pt,statusses ss where u.AccountID='".$_SESSION['user']['AccountID']."' and u.ProfileID = pt.ProfileID AND u.Status = ss.StatusID";
$sql = "select u.*,pt.ProfileName,ss.StatusName,f.FacilityName,p.ProgramName,ptm.UserName as MangerName from users u 
left join profile_type pt on u.ProfileID = pt.ProfileID 
left join statusses ss on u.Status = ss.StatusID 
left join facility f on u.FacilityID = f.FacilityID
left join Program p on u.Program = p.ProgramID
left join users ptm on u.Manager = ptm.UserId
where u.AccountID='".$_SESSION['user']['AccountID']."' ";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                    if ($key == 'UserName') {
                        $query = $query . " AND u.UserName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'FirstName') {
                        $query = $query . " AND u.FirstName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'LastName') {
                        $query = $query . " AND u.LastName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'Email') {
                        $query = $query . " AND u.Email like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'ProfileName') {
                        $query = $query . " AND pt.ProfileName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'ProgramName') {
                        $query = $query . " AND p.ProgramName like '%" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'StatusName') {
                        $query = $query . " AND ss.StatusName like '" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'FacilityName') {
                        $query = $query . " AND f.FacilityName like '" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'MangerName') {
                        $query = $query . " AND (u.OtherManager like '" . mysqli_real_escape_string($connectionlink1, $value) . "%' OR ptm.UserName like '" . mysqli_real_escape_string($connectionlink1, $value) . "%')  ";
                    }
                    if ($key == 'Shift') {
                        $query = $query . " AND u.Shift like '" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                    if ($key == 'AccountType') {
                        $query = $query . " AND u.AccountType like '" . mysqli_real_escape_string($connectionlink1, $value) . "%' ";
                    }
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

            if ($data['OrderBy'] == 'UserName') {
                $query = $query . " order by u.UserName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'FirstName') {
                $query = $query . " order by u.FirstName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'LastName') {
                $query = $query . " order by u.LastName " . $order_by_type . " ";
            } elseif ($data['OrderBy'] == 'Email') {
                $query = $query . " order by u.Email " . $order_by_type . " ";
            } elseif ($data['OrderBy'] == 'ProfileName') {
                $query = $query . " order by pt.ProfileName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'ProgramName') {
                $query = $query . " order by p.ProgramName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'StatusName') {
                $query = $query . " order by ss.StatusName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'FacilityName') {
                $query = $query . " order by f.FacilityName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'UserName') {
                $query = $query . " order by ptm.UserName " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'Shift') {
                $query = $query . " order by u.Shift " . $order_by_type . " ";
            } else if ($data['OrderBy'] == 'AccountType') {
                $query = $query . " order by u.AccountType " . $order_by_type . " ";
            }
        } else {
            $sql = $sql . " order by u.UserName desc ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
while($row = mysqli_fetch_assoc($query))
{
    if($row['Manager'] == '0')
    {
        $row['MangerName'] = $row['OtherManager'];
    }
    if($row['StatusUpdateDate'] == '')
    {
        $row['StatusUpdateDate'] = $row['DateCreated'];
    }
    $row2  = array($row['StatusUpdateDate'],$row['UserName'],$row['FirstName'],$row['LastName'],$row['FacilityName'],$row['ProfileName'],$row['ProgramName'],$row['MangerName'],$row['Shift'],$row['AccountType'],$row['StatusName']);
    $rows[] = $row2;
}

$sheet_name = 'User List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 