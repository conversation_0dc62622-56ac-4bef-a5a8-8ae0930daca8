<div class="row page" data-ng-controller="Module">
    <div class="col-md-12">
        <article class="article">
            <form name="material_signup_form" class="form-validation" >
                <md-card class="no-margin-h">
                    
                    <md-toolbar class="md-table-toolbar md-default">
                        <div class="md-toolbar-tools">
                            <span>Module</span>
                            <div flex></div>
                            <a href="#!/Module" class="md-button md-raised btn-w-md" style="display: flex;">
                                New Module
                            </a>
                        </div>
                    </md-toolbar>
                                    
                    <div class="col-md-4 col-md-offset-4">
                        <fieldset>
                            <md-input-container class="md-block">                                            
                                <label>Jabil Site</label>
                                <md-select name="CustomerID" ng-model="benefit.CustomerID" required aria-label="select" ng-disabled="benefit.ModuleID" ng-change="GetCustomerApplications()">
                                    <md-option ng-repeat="cus in Customer" value="{{cus.CustomerID}}"> {{cus.CustomerName}} </md-option>
                                </md-select>   
                                <div ng-messages="material_signup_form.CustomerID.$error" multiple ng-if='material_signup_form.CustomerID.$dirty'>
                                    <div ng-message="required">This is required.</div>                                           
                                </div>                                             
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Application</label>
                                <md-select name="ApplicationID" ng-model="benefit.ApplicationID" required aria-label="select" ng-disabled="benefit.ModuleID">
                                    <md-option ng-repeat="app in Applications" value="{{app.ApplicationID}}"> {{app.ApplicationName}} </md-option>
                                </md-select>
                                <div ng-messages="material_signup_form.ApplicationID.$error" multiple ng-if='material_signup_form.ApplicationID.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Module Name</label>
                                <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                <input type="text" name="ModuleName"  ng-model="benefit['ModuleName']"  required ng-maxlength="100" />
                                <div ng-messages="material_signup_form.ModuleName.$error" multiple ng-if='material_signup_form.ModuleName.$dirty'>                            
                                    <div ng-message="required">This is required.</div> 
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 100.</div>                           
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">
                                <label>Description</label>
                                <!-- <md-icon class="material-icons">perm_identity</md-icon> -->                                        
                                <textarea name="ModuleDescription" ng-minlength="3"  ng-model="benefit['ModuleDescription']"  required ng-maxlength="1000" ></textarea>
                                
                                <div ng-messages="material_signup_form.ModuleDescription.$error" multiple ng-if='material_signup_form.ModuleDescription.$dirty'>
                                    <div ng-message="required">This is required.</div>
                                    
                                    <div ng-message="minlength">Min length 3.</div>
                                    <div ng-message="maxlength">Max length 1000.</div>
                                </div>
                            </md-input-container>

                            <md-input-container class="md-block">                                            
                                <label>Status</label>
                                <md-select name="Status" ng-model="benefit.Status" required aria-label="select">
                                    <md-option value="Active"> Active </md-option>
                                    <md-option value="In active"> In active </md-option>
                                </md-select>   
                                <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                    <div ng-message="required">This is required.</div>                                           
                                </div>                                             
                            </md-input-container>
                                                        
                            <div class="col-md-12 btns-row">
                                <md-button 
                                class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || benefit.busy" ng-click="CreateModule('Submit')">
                                    <span ng-show="! benefit.busy">Submit</span>
                                    <span ng-show="benefit.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                </md-button>
                            </div>
                            
                        </fieldset>
                    </div>
                </md-card>                
            </form>            
        </article>                            
    </div>

</div>