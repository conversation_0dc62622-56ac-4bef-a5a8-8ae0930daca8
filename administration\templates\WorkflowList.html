<div class="row page  page-invoice" data-ng-controller="WorkflowList" >  
    <div class="col-md-12">
        <article class="article">
            
            <div class="row ui-section">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-body" style="padding-top:0px;">
                    
                            <md-toolbar class="md-table-toolbar md-default" ng-hide="options.rowSelection && selected.length">
                                <div class="md-toolbar-tools">
                                    <span>Workflow List</span>
                                    <div flex></div>
                                    <a href="#!/Workflow" class="md-button md-raised btn-w-md" style="display: flex;">
                                        <i class="material-icons">add</i> Create New Workflow
                                    </a>
                                </div>
                            </md-toolbar>
                            
                            <md-toolbar class="md-table-toolbar alternate" ng-show="options.rowSelection && selected.length">
                                <div class="md-toolbar-tools">
                                    <span>{{selected.length}} {{selected.length > 1 ? 'items' : 'item'}} selected</span>
                                </div>
                            </md-toolbar>
                            
                            <md-table-container>
                                <table md-table md-row-select="options.rowSelection" >
                                    <thead ng-if="!options.decapitate" md-head md-order="query.order" md-on-reorder="logOrder">
                                        <tr md-row>
                                            <th md-column><span>Edit</span></th> 
                                            <th md-column md-order-by="workflow"><span>workflow Name</span></th>
                                            <th md-column md-order-by="workflow_description"><span>Workflow Description</span></th>
                                            <th md-column md-order-by="status"><span>Status</span></th>                                
                                        </tr>
                                    </thead>
                                    <tbody md-body>
                                        <tr md-row ng-repeat="dessert in desserts.data | filter: filter.search | orderBy: query.order | limitTo: query.limit : (query.page -1) * query.limit">
                                            <td md-cell>
                                                <a href="#!/Workflow/{{dessert.workflow_id}}"><md-icon class="material-icons text-danger">edit</md-icon></a>
                                            </td>
                                            <td md-cell>{{dessert.workflow}}</td>                                
                                            <!-- <td md-cell>{{dessert.calories.value}}</td> -->
                                            <td md-cell>{{dessert.workflow_description}}</td>
                                            <td md-cell>
                                                <span ng-show="dessert.status == 'Active'">Active</span>
                                                <span ng-show="dessert.status == 'Inactive'">Inactive</span>
                                            </td>                               
                                        </tr>
                                    </tbody>
                                </table>
                            </md-table-container>

                            <md-table-pagination md-limit="query.limit" md-limit-options="limitOptions" md-page="query.page" md-total="{{desserts.count}}" md-page-select="options.pageSelect" md-boundary-links="options.boundaryLinks" md-on-paginate="logPagination"></md-table-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </article>                            
    </div>
</div>