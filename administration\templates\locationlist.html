<div ng-controller = "LocationList" class="page">

    <div ng-show="loading" class="loading" style="text-align:center;"><img src="../images/loading2.gif" /> LOADING...</div>

    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="LocationList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">

                                 <i ng-click="LocationList = !LocationList" class="material-icons md-primary" ng-show="LocationList">keyboard_arrow_up</i>
                                <i ng-click="LocationList = !LocationList" class="material-icons md-primary" ng-show="! LocationList">keyboard_arrow_down</i>
                                <span ng-click="LocationList = !LocationList">Location List</span>

                                <div flex></div> 
                                <a href="#!/LocationList" ng-click="LocationListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>

                                <a href="#!/LocationList" ng-click="LocationListxls()" class="md-button md-raised md-default dis_open_v mr-5" style="display:none; min-width: 40px;">
                                    <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon>
                                 </a>

                                <a href="#!/location" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Location
                                </a>

                                <a href="#!/location" class="md-button md-raised btn-w-md md-default dis_open2"  style="display:none;">
                                    <i class="material-icons">add</i> Create New
                                </a>

                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="LocationList">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div> 
                                    
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                        
                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LocationName')" ng-class="{'orderby' : OrderBy == 'LocationName'}">
                                                        <div>                               
                                                            Location Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationName'"></i>                                 
                                                            <span ng-show="OrderBy == 'LocationName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                                                                                                            
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LocationDesc')" ng-class="{'orderby' : OrderBy == 'LocationDesc'}">                           
                                                        <div>                               
                                                            Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationDesc'"></i>                                    
                                                            <span ng-show="OrderBy == 'LocationDesc'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                          
                                                        <div>                               
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                  
                                                            <span ng-show="OrderBy == 'FacilityName'">                                  
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LocationType')" ng-class="{'orderby' : OrderBy == 'LocationType'}">                          
                                                        <div>                               
                                                            Location Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationType'"></i>                                    
                                                            <span ng-show="OrderBy == 'LocationType'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CurrentItem')" ng-class="{'orderby' : OrderBy == 'CurrentItem'}">                           
                                                        <div>                               
                                                            Current Item <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CurrentItem'"></i>                                    
                                                            <span ng-show="OrderBy == 'CurrentItem'">                                   
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LockType')" ng-class="{'orderby' : OrderBy == 'LockType'}">
                                                        <div style="min-width: 80px;">                               
                                                            Locked <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LockType'"></i>                                  
                                                            <span ng-show="OrderBy == 'LockType'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>                                  
                                                            <span ng-show="OrderBy == 'StatusName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationName" ng-model="filter_text[0].LocationName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationDesc" ng-model="filter_text[0].LocationDesc" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationType" ng-model="filter_text[0].LocationType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CurrentItem" ng-model="filter_text[0].CurrentItem" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LockType" ng-model="filter_text[0].LockType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                                  
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>                       
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/location/{{product.LocationID}}"><md-icon class="material-icons text-danger">edit</md-icon></a></td>

                                                    <td>
                                                        {{product.LocationName}}                            
                                                    </td>                       
                                                    <td>
                                                        {{product.LocationDesc}}
                                                    </td>
                                                    <td>
                                                        {{product.FacilityName}}
                                                    </td>
                                                    <td>
                                                        {{product.LocationType}}
                                                    </td>
                                                    <td>
                                                     <span ng-show="product.currentItemType != '' && product.currentItemType != NULL">{{product.currentItemType}} <strong style="font-weight: bold;">({{product.currentItemID}}) </strong> </span>
                                                     </td>
                                                    <td>
                                                        {{product.LockType}}
                                                    </td>
                                                    <td>
                                                        {{product.StatusName}}
                                                    </td>  

                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="8">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>