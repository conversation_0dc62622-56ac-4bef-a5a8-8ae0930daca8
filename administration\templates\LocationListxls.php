<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['LocationListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "LocationList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Location List');
$header = array('Location Name','Description','Facility','Location Type','Current Item','Locked','Status');

//$sql = "select L.*,F.FacilityName from location L, facility F where F.FacilityID = L.FacilityID  AND F.AccountID = '".$_SESSION['user']['AccountID']."'";
$sql = "select L.*,F.FacilityName,ld.LockType,ss.StatusName from location L, facility F,statusses ss,locked_details ld where F.FacilityID = L.FacilityID  AND F.AccountID = '".$_SESSION['user']['AccountID']."' and L.Locked = ld.Locked AND L.LocationStatus = ss.StatusID";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                    if($key == 'LocationName') {
                        $sql = $sql . " AND L.LocationName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }                   
                    if($key == 'LocationDesc') {
                        $sql = $sql . " AND L.LocationDesc like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'FacilityName') {
                        $sql = $sql . " AND F.FacilityName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'LocationType') {
                        $sql = $sql . " AND L.LocationType like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }   
                    if($key == 'LocationStatus') {
                        $sql = $sql . " AND ss.StatusName like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'CurrentItem') {
                        $sql = $sql . " AND (L.currentItemType like '%".mysqli_real_escape_string($connectionlink1,$value)."%' or L.currentItemID like '%".mysqli_real_escape_string($connectionlink,$value)."%') "; 
                    }   
                    if($key == 'LockType') {
                        $sql = $sql . " AND ld.LockType like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }                    
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

            if($data['OrderBy'] == 'LocationName') {
                $sql = $sql . " order by L.LocationName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'LocationDesc') {
                $sql = $sql . " order by L.LocationDesc ".$order_by_type." ";
            } else if($data['OrderBy'] == 'FacilityName') {
                $sql = $sql . " order by L.FacilityName ".$order_by_type." ";
            }  elseif($data['OrderBy'] == 'LocationType') {
                $sql = $sql . " order by L.LocationType ".$order_by_type." ";
            } else if($data['OrderBy'] == 'LocationStatus') {
                $sql = $sql . " order by ss.StatusName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'CurrentItem') {
                $sql = $sql . " order by currentItemType ".$order_by_type." ";
            } else if($data['OrderBy'] == 'LockType') {
                $sql = $sql . " order by ld.LockType ".$order_by_type." ";
            }         
        } else {
            $sql = $sql . " order by L.LocationName desc ";
        }
$query = mysqli_query($connectionlink1,$sql);
            while($row = mysqli_fetch_assoc($query))
            {
                if($row['Locked'] == 1)
                {
                    $row['Locked'] = 'Locked';
                }
                else if($row['Locked'] == 2)
                {
                    $row['Locked'] = 'Unlocked';
                }

                if($row['LocationStatus'] == 1)
                {
                    $row['LocationStatus'] = 'Active';
                }
                else if($row['LocationStatus'] == 2)
                {
                    $row['LocationStatus'] = 'InActive';
                }
                if($row['currentItemType'] != '')
                {
                    $row['currentItemType'] = $row['currentItemType']."(".$row['currentItemID'].")";
                }
                else
                {
                    $row['currentItemType'] = '';
                }
                $row2  = array($row['LocationName'],$row['LocationDesc'],$row['FacilityName'],$row['LocationType'],$row['currentItemType'],$row['LockType'],$row['StatusName']);
                $rows[] = $row2;
            }

$sheet_name = 'Location List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 